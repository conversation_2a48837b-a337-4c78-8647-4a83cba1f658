target/
!.mvn/wrapper/maven-wrapper.jar
### Spring Boot DevTools ###
out/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
.ideaDataSources
dataSources
.run

### NetBeans ###
nbproject/private/
#build/
nbbuild/
dist/
nbdist/
.nb-gradle/

frontend/node_modules/*
src/main/resources/static/styles/main.*.css
src/main/resources/static/styles/assets/*
src/main/resources/static/styles/webpack-main.js
src/main/resources/static/webpack_assets.json
frontend/*

src/main/resources/static/css/main.js
src/main/resources/static/css/style_webpack.css
src/main/resources/static/js/all_pages_webpack.js
src/main/resources/static/js/main_page_webpack.js


**/application-local.yaml
**/application-testpropslocal.yaml


### Help scripts ###
*.cmd
*.sh

### Tmp files ###
*.tmp

### Mac OS index ###
**.DS_Store
*.icloud
### RR my tools
tools/**

### Javaagent tools
javaagent/**

tmp
log
*.log
.sdkmanrc

.vscode

.mvn
mvnw
mvnw.cmd

docker/postgres
/allure-results/
!/project-architecture-analysis.md
!/.local/
!/.gitignore
!/ci_settings.xml
