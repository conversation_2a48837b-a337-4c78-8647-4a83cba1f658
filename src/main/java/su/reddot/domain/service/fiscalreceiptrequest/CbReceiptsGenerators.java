package su.reddot.domain.service.fiscalreceiptrequest;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import su.reddot.domain.model.duty.OrderPositionDuty;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestType;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.infrastructure.bank.commons.BankCommons;
import su.reddot.infrastructure.cashregister.Checkable;
import su.reddot.infrastructure.cashregister.impl.starrys.type.BuyerCheckRequest;
import su.reddot.infrastructure.cashregister.impl.starrys.type.Line;
import su.reddot.infrastructure.cashregister.impl.starrys.type.ProviderData;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


@Service
@RequiredArgsConstructor
public class CbReceiptsGenerators {

    private final ReceiptsGenerators receiptsGenerators;

    private String getRequestFormat(FiscalReceiptRequestType fiscalReceiptRequestType) {
        switch (fiscalReceiptRequestType) {
            case CB_ADVANCE:
                return ReceiptsGenerators.REQUEST_ID_ADVANCE_FORMAT;
            case CB_ADVANCE_REFUND:
                return ReceiptsGenerators.REQUEST_ID_REFUNDS_FORMAT;
            case CB_ADVANCE_ADJUST:
                return ReceiptsGenerators.REQUEST_ID_ADJUSTS_FORMAT;
            case CB_PAYMENT:
                return ReceiptsGenerators.REQUEST_ID_PAYMENT_FORMAT;
        }
        throw new IllegalArgumentException("CbReceiptsGenerators: unable to find request format " + fiscalReceiptRequestType);
    }

    private short getReceiptType(FiscalReceiptRequestType fiscalReceiptRequestType) {
        switch (fiscalReceiptRequestType) {
            case CB_ADVANCE:
            case CB_PAYMENT:
                return BuyerCheckRequest.DOCUMENT_TYPE_SELL;
            case CB_ADVANCE_REFUND:
            case CB_ADVANCE_ADJUST:
                return BuyerCheckRequest.DOCUMENT_TYPE_SELL_REFUND;
        }
        throw new IllegalArgumentException("CbReceiptsGenerators: unable to find receipts type " + fiscalReceiptRequestType);
    }

    private BigDecimal getOrderPositionAmount(FiscalReceiptRequestType fiscalReceiptRequestType, OrderPosition orderPosition) {
        BigDecimal return2BuyerAmount = orderPosition.getHoldAmount()
                .subtract(ObjectUtils.defaultIfNull(orderPosition.getEffectiveAmount(), BigDecimal.ZERO));
        switch (fiscalReceiptRequestType) {
            case CB_ADVANCE:
            case CB_ADVANCE_REFUND:
                return orderPosition.getSellerPayoutAmountRaw();
            case CB_ADVANCE_ADJUST:
                return return2BuyerAmount;
            case CB_PAYMENT:
                return orderPosition.getSellerPayoutAmountRaw().subtract(return2BuyerAmount);
        }
        throw new IllegalArgumentException("CbReceiptsGenerators: unable to find request format " + fiscalReceiptRequestType);
    }

    private BigDecimal getDutiesAmount(OrderPosition orderPosition, OrderPositionDuty.Type dutyType) {
        return orderPosition.getDuties().stream()
                .filter(it -> it.getType() == dutyType)
                .map(OrderPositionDuty::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BuyerCheckRequest generateReceiptRequestCbCommon(Order order, FiscalReceiptRequestType fiscalReceiptRequestType) {
        List<Line> receiptLines = new ArrayList<>();
        for (OrderPosition orderPosition: order.getParticipatedInPaymentOrderPositions()) {
            long productAmount = BankCommons.toAmountInCents(getOrderPositionAmount(fiscalReceiptRequestType, orderPosition));
            Line productLine = Line.builder()
                    .qty(Checkable.Line.QUANTITY_MULTIPLIER)
                    .price(productAmount)
                    .payAttribute(fiscalReceiptRequestType == FiscalReceiptRequestType.CB_PAYMENT ? Checkable.Line.LINE_PAYMENT_FULL_PAYMENT : Checkable.Line.LINE_PAYMENT_ADVANCE)
                    .taxId(Checkable.Line.LINE_TAX_ID_VAT_NONE)
                    .description(ReceiptsGenerators.getReceiptItemName(orderPosition))
                    .lineAttribute(fiscalReceiptRequestType == FiscalReceiptRequestType.CB_PAYMENT ? Checkable.Line.LINE_ATTRIBUTE_COMMODITY : Checkable.Line.LINE_ATTRIBUTE_ADVANCE)
                    .build();
            productLine.setAgentModes(Checkable.Line.AGENT_MODE_AGENT);
            productLine.setProviderData(getCbProductAgentData());
            receiptLines.add(productLine);
            if (fiscalReceiptRequestType == FiscalReceiptRequestType.CB_ADVANCE_ADJUST) {
                break;
            }
            //
            long customsAmount = BankCommons.toAmountInCents(getDutiesAmount(orderPosition, OrderPositionDuty.Type.CUSTOM_DUTY));
            Line customsLine = Line.builder()
                    .qty(Checkable.Line.QUANTITY_MULTIPLIER)
                    .price(customsAmount)
                    .payAttribute(fiscalReceiptRequestType == FiscalReceiptRequestType.CB_PAYMENT ? Checkable.Line.LINE_PAYMENT_FULL_PAYMENT : Checkable.Line.LINE_PAYMENT_ADVANCE)
                    .taxId(Checkable.Line.LINE_TAX_ID_VAT_NONE)
                    .description(rcptItemNameCustoms())
                    .lineAttribute(fiscalReceiptRequestType == FiscalReceiptRequestType.CB_PAYMENT ? Checkable.Line.LINE_ATTRIBUTE_SERVICE : Checkable.Line.LINE_ATTRIBUTE_ADVANCE)
                    .build();
            customsLine.setAgentModes(Checkable.Line.AGENT_MODE_AGENT);
            customsLine.setProviderData(getCbCustomAgentData());
            if (customsAmount > 0) {
                receiptLines.add(customsLine);
            }
            //
            long serviceAmount = BankCommons.toAmountInCents(order.getDeliveryCost().add(orderPosition.getMarketplaceCommissionAmountRaw()));
            Line serviceLine = Line.builder()
                    .qty(Checkable.Line.QUANTITY_MULTIPLIER)
                    .price(serviceAmount)
                    .payAttribute(fiscalReceiptRequestType == FiscalReceiptRequestType.CB_PAYMENT ? Checkable.Line.LINE_PAYMENT_FULL_PAYMENT : Checkable.Line.LINE_PAYMENT_ADVANCE)
                    .taxId(Checkable.Line.LINE_TAX_ID_VAT_NONE)
                    .description(rcptItemNameService())
                    .lineAttribute(fiscalReceiptRequestType == FiscalReceiptRequestType.CB_PAYMENT ? Checkable.Line.LINE_ATTRIBUTE_SERVICE : Checkable.Line.LINE_ATTRIBUTE_ADVANCE)
                    .build();
            receiptLines.add(serviceLine);
        }
        //
        Long receiptAmount = receiptLines.stream().map(Line::getPrice).reduce(0L, Long::sum);
        String requestId = receiptsGenerators.getRqIdPrefix() + String.format(getRequestFormat(fiscalReceiptRequestType), order.getRequestId());
        List<Long> paymentsList = (fiscalReceiptRequestType == FiscalReceiptRequestType.CB_PAYMENT)
                ? Arrays.asList(0L, 0L, 0L)
                : Arrays.asList(receiptAmount, 0L, 0L);
        BuyerCheckRequest receiptRequest = new BuyerCheckRequest(getReceiptType(fiscalReceiptRequestType), requestId, 0, "", paymentsList);
        receiptRequest.setAdvancePayment((fiscalReceiptRequestType == FiscalReceiptRequestType.CB_PAYMENT) ? receiptAmount : 0L);
        receiptRequest.setLines(receiptLines);
        return receiptRequest;
    }

    public BuyerCheckRequest generateReceiptRequestCbAdvance(Order order) {
        return generateReceiptRequestCbCommon(order, FiscalReceiptRequestType.CB_ADVANCE);
    }

    public BuyerCheckRequest generateReceiptRequestCbAdvanceRefund(Order order) {
        return generateReceiptRequestCbCommon(order, FiscalReceiptRequestType.CB_ADVANCE_REFUND);
    }

    public BuyerCheckRequest generateReceiptRequestCbAdvanceAdjust(Order order) {
        return generateReceiptRequestCbCommon(order, FiscalReceiptRequestType.CB_ADVANCE_ADJUST);
    }

    public BuyerCheckRequest generateReceiptCbPayment(Order order) {
        return generateReceiptRequestCbCommon(order, FiscalReceiptRequestType.CB_PAYMENT);
    }

    private String rcptItemNameCustoms() {
        return "Таможенные пошлины";
    }

    private String rcptItemNameService() {
        return "Комиссия агента";
    }

    private static ProviderData getCbProductAgentData() {
        ProviderData providerData = new ProviderData();
        providerData.setInn("**********");
        providerData.setName("ООО \"ТРИКСТИ ГЛОБАЛ ЛИМИТЕД\"");
        return providerData;
    }

    private static ProviderData getCbCustomAgentData() {
        ProviderData providerData = new ProviderData();
        providerData.setInn("**********");
        providerData.setName("ООО \"ГБС-БРОКЕР\"");
        return providerData;
    }

}
