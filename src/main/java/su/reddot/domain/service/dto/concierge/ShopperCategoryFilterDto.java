package su.reddot.domain.service.dto.concierge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "Информация о категории для фильтра шопера")
public class ShopperCategoryFilterDto {
    
    @Schema(description = "Идентификатор категории", example = "123")
    private Long id;
    
    @Schema(description = "Отображаемое название категории", example = "Платья")
    private String displayName;
    
    @Schema(description = "Единственное число названия категории", example = "Платье")
    private String singularName;
    
    @Schema(description = "Полное название категории", example = "Женские платья")
    private String fullName;
    
    @Schema(description = "Множественное число названия категории", example = "Платья")
    private String pluralName;
    
    @Schema(description = "URL-имя категории для ссылок", example = "platya")
    private String url;
    
    @Schema(description = "Альтернативное URL-имя категории", example = "dresses")
    private String alternativeUrl;
    
    @Schema(description = "Иконка категории")
    private String icon;
    
    @Schema(description = "Количество товаров в категории", example = "250")
    private Integer productsCount;
    
    @Schema(description = "Есть ли дочерние категории", example = "true")
    private Boolean hasChildren;
    
    @Schema(description = "Идентификатор родительской категории", example = "2")
    private Long parentId;
    
    @Schema(description = "Список дочерних категорий")
    private List<ShopperCategoryFilterDto> children;
    
    @Schema(description = "Тип размера по умолчанию", example = "EU")
    private String defaultSizeType;
}
