package su.reddot.domain.service.dto.primary;

/*
 * Created by <PERSON> on 16.03.2022
 */

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.api.client.util.Strings;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import su.reddot.domain.service.dto.primary.enums.PrimaryContentType;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PrimaryContentDTO<T> implements Serializable {
    private final static String DEFAULT_ID = "DEFAULT_ID";

    private final static String ID_SEPARATOR = ":";

    @ApiModelProperty(notes = "Заголовок")
    private String title;
    @ApiModelProperty(notes = "Путь к изображению")
    private String imagePath;
    @ApiModelProperty(notes = "Тип контента", required = true)
    private PrimaryContentType contentType;
    @ApiModelProperty(notes = "ID блока")
    private String id;
    @ApiModelProperty(notes = "Контенты")
    private List<T> contentList;
    @ApiModelProperty(notes = "Дополнительный Контент")
    private List<T> additionalContentList;
    @ApiModelProperty(notes = "Контент")
    private T content;
    @ApiModelProperty(notes = "Индекс для пагинации карусельки")
    private String nextAnchor;
    @ApiModelProperty(notes = "Дополнительные данные контента")
    private PrimaryContentAdditionalData additionalData;
    @ApiModelProperty(notes = "Абсолютный индекс блока в выдаче")
    private Long chapterIndex;
    @ApiModelProperty(notes = "Id сегмента, который был выбран")
    private Long segmentId;
    @ApiModelProperty(notes = "Время когда был обновлен источник данных из которого построился этот объект")
    private LocalDateTime updateTime;
    @ApiModelProperty(notes = "Журнальный вид")
    private Boolean isJournalMode;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class PrimaryContentAdditionalData {
        @ApiModelProperty(notes = "Количество контентов")
        private Long count;
        @ApiModelProperty(notes = "Путь изображений к дополнительным контентам")
        private List<String> imgPathList;
        @ApiModelProperty(notes = "Путь перехода к показу всех контентов")
        private String jumpLink;
        @ApiModelProperty(notes = "Заголовок кнопки для перехода по jumpLink")
        private String buttonTitle;
        private String description;
        private Boolean isSellerHidden;
        @ApiModelProperty(notes = "Интервал переключения слайдов")
        private Long textSlideInterval;
    }


    @JsonIgnore
    private String originalId;

    public PrimaryContentDTO<T> setId(String id) {
        this.id = id;
        this.originalId = id;
        return this;
    }

    @JsonIgnore
    public String getIdOrDefault() {
        return Strings.isNullOrEmpty(originalId) ? DEFAULT_ID : originalId;
    }

    @JsonIgnore
    public String getTitleOrDefault() {
        return Strings.isNullOrEmpty(title) ? contentType.name() : title;
    }

    public PrimaryContentDTO<T> setIdLikeIdAndTitle() {
        id = getTitleOrDefault() + ID_SEPARATOR + getIdOrDefault();
        return this;
    }
}
