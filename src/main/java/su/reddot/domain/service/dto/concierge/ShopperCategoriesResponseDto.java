package su.reddot.domain.service.dto.concierge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "Ответ с категориями для шопера")
public class ShopperCategoriesResponseDto {
    
    @Schema(description = "Список доступных категорий")
    private List<ShopperCategoryFilterDto> categories;
    
    @Schema(description = "Общее количество категорий", example = "45")
    private Integer totalCount;
}
