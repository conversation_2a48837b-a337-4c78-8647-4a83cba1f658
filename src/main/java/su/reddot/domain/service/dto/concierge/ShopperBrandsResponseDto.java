package su.reddot.domain.service.dto.concierge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "Ответ с брендами для шопера")
public class ShopperBrandsResponseDto {
    
    @Schema(description = "Список доступных брендов")
    private List<ShopperBrandFilterDto> brands;
    
    @Schema(description = "Общее количество брендов", example = "150")
    private Integer totalCount;
}
