package su.reddot.domain.service.dto.concierge;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "Информация о бренде для фильтра шопера")
public class ShopperBrandFilterDto {
    
    @Schema(description = "Идентификатор бренда", example = "123")
    private Long id;
    
    @Schema(description = "Название бренда", example = "Gucci")
    private String name;
    
    @Schema(description = "URL-имя бренда для ссылок", example = "gucci")
    private String urlName;
    
    @Schema(description = "Транслитерированное название бренда", example = "gucci")
    private String transliterateName;
    
    @Schema(description = "Количество доступных товаров бренда", example = "150")
    private Integer productsCount;
    
    @Schema(description = "Скрыт ли бренд", example = "false")
    private Boolean isHidden;
    
    @Schema(description = "Заголовок бренда", example = "Gucci - итальянский дом моды")
    private String title;
    
    @Schema(description = "Описание скрытого бренда")
    private String hiddenDescription;
}
