package su.reddot.domain.service.dto.concierge;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Ответ с доступными фильтрами (брендами и категориями) для шопера")
public class ShopperAvailableFiltersResponseDto {

    @Schema(description = "Список доступных брендов")
    private List<ShopperBrandFilterDto> brands;

    @Schema(description = "Общее количество доступных брендов", example = "150")
    private Integer brandsCount;

    @Schema(description = "Список доступных категорий")
    private List<ShopperCategoryFilterDto> categories;

    @Schema(description = "Общее количество доступных категорий", example = "45")
    private Integer categoriesCount;

    @Schema(description = "Общее количество товаров, соответствующих текущим фильтрам", example = "1250")
    private Long totalProductsCount;

    @Schema(description = "Информация о примененных фильтрах")
    private AppliedFiltersInfo appliedFilters;

    @Schema(description = "Метаданные запроса")
    private FilterMetadata metadata;

    @Data
    @Accessors(chain = true)
    @Schema(description = "Информация о примененных фильтрах")
    public static class AppliedFiltersInfo {
        
        @Schema(description = "Количество примененных фильтров брендов", example = "2")
        private Integer appliedBrandsCount;
        
        @Schema(description = "Количество примененных фильтров категорий", example = "1")
        private Integer appliedCategoriesCount;
        
        @Schema(description = "Есть ли активные фильтры", example = "true")
        private Boolean hasActiveFilters;
    }

    @Data
    @Accessors(chain = true)
    @Schema(description = "Метаданные запроса фильтров")
    public static class FilterMetadata {
        
        @Schema(description = "Время выполнения запроса в миллисекундах", example = "125")
        private Long executionTimeMs;
        
        @Schema(description = "Версия API фильтров", example = "2.0")
        private String apiVersion;
        
        @Schema(description = "Источник данных", example = "CACHE")
        private String dataSource;
        
        @Schema(description = "Были ли применены ограничения по количеству результатов", example = "false")
        private Boolean hasLimitations;
    }
}
