package su.reddot.domain.service.concierge.oskellyconcierge;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.exception.UserNotFoundException;
import su.reddot.domain.exception.ValidationFailedException;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.concierge.api.ConciergePurchaseOrderControllerApi;
import su.reddot.domain.service.concierge.api.ConciergeStateHistoryControllerApi;
import su.reddot.domain.service.concierge.api.RejectionReasonControllerApi;
import su.reddot.domain.service.concierge.model.ConciergeOrderDetailsResponse;
import su.reddot.domain.service.concierge.model.CustomerInfoDTO;
import su.reddot.domain.service.concierge.model.OrderSources;
import su.reddot.domain.service.concierge.model.OrdersForConciergeDTO;
import su.reddot.domain.service.concierge.model.PaginatedResult;
import su.reddot.domain.service.concierge.model.PurchaseOrderCreateRequest;
import su.reddot.domain.service.concierge.model.PurchaseOrderFilter;
import su.reddot.domain.service.concierge.model.PurchaseOrderFullDTO;
import su.reddot.domain.service.concierge.model.PurchaseOrderStateHistoryDTO;
import su.reddot.domain.service.concierge.model.PurchaseOrderUpdateRequest;
import su.reddot.domain.service.concierge.model.RejectionEventRequest;
import su.reddot.domain.service.concierge.model.RejectionReason;
import su.reddot.domain.service.concierge.model.RequestsFilter;
import su.reddot.domain.service.concierge.model.Roles;
import su.reddot.domain.service.concierge.oskellyconcierge.enums.PurchaseOrderStatus;
import su.reddot.domain.service.concierge.oskellyconcierge.mappers.PurchaseOrderToAppMapper;
import su.reddot.domain.service.dto.concierge.PurchaseOrderMobileCreateRequest;
import su.reddot.domain.service.dto.concierge.PurchaseOrderMobileUpdateRequest;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.security.SecurityService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@ConditionalOnProperty(name = "concierge-service.enabled", havingValue = "true", matchIfMissing = false)
public class DefaultPurchaseOrderService extends ConciergeHTTPHandlerService implements PurchaseOrderService {
    private final ConciergePurchaseOrderControllerApi conciergePurchaseOrderControllerApi;
    private final ConciergeStateHistoryControllerApi conciergeStateHistoryControllerApi;
    private final RejectionReasonControllerApi rejectionReasonControllerApi;
    private final SecurityService securityService;
    private final UserService userService;
    private final UserRepository userRepository;
    private final EnrichmentService enrichmentService;
    private final EmployeeConciergeRoleExtractor employeeRoleExtractor;
    private final OfferService offerService;
    private final PurchaseOrderToAppMapper purchaseOrderToAppMapper;

    public DefaultPurchaseOrderService(
        MicrometerService micrometerService,
        ConciergePurchaseOrderControllerApi conciergePurchaseOrderControllerApi,
        ConciergeStateHistoryControllerApi conciergeStateHistoryControllerApi,
        RejectionReasonControllerApi rejectionReasonControllerApi,
        SecurityService securityService,
        UserService userService,
        UserRepository userRepository,
        EnrichmentService enrichmentService,
        EmployeeConciergeRoleExtractor employeeRoleExtractor,
        OfferService offerService,
        PurchaseOrderToAppMapper purchaseOrderToAppMapper
    ) {
        super(micrometerService);
        this.conciergePurchaseOrderControllerApi = conciergePurchaseOrderControllerApi;
        this.conciergeStateHistoryControllerApi = conciergeStateHistoryControllerApi;
        this.rejectionReasonControllerApi = rejectionReasonControllerApi;
        this.securityService = securityService;
        this.userService = userService;
        this.userRepository = userRepository;
        this.enrichmentService = enrichmentService;
        this.employeeRoleExtractor = employeeRoleExtractor;
        this.offerService = offerService;
        this.purchaseOrderToAppMapper = purchaseOrderToAppMapper;
    }

    // Это администраторская ручка
    // customer = получаем id в запросе
    // sales = авторизовавшийся пользователь.
    @Override
    public PurchaseOrderFullDTO createPurchaseOrder(PurchaseOrderCreateRequest request) {
        checkRequest(request);

        checkExistUser(request.getCustomerInfo().getCustomerId());

        Long authorizedUserId = securityService.getCurrentAuthorizedUserId();

        //SalesId всегда устанавливаем пользователя, создателя заявки
        request.setSalesInfo(enrichmentService.getSalesInfo(authorizedUserId));
        request.setCustomerInfo(enrichmentService.getCustomerInfo(request.getCustomerInfo().getCustomerId()));

        return executeHttpCall(
            "ConciergePurchaseOrderControllerApi.createPurchaseOrder",
            () -> conciergePurchaseOrderControllerApi.createPurchaseOrder(request, authorizedUserId)
        );
    }

    private void checkRequest(PurchaseOrderCreateRequest request) {
        boolean hasDescription = request.getDescription() != null && !request.getDescription().trim().isEmpty();

        request.setImagesUrl(filterOutEmptyImagesUrls(request.getImagesUrl()));
        boolean hasImages = !request.getImagesUrl().isEmpty();

        if (!hasDescription && !hasImages) {
            throw new ValidationFailedException("Заявка должна содержать описание или изображения", "description or images", "Обязательное поле");
        }

        Optional.of(request)
                .map(PurchaseOrderCreateRequest::getCustomerInfo)
                .map(CustomerInfoDTO::getCustomerId)
                .orElseThrow(() -> new ValidationFailedException(
                        "Не удалось получить CustomerId: request, customerInfo или customerId равен null",
                        "userId",
                        "Обязательное поле"
                ));
    }

    private List<String> filterOutEmptyImagesUrls(List<String> images) {
        if (images == null || images.isEmpty()) return new ArrayList<>();

        return images.stream().filter(url -> !StringUtils.isEmpty(url)).collect(Collectors.toList());
    }

    // Это пользовательская ручка
    // Можем получить и отдать только customer = авторизовавшийся пользователь.
    @Override
    public PurchaseOrderFullDTO createPurchaseOrder(PurchaseOrderMobileCreateRequest request) {
        User authorizedUser = securityService.getCurrentAuthorizedUser();
        Long currentAuthorizedUserId = authorizedUser.getId();

        PurchaseOrderCreateRequest createRequest = new PurchaseOrderCreateRequest();
        createRequest.setSource(PurchaseOrderCreateRequest.SourceEnum.SALES_APP);
        createRequest.setDescription(request.getDescription());
        createRequest.setImagesUrl(request.getImagesUrl());
        createRequest.setLink(request.getLink());

        CustomerInfoDTO customerInfoDTO = new CustomerInfoDTO();
        customerInfoDTO.setCustomerId(currentAuthorizedUserId);
        createRequest.setCustomerInfo(enrichmentService.getCustomerInfo(customerInfoDTO));

        createRequest.setSalesInfo(null);
        createRequest.setSourcerInfo(null);
        createRequest.setPurchaseToNew(request.getPurchaseToNew());

        checkRequest(createRequest);

        return executeHttpCall("ConciergePurchaseOrderControllerApi.createPurchaseOrder",
            () -> conciergePurchaseOrderControllerApi.createPurchaseOrder(
                createRequest, currentAuthorizedUserId));
    }

    @Override
    public PurchaseOrderFullDTO getByOrderId(long orderId) {
        Long userId = securityService.getCurrentAuthorizedUserId();
        Roles role = employeeRoleExtractor.extract(userId);
        PurchaseOrderFullDTO purchaseOrder =  executeHttpCall("ConciergePurchaseOrderControllerApi.getPurchaseOrder",
                () -> conciergePurchaseOrderControllerApi.getPurchaseOrder(orderId, role, null));
        return offerService.enrichOffersForPurchaseOrder(purchaseOrder);
    }

    @Override
    public PurchaseOrderFullDTO getPurchaseOrderForUser(long orderId) {
        Long userId = securityService.getCurrentAuthorizedUserId();
        PurchaseOrderFullDTO purchaseOrder = executeHttpCall("ConciergePurchaseOrderControllerApi.getPurchaseOrder",
                () -> conciergePurchaseOrderControllerApi.getPurchaseOrder(orderId, Roles.CUSTOMER, userId));
        return offerService.enrichOffersForPurchaseOrder(purchaseOrder);
    }

    @Override
    public List<PurchaseOrderStateHistoryDTO> getPurchaseOrderHistory(Long orderId) {
        List<PurchaseOrderStateHistoryDTO> stateTransitionHistory = executeHttpCall("ConciergeStateHistoryControllerApi.getStateTransitionHistory",
                () -> conciergeStateHistoryControllerApi.getStateTransitionHistory(orderId));
        stateTransitionHistory.forEach(purchaseOrderStateHistoryDTO -> {
            if (purchaseOrderStateHistoryDTO.getUserId() != null) {
                purchaseOrderStateHistoryDTO.setUserNickName(userRepository.findById(purchaseOrderStateHistoryDTO.getUserId())
                        .map(User::getNickname).orElse("-"));
            }
        });
        return stateTransitionHistory;
    }

    @Override
    public PurchaseOrderFullDTO eventSend(Long orderId, String eventCode) {
        Long userId = securityService.getCurrentAuthorizedUserId();
        Roles role = employeeRoleExtractor.extract(userId);
        return executeHttpCall("ConciergeStateHistoryControllerApi.eventSend",
                () -> conciergePurchaseOrderControllerApi.changeStateOperation(orderId, eventCode, userId, role));
    }

    @Override
    public PurchaseOrderFullDTO eventSendForUser(Long orderId, String eventCode) {
        Long userId = securityService.getCurrentAuthorizedUserId();
        return executeHttpCall("ConciergeStateHistoryControllerApi.eventSendForUser",
                () -> conciergePurchaseOrderControllerApi.changeStateOperation(orderId, eventCode, userId, null));
    }

    @Override
    public PurchaseOrderFullDTO createRejectionEvent(RejectionEventRequest request, Long orderId) {
        Long userId = securityService.getCurrentAuthorizedUserId();
        return executeHttpCall("ConciergePurchaseOrderControllerApi.createRejectionEvent",
                () -> conciergePurchaseOrderControllerApi.createRejectionEvent(userId, orderId, request));
    }

    @Override
    public List<RejectionReason> getRejectionReasons(RejectionReason.ObjectTypeEnum objectType, RejectionReason.OrderStatusEnum status) {
        String statusValue = status != null ? status.getValue() : null;
        Long userId = securityService.getCurrentAuthorizedUserId();
        return executeHttpCall("ConciergePurchaseOrderControllerApi.getRejectionReasons",
                () -> rejectionReasonControllerApi.getRejectionReasons(userId, objectType.getValue(), statusValue));
    }

    @Override
    public PurchaseOrderFilter getFilter(PurchaseOrderFilter filter) {
        Long userId = securityService.getCurrentAuthorizedUserId();

        Roles role = employeeRoleExtractor.extract(userId);

        return executeHttpCall("ConciergePurchaseOrderControllerApi.getFilter",
                () -> conciergePurchaseOrderControllerApi.getFilterPurchaseOrders(userId, role, filter));
    }

    @Override
    public PaginatedResult getPurchaseOrders(RequestsFilter filter, String searchText) {
        Long userId = securityService.getCurrentAuthorizedUserId();
        Roles role = employeeRoleExtractor.extract(userId);

        removeRejectedStatus(filter);

        PaginatedResult paginatedResult = executeHttpCall(
            "ConciergePurchaseOrderControllerApi.getPurchaseOrders",
                () -> conciergePurchaseOrderControllerApi.getPurchaseOrders(userId, role, filter, searchText)
        );

        modificationFieldsForRole(paginatedResult, role);
        return paginatedResult;
    }

    //    Сейлз:
    //    ID (заявки) +
    //    Фото (юзера) -
    //    Имя (Никнейм customer)
    //    Источник -
    //    Дата (создания) +
    //    Статус (заявки) +
    //    Заказ (номер) +
    //
    //    Если роль админ консьерж сейлз, будет еще колонка "Сейлз менеджер" (ФИО сейлза)
    //            -- (еще не знаем кто это)Если супер админ, будет Сейлз менеджер и Сорсер (у двоих ФИО)
    //
    //    Сорсер:
    //    ID (заявки) +
    //    Фото (сейлза) -
    //    Имя (ФИО сейлза)
    //    Дата (создания) +
    //    Статус (заявки) +
    //    Заказ (номер) +
    @Deprecated
    private void modificationFieldsForRole(PaginatedResult paginatedResult, Roles role) {
        List<OrdersForConciergeDTO> ordersForConciergeDTOS = paginatedResult.getData();
        ordersForConciergeDTOS.forEach(ordersForConciergeDTO -> {
            if (role.equals(Roles.SALES)) {
                ordersForConciergeDTO.setCustomerInfo(enrichmentService.getCustomerInfo(ordersForConciergeDTO.getCustomerInfo()));
            } else if (role.equals(Roles.SOURCER)) {
                ordersForConciergeDTO.setSalesInfo(enrichmentService.getSalesInfo(ordersForConciergeDTO.getSalesInfo()));
            }
            if (isAdminSales()) {
                ordersForConciergeDTO.setSalesInfo(enrichmentService.getSalesInfo(securityService.getCurrentAuthorizedUserId()));
            } else if (isAdminSourcer()) {
                ordersForConciergeDTO.setSourcerInfo(enrichmentService.getSourcerInfo(securityService.getCurrentAuthorizedUserId()));
            }
        });
        paginatedResult.setData(ordersForConciergeDTOS);
    }

    private boolean isAdminSales() {
        return securityService.hasAnyAuthority(AuthorityName.CONCIERGE_SALES_ADMIN, AuthorityName.ADMIN);
    }

    private boolean isAdminSourcer() {
        return securityService.hasAnyAuthority(AuthorityName.CONCIERGE_SOURCERS_ADMIN, AuthorityName.ADMIN);
    }

    @Override
    public PurchaseOrderFullDTO updatePurchaseOrder(Long orderId, PurchaseOrderMobileUpdateRequest request) {
        checkRequest(request);
        Long userId = securityService.getCurrentAuthorizedUserId();
        PurchaseOrderUpdateRequest updateRequest = new PurchaseOrderUpdateRequest();
        updateRequest.setDescription(request.getDescription());
        updateRequest.setImagesUrl(request.getImagesUrl());
        updateRequest.setLink(request.getLink());

        return executeHttpCall("ConciergePurchaseOrderControllerApi.updatePurchaseOrder",
                () -> conciergePurchaseOrderControllerApi.updatePurchaseOrder(orderId, userId, updateRequest));
    }

    @Override
    public OrderSources getSources() {
        return executeHttpCall("ConciergePurchaseOrderControllerApi.getSources",
                conciergePurchaseOrderControllerApi::getSources);
    }

    private void checkRequest(PurchaseOrderMobileUpdateRequest request) {
        boolean hasDescription = request.getDescription() != null && !request.getDescription().trim().isEmpty();

        request.setImagesUrl(filterOutEmptyImagesUrls(request.getImagesUrl()));
        boolean hasImages = !request.getImagesUrl().isEmpty();

        if (!hasDescription && !hasImages) {
            throw new ValidationFailedException("Заявка должна содержать описание или изображения", "description or images", "Обязательное поле");
        }
    }

    @Override
    public PurchaseOrderFullDTO returnToWork(Long orderId, String comment) {
        Long userId = securityService.getCurrentAuthorizedUserId();
        return executeHttpCall("ConciergePurchaseOrderControllerApi.returnToWork",
                () -> conciergePurchaseOrderControllerApi.returnToWork(userId, orderId, comment));
    }

    @Override
    public ConciergeOrderDetailsResponse getPurchaseOrderForApp(long orderId) {
        PurchaseOrderFullDTO purchaseOrder = getPurchaseOrderForUser(orderId);

        return purchaseOrderToAppMapper.toDTO(purchaseOrder);
    }

    @Override
    public ConciergeOrderDetailsResponse createRejectionEventToApp(RejectionEventRequest request, Long orderId) {
        PurchaseOrderFullDTO orderFullDTO = createRejectionEvent(request, orderId);

        return purchaseOrderToAppMapper.toDTO(orderFullDTO);
    }

    /**
     * Проверка существования пользователя
     * @param userId идентификатор пользователя
     */
    private void checkExistUser(Long userId) {
        Optional<User> user = userService.getUserById(userId);

        if (!user.isPresent()) {
            throw new UserNotFoundException("Пользователь не найден. ID: " + userId);
        }
    }

    public void removeRejectedStatus(RequestsFilter filter) {
        List<RequestsFilter.OrderStatusEnumsEnum> orderStatusEnums = filter.getOrderStatusEnums();
        if (orderStatusEnums == null) {
            return;
        }

        boolean hasAllStatus = orderStatusEnums.stream()
                .anyMatch(status -> status != null &&
                        PurchaseOrderStatus.ALL.name().equals(status.name()));

        if (hasAllStatus || orderStatusEnums.isEmpty()) {
            orderStatusEnums.clear();

            orderStatusEnums.addAll(Arrays.stream(RequestsFilter.OrderStatusEnumsEnum.values())
                    .filter(status -> status != RequestsFilter.OrderStatusEnumsEnum.REJECTED
                            && status != RequestsFilter.OrderStatusEnumsEnum.CANCELLED
                            && status != RequestsFilter.OrderStatusEnumsEnum.ALL)
                    .collect(Collectors.toList()));
        }
    }
}
