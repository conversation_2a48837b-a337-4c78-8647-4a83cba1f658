package su.reddot.domain.service.concierge.model;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import su.reddot.domain.service.filter.model.SearchQuery;
import su.reddot.presentation.api.v2.filter.ClientProductFiltrationContext;
import su.reddot.presentation.api.v2.filter.Source;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Запрос для получения доступных фильтров (брендов и категорий) для шопера")
public class ShopperAvailableFiltersRequest {

    @Schema(description = "Поисковый запрос для фильтрации результатов")
    private SearchQuery search;

    @Schema(description = "Применяемые фильтры в формате ключ-значение")
    private Map<String, JsonNode> filters;

    @Schema(description = "Скрытые фильтры, которые не возвращаются в availableFilters")
    private Map<String, JsonNode> hiddenFilters;

    @Schema(description = "Предустановленные значения фильтров")
    private Map<String, JsonNode> presets;

    @Schema(description = "Базовая категория для фильтрации", example = "123")
    private Long baseCategory;

    @Schema(description = "Код валюты", example = "RUB")
    private String currencyCode;

    @Schema(description = "Идентификатор страны", example = "1")
    private Long countryId;

    @Schema(description = "Контексты фильтрации продуктов")
    private List<ClientProductFiltrationContext> contexts;

    @Schema(description = "Источник запроса")
    private Source source;

    @Schema(description = "Включать ли бренды в ответ", example = "true")
    private Boolean includeBrands = true;

    @Schema(description = "Включать ли категории в ответ", example = "true")
    private Boolean includeCategories = true;

    @Schema(description = "Максимальное количество брендов для возврата", example = "50")
    private Integer maxBrands;

    @Schema(description = "Максимальное количество категорий для возврата", example = "20")
    private Integer maxCategories;

    @Schema(description = "Включать ли количество товаров для каждого фильтра", example = "true")
    private Boolean includeProductsCount = true;

    public ShopperAvailableFiltersRequest(ShopperAvailableFiltersRequest request) {
        this.search = request.getSearch();
        this.filters = request.getFilters();
        this.hiddenFilters = request.getHiddenFilters();
        this.presets = request.getPresets();
        this.baseCategory = request.getBaseCategory();
        this.currencyCode = request.getCurrencyCode();
        this.countryId = request.getCountryId();
        this.contexts = request.getContexts();
        this.source = request.getSource();
        this.includeBrands = request.getIncludeBrands();
        this.includeCategories = request.getIncludeCategories();
        this.maxBrands = request.getMaxBrands();
        this.maxCategories = request.getMaxCategories();
        this.includeProductsCount = request.getIncludeProductsCount();
    }
}
