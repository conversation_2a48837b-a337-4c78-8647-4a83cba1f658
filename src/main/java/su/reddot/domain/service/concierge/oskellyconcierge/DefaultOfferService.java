package su.reddot.domain.service.concierge.oskellyconcierge;

import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.concierge.CustomConcierge400Exception;
import su.reddot.domain.exception.concierge.CustomConcierge404Exception;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductCondition;
import su.reddot.domain.model.size.Size;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.concierge.api.OffersControllerApi;
import su.reddot.domain.service.concierge.model.BuyerOffers;
import su.reddot.domain.service.concierge.model.OfferDTO;
import su.reddot.domain.service.concierge.model.OfferType;
import su.reddot.domain.service.concierge.model.ProposedOfferDTO;
import su.reddot.domain.service.concierge.model.PurchaseOrderFullDTO;
import su.reddot.domain.service.concierge.model.SellerInfoDTO;
import su.reddot.domain.service.concierge.model.SendOffersToClientRequest;
import su.reddot.domain.service.concierge.model.ShimpentSizeDTO;
import su.reddot.domain.service.concierge.model.ShipmentOffersDTO;
import su.reddot.domain.service.concierge.model.ShipmentResponseDTO;
import su.reddot.domain.service.concierge.oskellyconcierge.dto.ConciergeErrorResponse;
import su.reddot.domain.service.dto.UserDTO;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.size.SizeService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.security.SecurityService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class DefaultOfferService extends ConciergeHTTPHandlerService implements OfferService {
    private final SecurityService securityService;
    private final OffersControllerApi offersControllerApi;
    private final ProductRepository productRepository;
    private final ProductService productService;
    private final UserService userService;
    private final EnrichmentService enrichmentService;
    private final SizeService sizeService;

    public DefaultOfferService(MicrometerService micrometerService, SecurityService securityService, OffersControllerApi offersControllerApi, ProductRepository productRepository, ProductService productService, UserService userService, EnrichmentService enrichmentService, SizeService sizeService) {
        super(micrometerService);
        this.securityService = securityService;
        this.offersControllerApi = offersControllerApi;
        this.productRepository = productRepository;
        this.productService = productService;
        this.userService = userService;
        this.enrichmentService = enrichmentService;
        this.sizeService = sizeService;
    }

    @Override
    public ShipmentOffersDTO addingProducts(ShipmentOffersDTO shipmentOffers) {
        Long userId = securityService.getCurrentAuthorizedUserId();
        enrichSellerInfoForProduct(shipmentOffers);
        return executeHttpCall("OffersControllerApi.addingProducts",
                () -> enrichShipmentOffers(offersControllerApi.addingProducts(userId, shipmentOffers)));
    }


    @Override
    public ShipmentOffersDTO addingShoppers(ShipmentOffersDTO shipmentOffers) {
        Long userId = securityService.getCurrentAuthorizedUserId();
        enrichSellerInfo(shipmentOffers);
        return executeHttpCall("OffersControllerApi.addingShoppers",
                () -> enrichShipmentOffers(offersControllerApi.addingShoppers(userId, shipmentOffers)));
    }

    @Override
    public ShipmentOffersDTO addShopperOffer(ShipmentOffersDTO shipmentOffers) {
        List<OfferDTO> offers = Optional.ofNullable(shipmentOffers.getOffers())
                .orElse(Collections.emptyList());
        validateProductConditions(offers);
        Long userId = securityService.getCurrentAuthorizedUserId();
        enrichSellerInfo(shipmentOffers);
        return executeHttpCall("OffersControllerApi.addShopperOffer",
                () -> enrichShipmentOffers(offersControllerApi.addShopperOffer(userId, shipmentOffers)));
    }

    @Override
    public List<ProposedOfferDTO> getProposedOffersByOffer(Long offerId) {
        return executeHttpCall("OffersControllerApi.getProposedOffersByOffer",
                () -> offersControllerApi.getProposedOffersByOffer(offerId));
    }

    /**
     * Проверка, существуют ли переданные с предложениями состояния товаров
     * */
    private void validateProductConditions(List<OfferDTO> offers) {
        Set<Long> productConditionIds = offers.stream()
                .map(OfferDTO::getBuyerOffers)
                .filter(Objects::nonNull)
                .flatMap(buyerOffers -> Optional.ofNullable(buyerOffers.getProposedOffers())
                        .orElse(Collections.emptySet())
                        .stream())
                .map(ProposedOfferDTO::getProductCondition)
                .map(su.reddot.domain.service.concierge.model.ProductConditionDTO::getId)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(productConditionIds)) {
            return;
        }

        Set<Long> existingConditionIds = productService.getProductConditionsCached().stream()
                .map(ProductCondition::getId)
                .collect(Collectors.toSet());

        if (!existingConditionIds.containsAll(productConditionIds)) {
            throw new CustomConcierge400Exception(
                    "Состояние товара не найдено",
                    null,
                    null,
                    HttpStatus.BAD_REQUEST,
                    ConciergeErrorResponse.builder()
                            .message("Состояние товара не найдено")
                            .httpCode(400)
                            .timestamp(LocalDateTime.now())
                            .build());
        }
    }

    @Override
    public Void sendOffers(SendOffersToClientRequest sendOffersRequest) {
        Long userId = securityService.getCurrentAuthorizedUserId();
        executeHttpCall(
                "OffersControllerApi.sendOffers",
                () -> offersControllerApi.sendOffers(userId, sendOffersRequest)
        );
        return null;
    }

    private PurchaseOrderFullDTO enrichBestOffersForProduct(PurchaseOrderFullDTO purchaseOrder) {
        List<ShipmentResponseDTO> enrichedShipments = Optional.ofNullable(purchaseOrder.getShipments())
                .orElse(Collections.emptyList())
                .stream()
                .map(this::enrichShipmentWithBestPriceCriteria) // Используем обновленный метод
                .collect(Collectors.toList());
        purchaseOrder.setShipments(enrichedShipments);
        return purchaseOrder;
    }

    /**
     * Обогащает предложения в конкретной поставке критерием лучшей цены.
     *
     * @param shipment поставка для обработки
     * @return поставка с обновленными предложениями
     */
    private ShipmentResponseDTO enrichShipmentWithBestPriceCriteria(ShipmentResponseDTO shipment) {
        List<OfferDTO> offers = shipment.getOffers();
        if (CollectionUtils.isEmpty(offers)) {
            return shipment;
        }

        // Загружаем все продукты за один запрос
        Map<Long, Product> productMap = productRepository.findAllById(offers.stream()
                .filter(offer -> offer.getProduct() != null)
                .filter(offer -> offer.getProduct().getProductId() != null)
                .map(offer -> offer.getProduct().getProductId())
                .collect(Collectors.toSet()))
            .stream()
            .collect(Collectors.toMap(Product::getId, Function.identity()));

        // Обогащаем каждое предложение ценой товара для дальнейшего сравнения
        for (OfferDTO offer : offers) {
            if (offer.getProduct() == null || offer.getProduct().getProductId() == null) {
                continue;
            }

            Product product = productMap.get(offer.getProduct().getProductId());
            if (product != null && offer.getType() == OfferType.PLATFORM_PRODUCT) {
                offer.getProduct().setCurrencyPrice(product.getCurrentPrice());
            }
        }

        // 1. Находим глобальную минимальную цену для всей поставки
        Optional<BigDecimal> minPriceOpt = findMinPrice(offers);
        if (!minPriceOpt.isPresent()) {
            return shipment;
        }
        final BigDecimal minPrice = minPriceOpt.get();

        // 2. Обновляем критерии для каждого offer на основе минимальной цены
        List<OfferDTO> updatedOffers = offers.stream()
                .map(offer -> updateOfferCriteriaBasedOnBestPrice(offer, minPrice))
                .collect(Collectors.toList());

        shipment.setOffers(updatedOffers);
        return shipment;
    }

    /**
     * Находит минимальную цену среди всех вложенных предложений и связанных товаров платформы.
     *
     * @param offers список предложений для анализа
     * @return минимальная цена, если найдена
     */
    private Optional<BigDecimal> findMinPrice(List<OfferDTO> offers) {
        return Stream.concat(
                offers.stream() // цены из товаров платформы
                    .filter(i -> i.getProduct() != null && i.getProduct().getCurrencyPrice() != null)
                    .map(i -> i.getProduct().getCurrencyPrice()),
                offers.stream() // цены из предложений
                    .flatMap(this::extractPrices)
                    .filter(Objects::nonNull))
            .min(BigDecimal::compareTo);
    }

    /**
     * Извлекает все применимые цены из вложенных предложений (proposedOffer) каждого предложения (offer).
     *
     * @param offer предложение для извлечения цен
     * @return поток всех применимых цен из предложения
     */
    private Stream<BigDecimal> extractPrices(OfferDTO offer) {
        return Optional.ofNullable(offer.getBuyerOffers())
                .map(BuyerOffers::getProposedOffers)
                .orElse(Collections.emptySet())
                .stream()
                .map(ProposedOfferDTO::getRublePrice)
                .filter(Objects::nonNull);
    }

    /**
     * Обновляет критерии `OfferDTO`, устанавливая флаг `PRICE`, если этот `OfferDTO`
     * или одно из его вложенных `ProposedOfferDTO` имеет лучшую цену.
     *
     * @param offer    предложение для обновления
     * @param minPrice минимальная цена для сравнения
     * @return обновленное предложение с корректными критериями
     */
    private OfferDTO updateOfferCriteriaBasedOnBestPrice(OfferDTO offer, BigDecimal minPrice) {
        // Проверяем, содержит ли этот оффер (или его дочерние элементы) лучшую цену
        boolean hasBestPrice = checkOfferForBestPrice(offer, minPrice);

        Set<OfferDTO.ComparisonCriteriaEnum> criteria = offer.getComparisonCriteria() != null
                ? EnumSet.copyOf(offer.getComparisonCriteria())
                : EnumSet.noneOf(OfferDTO.ComparisonCriteriaEnum.class);

        if (hasBestPrice) {
            criteria.add(OfferDTO.ComparisonCriteriaEnum.PRICE);
        } else {
            criteria.remove(OfferDTO.ComparisonCriteriaEnum.PRICE);
        }

        // Пересоздаем OfferDTO с обновленными критериями. proposedOffers остаются неизменными.
        return new OfferDTO()
                .id(offer.getId())
                .comparisonCriteria(criteria) // Обновленное поле
                .shopper(offer.getShopper())
                .shipmentId(offer.getShipmentId())
                .seller(offer.getSeller())
                .product(offer.getProduct())
                .type(offer.getType())
                .sellerType(offer.getSellerType())
                .creationDate(offer.getCreationDate())
                .buyerOffers(offer.getBuyerOffers());
    }

    /**
     * Проверяет, имеет ли сам `OfferDTO` или одно из его вложенных `ProposedOfferDTO` лучшую (минимальную) цену.
     *
     * @param offer    предложение для проверки
     * @param minPrice минимальная цена для сравнения
     * @return true, если найдена лучшая цена
     */
    private boolean checkOfferForBestPrice(OfferDTO offer, BigDecimal minPrice) {
        if (offer.getProduct() != null && minPrice.equals(offer.getProduct().getCurrencyPrice())) {
            return true;
        }

        if (offer.getBuyerOffers() != null && offer.getBuyerOffers().getProposedOffers() != null) {
            return offer.getBuyerOffers().getProposedOffers().stream()
                    .map(ProposedOfferDTO::getRublePrice)
                    .filter(Objects::nonNull)
                    .anyMatch(price -> price.compareTo(minPrice) == 0);
        }

        return false;
    }

    private void enrichSellerInfoForProduct(ShipmentOffersDTO shipmentOffers) {
        List<OfferDTO> offers = shipmentOffers.getOffers();
        if (offers == null || offers.isEmpty()) {
            return;
        }

        // Собираем все ID продуктов, которые нужно получить
        List<Long> productIds = offers.stream()
                .filter(offer -> offer.getProduct() != null)
                .map(offer -> offer.getProduct().getProductId())
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (productIds.isEmpty()) {
            return;
        }

        // Получаем все продукты за один запрос
        List<Product> products = productService.getRawProductsByIds(productIds);
        Map<Long, Product> productMap = products.stream()
                .collect(Collectors.toMap(Product::getId, Function.identity()));

        // Обрабатываем каждое предложение
        for (OfferDTO offer : offers) {
            if (offer.getProduct() == null || offer.getProduct().getProductId() == null) {
                continue;
            }

            Long productId = offer.getProduct().getProductId();
            if (!productMap.containsKey(productId)) {
                throw new CustomConcierge404Exception(
                        "Найдены товары, отсутствующие на платформе.",
                        null,
                        null,
                        HttpStatus.BAD_REQUEST,
                        ConciergeErrorResponse.builder()
                                .timestamp(LocalDateTime.now())
                                .httpCode(400)
                                .message(productId.toString())
                                .build());
            }

            Product product = productMap.get(productId);

            offer.setProduct(enrichmentService.enrichProduct(offer, product));

            UserDTO user = userService.getUserDTO(product.getSeller());
            SellerInfoDTO sellerInfo = new SellerInfoDTO();
            sellerInfo.setSellerId(product.getSeller().getId());
            sellerInfo.setSellerEmail(user.getEmail());
            sellerInfo.setSellerFio(user.getFullName());
            sellerInfo.setSellerNickname(user.getNickname());
            offer.setSeller(sellerInfo);
            offer.setSellerType(user.getSellerType().name());
        }
    }

    private void enrichSellerInfo(ShipmentOffersDTO shipmentOffers) {
        List<OfferDTO> offers = shipmentOffers.getOffers();
        if (offers == null || offers.isEmpty()) {
            throw new CustomConcierge400Exception(
                    "Список предложений не может быть пустым",
                    null,
                    null,
                    HttpStatus.BAD_REQUEST,
                    ConciergeErrorResponse.builder()
                            .message("Список предложений не может быть пустым")
                            .httpCode(400)
                            .timestamp(LocalDateTime.now())
                            .build());
        }

        for (OfferDTO offer : offers) {
            if (offer != null && offer.getSeller() != null) {
                enrichSeller(offer);
            }
        }
    }

    private void enrichSeller(OfferDTO offer) {
        // Получаем данные пользователя
        Long sellerId = offer.getSeller().getSellerId();
        User user = userService.getUserOrThrowUserNotFoundException(sellerId);

        // Обогащаем информацию о продавце
        SellerInfoDTO sellerInfoDTO = offer.getSeller();
        sellerInfoDTO.setSellerEmail(user.getEmail());
        sellerInfoDTO.setSellerFio(user.getFullName().orElse(null));
        sellerInfoDTO.setSellerNickname(user.getNickname());
        sellerInfoDTO.setUrlAvatar(user.getAvatarPath());
        offer.setSellerType(user.getSellerType().name());
        offer.setSeller(sellerInfoDTO);
    }

    /**
     * Обогащает предложенные товары дополнительной информацией, такой как цены товаров и данные о продавце.
     * Для каждого товара в предложении загружается соответствующая информация о товаре и, при необходимости,
     * обновляется цена предложения. Также обогащается информация о продавце.
     *
     * @param shipmentOffers DTO, содержащий предложенные товары, которые необходимо обогатить
     * @return обогащенный DTO предложенных товаров
     */
    private ShipmentOffersDTO enrichShipmentOffers(ShipmentOffersDTO shipmentOffers) {
        List<OfferDTO> offers = shipmentOffers.getOffers();
        if (offers == null || offers.isEmpty()) {
            return shipmentOffers;
        }

        // Собираем ID всех продуктов для пакетной загрузки
        List<Long> productIds = offers.stream()
                .filter(offer -> offer != null &&
                        offer.getProduct() != null &&
                        offer.getProduct().getProductId() != null)
                .map(offer -> offer.getProduct().getProductId())
                .collect(Collectors.toList());

        // Получаем один раз все состояния товаров
        Map<Long, ProductCondition> conditionsByIds = productService.getProductConditionsCached().stream()
                .collect(Collectors.toMap(ProductCondition::getId, Function.identity()));

        // Обогащаем информацию о состоянии товаров
        offers.stream()
                .filter(offer -> offer.getBuyerOffers() != null)
                .flatMap(offer -> Optional.ofNullable(offer.getBuyerOffers().getProposedOffers())
                        .orElse(Collections.emptySet())
                        .stream())
                .forEach(proposedOffer -> {
                    su.reddot.domain.service.concierge.model.ProductConditionDTO conditionDTO = proposedOffer.getProductCondition();
                    if (conditionDTO != null) {
                        ProductCondition condition = conditionsByIds.get(conditionDTO.getId());
                        if (condition != null) {
                            conditionDTO.setName(condition.getName());
                            conditionDTO.setDescription(condition.getDescription());
                        }
                    }
                });

        if (productIds.isEmpty()) {
            return shipmentOffers;
        }

        // Загружаем все продукты за один запрос
        List<Product> products = productRepository.findAllById(productIds);
        Map<Long, Product> productMap = products.stream()
                .collect(Collectors.toMap(Product::getId, Function.identity()));

        // Обогащаем каждое предложение
        for (OfferDTO offer : offers) {
            if (offer == null || offer.getProduct() == null || offer.getProduct().getProductId() == null) {
                continue;
            }

            Product product = productMap.get(offer.getProduct().getProductId());
            if (product != null && offer.getType() == OfferType.PLATFORM_PRODUCT) {
                offer.getProduct().setCurrencyPrice(product.getCurrentPrice());
            }

            // Обогащаем информацию о продавце
            if (offer.getSeller() != null) {
                enrichSeller(offer);
            }
        }

        return shipmentOffers;
    }

    @Override
    public Void deleteProduct(Long productId) {
        if (productId == null) {
            throw new IllegalArgumentException("ID продукта не может быть null");
        }

        Long userId = securityService.getCurrentAuthorizedUserId();
        if (userId == null) {
            throw new SecurityException("Не удалось определить текущего пользователя");
        }

        executeHttpCall("OffersControllerApi.deleteProduct",
                () -> offersControllerApi.deleteProduct(userId, productId));
        return null;
    }

    @Override
    public PurchaseOrderFullDTO enrichOffersForPurchaseOrder(PurchaseOrderFullDTO purchaseOrder) {
        enrichShipmentOffers(purchaseOrder);
        enrichSalesInfo(purchaseOrder);
        enrichSourcerInfo(purchaseOrder);
        enrichCustomerInfo(purchaseOrder);
        return enrichBestOffersForProduct(purchaseOrder);
    }

    private void enrichCustomerInfo(PurchaseOrderFullDTO purchaseOrder) {
        if (purchaseOrder.getCustomer() == null && purchaseOrder.getCustomer().getCustomerId() == null) {
            return;
        }
        purchaseOrder.setCustomer(enrichmentService.getCustomerInfo(purchaseOrder.getCustomer().getCustomerId()));
    }

    private void enrichShipmentOffers(PurchaseOrderFullDTO purchaseOrder) {
        if (CollectionUtils.isEmpty(purchaseOrder.getShipments())) {
            return;
        }

        purchaseOrder.getShipments().forEach(shipment -> {
            if (shipment == null) {
                return;
            }
            if (shipment.getShipmentSize()!= null && shipment.getShipmentSize().getSizeId() != null) {
                Size size = sizeService.fromId(shipment.getShipmentSize().getSizeId());
                ShimpentSizeDTO shipmentSize = new ShimpentSizeDTO()
                        .type(shipment.getShipmentSize().getType())
                        .sizeId(shipment.getShipmentSize().getSizeId())
                        .availableSizes(Collections.singleton(size.getBySizeType(SizeType.valueOf(shipment.getShipmentSize().getType()))));
                shipment.setShipmentSize(shipmentSize);
            }
            if (!CollectionUtils.isEmpty(shipment.getOffers())) {
                shipment.getOffers().forEach(this::enrichOffer);
            }
        });
    }

    private void enrichOffer(OfferDTO offer) {
        if (offer.getSeller() != null) {
            offer.setSeller(enrichmentService.getSellerInfo(offer.getSeller()));
        }
        if (offer.getProduct() != null && offer.getProduct().getProductId() != null) {
            enrichmentService.enrichProductInfo(offer.getProduct());
        }
        if (offer.getBuyerOffers() != null && offer.getBuyerOffers().getProposedOffers() != null) {
            offer.getBuyerOffers().getProposedOffers().stream()
                    .filter(proposedOfferDTO -> proposedOfferDTO.getProposedProduct() != null)
                    .forEach(proposedOfferDTO -> enrichmentService.enrichProductInfo(proposedOfferDTO.getProposedProduct()));
        }
    }

    private void enrichSalesInfo(PurchaseOrderFullDTO purchaseOrder) {
        if (purchaseOrder.getSalesInfo() != null && purchaseOrder.getSalesInfo().getSalesId() != null) {
            purchaseOrder.setSalesInfo(enrichmentService.getSalesInfo(purchaseOrder.getSalesInfo().getSalesId()));
        }
    }

    private void enrichSourcerInfo(PurchaseOrderFullDTO purchaseOrder) {
        if (purchaseOrder.getSourcerInfo() != null && purchaseOrder.getSourcerInfo().getSourcerId() != null) {
            purchaseOrder.setSourcerInfo(enrichmentService.getSourcerInfo(purchaseOrder.getSourcerInfo().getSourcerId()));
        }
    }
}
