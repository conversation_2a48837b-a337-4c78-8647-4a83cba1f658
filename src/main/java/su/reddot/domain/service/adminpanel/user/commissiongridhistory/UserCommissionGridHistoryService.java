package su.reddot.domain.service.adminpanel.user.commissiongridhistory;

import com.google.common.collect.ImmutableList;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.dao.commissiongridhistory.UserCommissionGridHistoryRepository;
import su.reddot.domain.model.commissiongridhistory.UserCommissionGridHistory;
import su.reddot.domain.model.commissiongridhistory.UserCommissionGridHistoryStatus;
import su.reddot.domain.service.adminpanel.user.domain.CommissionGridChangeDTO;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserCommissionGridHistoryService {
    private final UserCommissionGridHistoryRepository repository;
    private final UserCommissionGridHistoryProcessorService processorService;
    private final CommissionGridSettings settings;

    @Transactional
    public void saveCommissionGridChangeEvent(CommissionGridChangeDTO dto) {
        try {
            //сохраняем очередную запись по смене комиссионной сетки в "очередь"
            repository.save(fromEventDTO(dto));
        } catch (Throwable t) {
            //логирование ошибок для анализа ситуаций типа двойных евентов
            log.error("UserCommissionGridHistory persist error", t);
        }
    }

    public void processProductPriceChangeCycle() {
        if (!settings.isEnabled()) {
            log.warn("UserCommissionGridHistory disabled.");
            return;
        }

        // считаем, что крон настроен только на одном инстансе,
        // поэтому наличие задачи в процессе игнорируем и подхватываем, если такая осталась
        Optional<UserCommissionGridHistory> nextQueuedRecord =
                repository.findTopByStatusInOrderByIdAsc(
                        ImmutableList.of(UserCommissionGridHistoryStatus.IN_PROGRESS, UserCommissionGridHistoryStatus.QUEUED));

        if (!nextQueuedRecord.isPresent()) {
            log.info("No queued records");
            return;
        }

        UserCommissionGridHistory historyEntity = nextQueuedRecord.get();

        log.info("Next queued record " + historyEntity);

        //если какой-то второй поток первым установит этот статус, в этой строке будет исключение из-за уникального индекса
        processorService.commitInProgressStatus(historyEntity);

        processorService.processProductsForHistoryRecord(historyEntity.getId());
    }

    private UserCommissionGridHistory fromEventDTO(CommissionGridChangeDTO dto) {
        return new UserCommissionGridHistory(dto.getUserId(),
                dto.getCommissionGridOld(), dto.getCommissionGridNew(), dto.getUuid());
    }
}
