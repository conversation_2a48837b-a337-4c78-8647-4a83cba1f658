package su.reddot.domain.service.adminpanel.user.commissiongridhistory;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.dao.UserRepository;
import su.reddot.domain.dao.commission.CommissionGridRepository;
import su.reddot.domain.dao.commissiongridhistory.UserCommissionGridHistoryRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.exception.NotFoundException;
import su.reddot.domain.model.commission.CommissionGrid;
import su.reddot.domain.model.commissiongridhistory.UserCommissionGridHistory;
import su.reddot.domain.model.commissiongridhistory.UserCommissionGridHistoryStatus;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.adminpanel.product.AdminProductPriceServiceImpl;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.util.CallInTransaction;

import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserCommissionGridHistoryProcessorService {

    private final UserCommissionGridHistoryRepository repository;

    private final ProductRepository productRepository;

    private final CommissionGridSettings settings;

    private final AdminProductPriceServiceImpl adminProductPriceService;

    private final CommissionGridRepository commissionGridRepository;

    private final UserService userService;

    private final UserRepository userRepository;

    private final CallInTransaction callInTransaction;

    private static final List<ProductState> EXCLUDE_PRODUCT_STATES = Arrays.asList(ProductState.SOLD,
            ProductState.DELETED);

    @Transactional
    public void commitInProgressStatus(UserCommissionGridHistory entity) {
        entity.setStatus(UserCommissionGridHistoryStatus.IN_PROGRESS);
        repository.save(entity);
    }

    @Transactional
    public void processProductsForHistoryRecord(Long historyRecordId) {
        UserCommissionGridHistory historyRecord = repository.findById(historyRecordId).get();

        log.info("UserCommissionGridHistory progress start " + historyRecord);

        //Сюда должны попадать только IN_PROGRESS записи
        if (!historyRecord.isInProgress()) {
            throw new RuntimeException("UserCommissionGridHistory not in IN_PROGRESS status " + historyRecord.getId());
        }

        //товары пользователя для пересчета цен
        List<Product> products = findProductsBatchForPriceRefresh(historyRecord);

        //если нужных товаров нет, то либо их просто нет, либо они все уже были пересчитаны
        if (products.size() == 0) {
            historyRecord.setStatus(UserCommissionGridHistoryStatus.COMPLETED);
            log.info("UserCommissionGridHistory progress finished COMPLETED");
            return;
        }

        //пересчет цен для пачки продуктов
        for (Product product : products) {
            callInTransaction.runInNewTransaction(() -> refreshPrice(product));
        }

        //максимальный id из обновленных продуктов
        historyRecord.setLastUpdatedProductId(maxId(products));
        historyRecord.setStatus(UserCommissionGridHistoryStatus.QUEUED);

        log.info("UserCommissionGridHistory progress finished " + historyRecord);
    }

    @Transactional
    public Map<Long, Integer> forceUpdateUserGrid(Long newGridId, List<Long> userIds) {
        log.info("forceUpdateUserGrid: updating grid to {} for users {}", newGridId, userIds);
        // поиск сетки и пользователей нужны для валидации
        CommissionGrid newGrid = commissionGridRepository.findById(newGridId).orElseThrow(NotFoundException::new);
        List<User> users =
                userIds.stream().map(userService::getUserOrThrowUserNotFoundException).collect(Collectors.toList());

        Map<Long, Integer> result = new HashMap<>();
        for (User user : users) {
            log.info("forceUpdateUserGrid: processing user {}. Updating user grid to {}", user.getId(), newGridId);
            callInTransaction.runInNewTransaction(
                    () -> userRepository.changeUserCommissionGrid(user.getId(), newGridId));

            long lastProductId = 0L;
            boolean needLoop = true;
            int processed = 0;
            do {
                Long finalLastProductId = lastProductId;
                List<Product> products = callInTransaction.runInNewTransaction(() -> {
                    List<Product> innerProducts =
                            productRepository.findProductsForPriceRefresh(
                                    newGridId,
                                    user.getId(),
                                    EXCLUDE_PRODUCT_STATES.stream().map(ProductState::getName)
                                            .collect(Collectors.toList()),
                                    finalLastProductId,
                                    OffsetDateTime.now(),
                                    settings.getProductsBatchSize());
                    log.info("forceUpdateUserGrid: processing user {}. Found {} more products to update.",
                            user.getId(), innerProducts.size());

                    for (Product product : innerProducts) {
                        refreshPrice(product);
                    }

                    return innerProducts;
                });

                log.info("forceUpdateUserGrid: processing user {}. {} products processed.",
                        user.getId(), products.size());

                processed += products.size();
                lastProductId = products.stream().map(Product::getId).max(Long::compareTo).orElse(0L);

                if (products.size() < settings.getProductsBatchSize()) {
                    needLoop = false;
                }
            } while (needLoop);

            log.info("forceUpdateUserGrid: processing user {}. Total products processed: {}", user.getId(), processed);
            result.put(user.getId(), processed);
        }
        log.info("forceUpdateUserGrid: finished updating grid to {} for users {}", newGridId, userIds);
        return result;
    }

    private Long maxId(List<Product> products) {
        return products.stream().map(Product::getId)
                .max(Long::compareTo).get();
    }

    private void refreshPrice(Product product) {
        log.debug("Price refresh attempt for product {}. Old currentPrice = {}, currentPriceWithoutCommission = {}",
                product.getId(), product.getCurrentPrice(), product.getCurrentPriceWithoutCommission());
        try {
            adminProductPriceService.refreshPriceForAutoCalculate(product);
            product.getProductItems().forEach(adminProductPriceService::refreshProductItemPriceForAutoCalculate);
            log.debug("Price refreshed for product {}. Old currentPrice = {}, currentPriceWithoutCommission = {}",
                    product.getId(), product.getCurrentPrice(), product.getCurrentPriceWithoutCommission());
        } catch (Throwable t) {
            log.error("Price refresh error for product " + product.getId(), t);
        }
    }

    private List<Product> findProductsBatchForPriceRefresh(UserCommissionGridHistory historyRecord) {
        return productRepository.findProductsForPriceRefresh(
                historyRecord.getCommissionGridId(),
                historyRecord.getUserId() != null ? historyRecord.getUserId() : 0,
                EXCLUDE_PRODUCT_STATES.stream().map(ProductState::getName).collect(Collectors.toList()),
                historyRecord.getLastUpdatedProductId(),
                historyRecord.getCreateDate().plusMinutes(5L),
                settings.getProductsBatchSize());
    }
}
