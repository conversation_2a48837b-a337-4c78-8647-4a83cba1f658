package su.reddot.domain.service.adminpanel.v2;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import su.reddot.domain.model.adminpanel.v2.PrimaryPageType;
import su.reddot.domain.model.adminpanel.v2.common.SelectedProductBlock;
import su.reddot.domain.model.adminpanel.v2.filter.AbstractFilter;
import su.reddot.domain.model.adminpanel.v2.filter.CategoryAndUserFilter;
import su.reddot.domain.model.adminpanel.v2.filter.ProductListFilter;
import su.reddot.domain.model.adminpanel.v2.filter.UserProfileFilter;
import su.reddot.domain.model.user.SellerType;
import su.reddot.domain.model.product.ProductCondition;
import su.reddot.domain.service.adminpanel.v2.dto.GetOskellyChoiceContentBlockParams;
import su.reddot.domain.service.adminpanel.v2.dto.GetOskellyChoiceContentBlockWrapperParams;
import su.reddot.domain.service.adminpanel.v2.dto.GetProductsForPrimaryPageParams;
import su.reddot.domain.service.catalog.CatalogSlotFeaturesService;
import su.reddot.domain.service.dto.Page;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.primary.PrimaryContentDTO;
import su.reddot.domain.service.dto.primary.PrimaryPageProductDTO;
import su.reddot.domain.service.dto.primary.converter.PrimaryPageProductDtoConverter;
import su.reddot.domain.service.dto.primary.enums.PrimaryContentType;
import su.reddot.domain.service.filter.FilterTransformationService;
import su.reddot.domain.service.filter.FiltrationServiceFeatures;
import su.reddot.domain.service.filter.ProductFilterProcessorEngine;
import su.reddot.domain.service.filter.ProductFilterTransformationService;
import su.reddot.domain.service.filter.ProductFiltrationEngine;
import su.reddot.domain.service.filter.ProductFiltrationEngine.CountProductsOptions;
import su.reddot.domain.service.filter.ProductFiltrationEngine.GetProductsOptions;
import su.reddot.domain.service.filter.ProductFiltrationEngine.ProductFiltrationPageRequest;
import su.reddot.domain.service.filter.ProductFiltrationEngineSupport;
import su.reddot.domain.service.filter.processor.filter.impl.NewResaleFilterProcessor;
import su.reddot.domain.service.filter.processor.filter.impl.ResaleFilterProcessor;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.product.ProductService.FilterSpecification;
import su.reddot.domain.service.product.ProductService.SortAttribute;
import su.reddot.infrastructure.configparam.dto.SlotPatternDTO;
import su.reddot.presentation.DeeplinkUtils;
import su.reddot.presentation.adminpanel.v2.dto.filter.FilterContentDto;
import su.reddot.presentation.api.v2.filter.ProductFilterItemsRequest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static su.reddot.domain.model.product.ProductState.PUBLISHED;
import static su.reddot.domain.service.product.ProductService.SortAttribute.PUBLISH_TIME_DESC;

@Slf4j
@RequiredArgsConstructor
@Service
public class AdminPanelProductService {

    private static final int OSKELLY_CHOICE_PRODUCT_COUNT = 6;

    private final ProductService productService;

    private final ProductFiltrationEngine productFiltrationEngine;

    private final ProductFiltrationEngineSupport productFiltrationEngineSupport;

    private final ProductFilterProcessorEngine productFilterProcessorEngine;

    private final PrimaryPageProductDtoConverter primaryPageProductDtoConverter;

    private final MessageSourceAccessor messageSourceAccessor;

    private final DeeplinkUtils deeplinkUtils;

    @Autowired
    private AdminPanelProductService self;

    /**
     * Категории, которые должны исключаться в подборках главной
     */
    @Value("${app.indexPageProductsSetExceptCategoryIds}")
    private List<Long> indexPageProductsSetExceptCategoryIds;

    public long getTotalCount(AbstractFilter filter) {

        if (filter == null || filter.isEmpty()) {
            return 0;
        }

        FilterSpecification filterSpec = mapToFilterSpecOrDefault(filter);

        CountProductsOptions options = new CountProductsOptions();
        options.setForAdmin(true);
        productFiltrationEngineSupport.fillFiltrationEngineOptions(options);

        return productFiltrationEngine.countProducts(filterSpec, options);
    }

    private FilterSpecification mapToFilterSpec(AbstractFilter filter) {
        FilterSpecification filterSpecification = null;
        switch (filter.getFilterType()) {
            case CATEGORY_AND_USER:
                filterSpecification = mapToFilterSpec((CategoryAndUserFilter) filter);
                break;
            case PRODUCT_LIST:
                filterSpecification = mapToFilterSpec((ProductListFilter) filter);
                break;
            case USER_PROFILE:
                filterSpecification = mapToFilterSpec((UserProfileFilter) filter);
        }
        if (filterSpecification != null) {
            filterSpecification.state(PUBLISHED);
        }
        return filterSpecification;
    }

    public FilterSpecification mapToFilterSpecOrDefault(AbstractFilter filter) {
        FilterSpecification filterSpec = mapToFilterSpec(filter);
        if (filterSpec == null) {
            filterSpec = new FilterSpecification().state(PUBLISHED);
        }
        return filterSpec;
    }

    @SuppressWarnings("unchecked")
    private FilterSpecification mapToFilterSpec(CategoryAndUserFilter filter) {
        List<Long> attrValues = new ArrayList<>();

        if (filter.getColorIds() != null) {
            attrValues.addAll(filter.getColorIds());
        }

        if (filter.getMaterialIds() != null) {
            attrValues.addAll(filter.getMaterialIds());
        }

        FilterSpecification filterSpecification = new FilterSpecification()
                .categoriesIds(filter.getCategoryIds() != null ? filter.getCategoryIds() : Collections.emptyList())
                .interestingBrands(filter.getBrandIds() != null ? filter.getBrandIds() : Collections.emptyList())
                .interestingProductModels(filter.getProductModelIds() != null ? filter.getProductModelIds() : Collections.emptyList())
                .interestingConditions(filter.getConditionIds() != null ? filter.getConditionIds() : Collections.emptyList())
                .interestingAttributeValues(attrValues);

        if (filter.getPrice() != null) {
            CategoryAndUserFilter.Price price = filter.getPrice();
            filterSpecification
                    .currencyCode(null)
                    .startPrice(price.getMin() != null ? BigDecimal.valueOf(filter.getPrice().getMin()) : null)
                    .endPrice(price.getMax() != null ? BigDecimal.valueOf(filter.getPrice().getMax()) : null);

        }

        filterSpecification.sellerId((filter.getUsers() == null || filter.getUsers().isEmpty()) && filter.getUser() != null ? filter.getUser().getId() : null)
                .sellerIds(isNotEmpty(filter.getUsers()) ? filter.getUsers().stream().map(CategoryAndUserFilter.User::getId).collect(Collectors.toList()) : null)
                .hasOurChoice(filter.getIsOskellyChoice() != null && filter.getIsOskellyChoice() ? Boolean.TRUE : null)
                .isBeegz(filter.getIsBeegz() != null && filter.getIsBeegz() ? Boolean.TRUE : null)
                .isInStock(filter.getIsInOskellyStock() != null && filter.getIsInOskellyStock() ? Boolean.TRUE : null)
                .isInBoutique(filter.getIsInBoutique() != null && filter.getIsInBoutique() ? Boolean.TRUE : null)
                .boutiqueLocationTags(isNotEmpty(filter.getBoutiqueLocationTagIds()) ? filter.getBoutiqueLocationTagIds() : null)
                .isExclusiveSelection(filter.getIsExclusiveSelection() != null && filter.getIsExclusiveSelection() ? Boolean.TRUE : null)
                .isNewCollection(filter.getIsNewCollection() != null && filter.getIsNewCollection() ? Boolean.TRUE : null)
                .isVintage(filter.getIsVintage() != null && filter.getIsVintage() ? Boolean.TRUE : null)
                .isBrandNew(filter.getIsBrandNew() != null && filter.getIsBrandNew() ? Boolean.TRUE : null);

        if (Boolean.TRUE.equals(filter.getIsResale())) {
            filterSpecification.exceptSellerTypes(CollectionUtils.union(
                    filterSpecification.exceptSellerTypes(),
                    ResaleFilterProcessor.EXPECT_SELLER_TYPES)
            );

            filterSpecification.exceptUserTags(CollectionUtils.union(
                    filterSpecification.exceptUserTags(),
                    ResaleFilterProcessor.EXPECT_USER_TAGS)
            );
        }

        if (CollectionUtils.isNotEmpty(filter.getSellerTypes())){
            List<SellerType> sellerTypes = filter.getSellerTypes().stream()
                    .map(it -> SellerType.valueOf(it.name()))
                    .collect(Collectors.toList());
            filterSpecification.interestingSellerTypes(sellerTypes);
        }

        filterSpecification.newResaleSelection(filter.getNewResale());
        if (filter.getNewResale() != null) {
            switch (filter.getNewResale()) {
                case NEW:
                    filterSpecification.interestingSellerTypes(
                            CollectionUtils.isNotEmpty(filterSpecification.interestingSellerTypes())
                                    ? CollectionUtils.intersection(filterSpecification.interestingSellerTypes(), NewResaleFilterProcessor.NEW_SELLER_TYPES)
                                    : NewResaleFilterProcessor.NEW_SELLER_TYPES
                    );
                    Collection<Long> existingConditions = filterSpecification.interestingConditions();
                    if (!CollectionUtils.isEmpty(existingConditions) && !existingConditions.contains(ProductCondition.CONDITION_ID_NEW)) {
                        filterSpecification.interestingConditions(Collections.singletonList(ProductCondition.NOT_EXISTED_CONDITION_ID));
                    } else {
                        filterSpecification.interestingConditions(Collections.singletonList(ProductCondition.CONDITION_ID_NEW));
                    }
                    break;
                case RESALE:
                    filterSpecification.interestingSellerTypes(
                            CollectionUtils.isNotEmpty(filterSpecification.interestingSellerTypes())
                                    ? CollectionUtils.intersection(filterSpecification.interestingSellerTypes(), NewResaleFilterProcessor.RESALE_SELLER_TYPES)
                                    : NewResaleFilterProcessor.RESALE_SELLER_TYPES
                    );
                    break;
                case ALL:
            }
        }

        return filterSpecification;
    }

    private FilterSpecification mapToFilterSpec(ProductListFilter filter) {
        return new FilterSpecification()
                .productsIds(filter.getProductIds()).excludeCrossborder(filter.isExcludeCrossBorder());
    }

    private FilterSpecification mapToFilterSpec(UserProfileFilter filter) {
        return new FilterSpecification()
                .sellerId(filter.getUserId());
    }

    @Transactional
    public Page<ProductDTO> getProductListPageable(FilterContentDto filter, String currencyCode, int pageNumber, int pageSize) {
        AbstractFilter abstractFilter = null;
        if (filter.getUserProfileFilter() != null) {
            abstractFilter = filter.getUserProfileFilter().toDomain();
        } else if (filter.getCategoryAndUserFilter() != null) {
            abstractFilter = filter.getCategoryAndUserFilter().toDomain();
        } else if (filter.getProductListFilter() != null) {
            abstractFilter = filter.getProductListFilter().toDomain();
        }
        return abstractFilter != null ? getProductListPageable(abstractFilter, currencyCode, pageNumber, pageSize) : Page.emptyPage();
    }

    public Page<ProductDTO> getProductListPageable(AbstractFilter filter, String currencyCode, int pageNumber, int pageSize) {
        return getProductListPageable(mapToFilterSpecOrDefault(filter), currencyCode, pageNumber, pageSize);
    }

    public Page<ProductDTO> getProductListPageable(FilterSpecification spec, String currencyCode, int pageNumber, int pageSize) {

        ProductFiltrationPageRequest pageRequest = new ProductFiltrationPageRequest(
                pageNumber + 1,
                pageSize,
                PUBLISH_TIME_DESC);

        GetProductsOptions options = new GetProductsOptions().setCurrencyCode(currencyCode);
        productFiltrationEngineSupport.fillFiltrationEngineOptions(options);

        return productFiltrationEngine
                .getProductPage(spec, pageRequest, options);
    }

    @Transactional
    public Page<ProductDTO> getProductListPageable(ProductFilterItemsRequest productFilterItemsRequest) {
        return productFilterProcessorEngine.getProductItems(productFilterItemsRequest);
    }

    @Transactional
    public Page<ProductDTO> getProductListPageable(ProductFilterItemsRequest productFilterItemsRequest, CatalogSlotFeaturesService.Features catalogSlotFeatures) {
        return productFilterProcessorEngine.getProductItems(productFilterItemsRequest, new FiltrationServiceFeatures(), catalogSlotFeatures);
    }

    private FilterSpecification makePPTypeSpecification(PrimaryPageType type) {
        FilterTransformationService.FilterSpecification filterSpecification =
                new FilterTransformationService.FilterSpecification();

        switch (type.getConfig().getNewResaleValue()) {
            case NEW:
                NewResaleFilterProcessor.appendNewSpecification(filterSpecification);
                break;
            case RESALE:
                NewResaleFilterProcessor.appendResaleSpecification(filterSpecification);
                break;
        }

        FilterSpecification resultSpec = ProductFilterTransformationService.toProductServiceSpec(
                filterSpecification);

        resultSpec.categoriesIds(Collections.singletonList(type.getConfig().getCategory().getId()));

        return resultSpec;
    }

    @Transactional
    public Page<PrimaryPageProductDTO> getProductsForPrimaryPage(GetProductsForPrimaryPageParams params) {
        PrimaryPageType type = params.getType();
        String currencyCode = params.getCurrencyCode();
        int pageSize = params.getPageSize();
        int pageNumber = params.getPageNumber();
        boolean usePromotionTimeBaseSorting = params.isUsePromotionTimeBaseSorting();
        SlotPatternDTO slotPattern = params.getSlotPattern();

        try {
            FilterSpecification spec = makePPTypeSpecification(type);

            spec.state(PUBLISHED)
                    .exceptCategoriesIds(indexPageProductsSetExceptCategoryIds)
                    .excludeCrossborder(params.isExcludeCrossBorder());

            ProductFiltrationPageRequest pageRequest = new ProductFiltrationPageRequest(
                    pageNumber + 1,
                    pageSize,
                    (usePromotionTimeBaseSorting) ? SortAttribute.PROMOTION_TIME_DESC : PUBLISH_TIME_DESC);

            GetProductsOptions options = new GetProductsOptions().setCurrencyCode(currencyCode);
            productFiltrationEngineSupport.fillFiltrationEngineOptions(options);

            if (slotPattern != null) {
                options.setWithSlots(true);
                options.setSlotPatternDTO(slotPattern);
            }

            return productFiltrationEngine
                    .getProductPage(spec, pageRequest, options)
                    .map(primaryPageProductDtoConverter::convertFrom);
        } catch (Throwable e) {
            log.error("su.reddot.domain.service.adminpanel.v2.AdminPanelProductService.getProductsForPrimaryPage", e);
            return Page.emptyPage();
        }
    }

    public PrimaryContentDTO<PrimaryPageProductDTO> getLastRecentlyViewProductsContentBlock(String currencyCode) {
        return getLastRecentlyViewProductsContentBlock(20, currencyCode, PrimaryContentType.RECENTLY_VIEW);
    }

    public PrimaryContentDTO<PrimaryPageProductDTO> getLastRecentlyViewProductsContentBlock(String currencyCode, PrimaryContentType contentType) {
        return getLastRecentlyViewProductsContentBlock(20, currencyCode, contentType);
    }

    @Transactional
    public PrimaryContentDTO<PrimaryPageProductDTO> getLastRecentlyViewProductsContentBlock(int count, String currencyCode, PrimaryContentType contentType) {
        try {
            List<ProductDTO> myLastSeenProducts = productService.getMyLastSeenProducts(count, currencyCode);
            if (myLastSeenProducts == null || myLastSeenProducts.isEmpty()) {
                return null;
            }
            PrimaryContentDTO<PrimaryPageProductDTO> content = new PrimaryContentDTO<>();
            content.setTitle(messageSourceAccessor.getMessage("service.PrimaryServiceImpl.RecentlyViewed"))
                    .setContentType(contentType)
                    .setId(PrimaryContentType.RECENTLY_VIEW.name())
                    .setContentList(primaryPageProductDtoConverter.convertFrom(myLastSeenProducts));
            return content;
        } catch (Throwable e) {
            log.error("su.reddot.domain.service.adminpanel.v2.AdminPanelProductService.getLastRecentlyViewProductsContentBlock", e);
        }
        return null;
    }

    public PrimaryContentDTO<PrimaryPageProductDTO> getOskellyChoiceContentBlock(GetOskellyChoiceContentBlockWrapperParams params)  {
        return self.getOskellyChoiceContentBlock(GetOskellyChoiceContentBlockParams.builder()
                .type(params.getType())
                .currencyCode(params.getCurrencyCode())
                .contentType(PrimaryContentType.SELECTION)
                .excludeCrossBorder(params.isExcludeCrossBorder())
                .build());
    }

    @Transactional
    public PrimaryContentDTO<PrimaryPageProductDTO> getOskellyChoiceContentBlock(GetOskellyChoiceContentBlockParams params) {
        PrimaryPageType type = params.getType();
        String currencyCode = params.getCurrencyCode();
        PrimaryContentType contentType = params.getContentType();
        try {
            FilterSpecification spec = makePPTypeSpecification(type);

            spec.state(PUBLISHED)
                            .hasOurChoice(true)
                            //13.08.2021 было решено исключать товары некоторых категорий(веток) в подборках новинок
                            //В частности, полностью исключаются товары из разделов beauty
                            .exceptCategoriesIds(indexPageProductsSetExceptCategoryIds)
                            .excludeCrossborder(params.isExcludeCrossBorder());

            GetProductsOptions options = new GetProductsOptions().setCurrencyCode(currencyCode);
            productFiltrationEngineSupport.fillFiltrationEngineOptions(options);

            ProductFiltrationPageRequest pageRequest = new ProductFiltrationPageRequest(
                    1,
                    OSKELLY_CHOICE_PRODUCT_COUNT,
                    PUBLISH_TIME_DESC);

            Page<ProductDTO> productPage = productFiltrationEngine
                    .getProductPage(spec, pageRequest, options);

            if (productPage == null || productPage.getItems() == null || productPage.getItems().isEmpty()) {
                return null;
            }

            String categoryName = type.getConfig().getCategory().getUrl();

            PrimaryContentDTO<PrimaryPageProductDTO> content = new PrimaryContentDTO<>();
            content.setTitle(messageSourceAccessor.getMessage("service.PrimaryServiceImpl.OurChoice"))
                    .setContentType(contentType)
                    .setId("oskelly_choice")
                    .setContentList(primaryPageProductDtoConverter.convertFromDtoList(productPage.getItems()))
                    .setAdditionalData(new PrimaryContentDTO.PrimaryContentAdditionalData()
                            .setCount(productPage.getTotalAmount())
                            .setJumpLink(deeplinkUtils.getOurChoiceCatalogLink(categoryName))
                            .setButtonTitle(messageSourceAccessor.getMessage("common.MoreProducts"))
                    );

            return content;
        } catch (Exception e) {
            log.error("su.reddot.domain.service.adminpanel.v2.AdminPanelProductService.getOskellyChoiceContentBlock", e);
        }
        return null;
    }

    @Transactional
    public PrimaryContentDTO<PrimaryPageProductDTO> getSelectedProductContentBlock(SelectedProductBlock.SelectedProduct selectedProduct) {
        ProductDTO productDTO = productService.getProductDTOCached(Long.parseLong(selectedProduct.getProductId()), ProductService.UserType.HUMAN);

        PrimaryContentDTO<PrimaryPageProductDTO> content = new PrimaryContentDTO<>();

        content.setContentType(PrimaryContentType.AD_PRODUCT)
                .setId(PrimaryContentType.AD_PRODUCT.name())
                .setContent(primaryPageProductDtoConverter.convertFrom(productDTO))
                .setAdditionalData(new PrimaryContentDTO.PrimaryContentAdditionalData().setDescription(selectedProduct.getDescription()));

        return content;
    }
}
