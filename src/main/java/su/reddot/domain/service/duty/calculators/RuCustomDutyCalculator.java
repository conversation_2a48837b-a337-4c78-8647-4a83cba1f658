package su.reddot.domain.service.duty.calculators;

import com.google.common.collect.ImmutableList;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.support.MessageSourceAccessor;
import su.reddot.domain.model.duty.OrderPositionDuty;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.service.currency.CurrencyRateService;
import su.reddot.domain.service.dto.CurrencyRateDTO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@NoArgsConstructor
@SuperBuilder
public class RuCustomDutyCalculator extends RuOskellyCommissionDutyCalculator {

    private CurrencyRateService currencyRateService;
    private MessageSourceAccessor messageSourceAccessor;

    private RuCustomDutyCalculatorParams params;

    @Override
    public List<OrderPositionDuty> flatCalculateOrderPositionDuties(OrderPosition orderPosition) {
        List<OrderPositionDuty> commisionDutiesLst = super.flatCalculateOrderPositionDuties(orderPosition);
        //
        Pair<BigDecimal, String> dutySubjectInfo = getSubjectToCustomsAmounts(orderPosition);
        BigDecimal dutyPercent = params.getCustomPercent().movePointLeft(2);
        BigDecimal taxableAmnt = dutySubjectInfo.getLeft();
        BigDecimal dutyAmounts = dutyPercent.multiply(taxableAmnt).setScale(0, RoundingMode.UP);
        OrderPositionDuty customDuty = new OrderPositionDuty()
                .setDutyCalculatorConfig(config)
                .setUpdateTime(LocalDateTime.now())
                .setType(OrderPositionDuty.Type.CUSTOM_DUTY)
                .setOrderPosition(orderPosition)
                .setSequence(1)
                .setIncludedInThePrice(false)
                .setAmount(dutyAmounts)
                .setHoldAmount(dutyAmounts)
                .setEffectiveAmount(dutyAmounts)
                .setPercent(dutyPercent)
                .setDescription(MessageFormat.format("{0} x {1} (AMOUNT) = {2} [(AMOUNT) {3}]", dutyPercent, taxableAmnt, dutyAmounts, dutySubjectInfo.getRight()));
        //
        return ImmutableList.<OrderPositionDuty>builder()
                .addAll(commisionDutiesLst)
                .add(customDuty)
                .build();
    }

    private Optional<BigDecimal> getRate4mOrderPosition(OrderPosition orderPosition, Long customCurrencyId) {
        if (Objects.isNull(customCurrencyId)) {
            return Optional.empty();
        }
        if (!Objects.equals(orderPosition.getForeignCurrencyId(), customCurrencyId)) {
            return Optional.empty();
        }
        return Optional.ofNullable(orderPosition.getForeignCurrencyRate());
    }

    private BigDecimal getRate4mRateService(Long customCurrencyId) {
        return currencyRateService.getAllRatesDtos().stream()
                .filter(it -> Objects.equals(it.getCurrencyId(), customCurrencyId))
                .findFirst()
                .map(CurrencyRateDTO::getRateValue)
                .orElseThrow(() -> new IllegalArgumentException("Custom duty calc: unable to find rate with code " + params.getCustomFreeCurrencyCode()));
    }

    public Pair<BigDecimal, String> getSubjectToCustomsAmounts(OrderPosition orderPosition) {
        BigDecimal itemAmount = orderPosition.getSellerPayoutAmountRaw();
        Long customCurrencyId = currencyRateService.getAllRates().stream()
                .filter(it -> Objects.equals(it.getCurrency().getIsoCode(), params.getCustomFreeCurrencyCode()))
                .findFirst()
                .map(it -> it.getCurrency().getId())
                .orElseThrow(() -> new IllegalArgumentException("Custom duty calc: unable to find rate with code " + params.getCustomFreeCurrencyCode()));
        //
        BigDecimal customFreeMaxAmount = params.getCustomFreeAmountLimits();
        BigDecimal customFreeRateValue = getRate4mOrderPosition(orderPosition, customCurrencyId)
                .orElseGet(() -> getRate4mRateService(customCurrencyId));
        BigDecimal customFreeMaxAmountBase = customFreeMaxAmount.multiply(customFreeRateValue);
        BigDecimal taxableCustomAmountBase = itemAmount.subtract(customFreeMaxAmountBase).max(BigDecimal.ZERO);
        String dutyDetailText = MessageFormat.format("{0} = MAX({1} - {2} ({3} {4} x {5}), 0)",
                taxableCustomAmountBase, itemAmount, customFreeMaxAmountBase,
                params.getCustomFreeAmountLimits(), params.getCustomFreeCurrencyCode(), customFreeRateValue, taxableCustomAmountBase);
        return Pair.of(taxableCustomAmountBase, dutyDetailText);
    }

    @Data
    public static class RuCustomDutyCalculatorParams implements DutyCalculator.DutyCalculatorParams {
        private String customFreeCurrencyCode;
        private BigDecimal customFreeAmountLimits;
        private BigDecimal customPercent;

        public boolean isValid() {
            return ObjectUtils.allNotNull(customFreeCurrencyCode, customFreeAmountLimits, customPercent);
        }

    }

}