package su.reddot.domain.service.primary.viewmapper.shelf;

import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.adminpanel.v2.common.BaseContentBlock;
import su.reddot.domain.model.adminpanel.v2.common.ContentBlockType;
import su.reddot.domain.model.adminpanel.v2.common.ShelfContentBlock;
import su.reddot.domain.service.adminpanel.v2.AdminPanelProductService;
import su.reddot.domain.service.adminpanel.v2.BannerSettingService;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.primary.PrimaryContentDTO;
import su.reddot.domain.service.dto.primary.PrimaryPageProductDTO;
import su.reddot.domain.service.dto.primary.converter.PrimaryPageProductDtoConverter;
import su.reddot.domain.service.dto.primary.enums.PrimaryContentType;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.primary.interfaces.PrimaryServiceReactor;
import su.reddot.presentation.adminpanel.v2.dto.bannersetting.CustomizableBannerSettingDto;
import su.reddot.presentation.adminpanel.v2.dto.segment.BaseContentBlockSegmentDto;
import su.reddot.presentation.adminpanel.v2.dto.segment.property.ShelfSegmentPropertiesDto;

import java.util.Collections;
import java.util.List;

@Component
public class ShelfContentBlockToViewMapper extends AbstractShelfContentBlockToViewMapper {

    private final PrimaryPageProductDtoConverter primaryPageProductDtoConverter;
    private final MessageSourceAccessor messageSourceAccessor;

    public ShelfContentBlockToViewMapper(MicrometerService micrometerService,
                                         BannerSettingService<CustomizableBannerSettingDto> bannerSettingService,
                                         AdminPanelProductService adminPanelProductService,
                                         PrimaryPageProductDtoConverter primaryPageProductDtoConverter,
                                         MessageSourceAccessor messageSourceAccessor) {
        super(micrometerService, bannerSettingService, adminPanelProductService);
        this.primaryPageProductDtoConverter = primaryPageProductDtoConverter;
        this.messageSourceAccessor = messageSourceAccessor;
    }

    @Override
    public boolean isApplicable(BaseContentBlock fromBlock, PrimaryServiceReactor.GetPrimaryPageDataContext context) {
        if (context.getBlockListVersion() < 2) return false;
        if (ContentBlockType.SHELF.equals(fromBlock.getType())) {
            ShelfContentBlock shelfContentBlock = (ShelfContentBlock) fromBlock;
            return !isShelfBlockWithBannerInformation(shelfContentBlock, context) &&
                    shelfContentBlock.isEnabledForSegment(context.getSegmentDtoList());
        }
        return false;
    }

    @Override
    protected List<PrimaryContentDTO<?>> mapToPrimaryContentDTO(ShelfContentBlock fromBlock,
                                                                CustomizableBannerSettingDto bannerSettingDto,
                                                                List<ProductDTO> products,
                                                                PrimaryServiceReactor.GetPrimaryPageDataContext context) {
        Boolean isSellerHidden = fromBlock.getSegmentByDTOs(context.getSegmentDtoList())
                .map(BaseContentBlockSegmentDto::getProperties)
                .map(ShelfSegmentPropertiesDto::getIsSellerHidden)
                .orElse(false);
        PrimaryContentDTO<PrimaryPageProductDTO> result = new PrimaryContentDTO<>();
        result
                .setId(fromBlock.getBlockId())
                .setContentType(PrimaryContentType.H_SELECTION)
                .setTitle(fromBlock.getTitleBySegmentDTO(context.getSegmentDtoList()))
                .setContentList(primaryPageProductDtoConverter.convertFromDtoList(products))
                .setAdditionalData(new PrimaryContentDTO.PrimaryContentAdditionalData()
                        .setJumpLink(bannerSettingDto.getBannerDeeplink())
                        .setButtonTitle(messageSourceAccessor.getMessage("common.MoreProducts"))
                        .setIsSellerHidden(isSellerHidden)
                );
        return Collections.singletonList(result);
    }
}
