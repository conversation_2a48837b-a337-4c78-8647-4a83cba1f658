package su.reddot.domain.service.primary.viewmapper;

import org.apache.groovy.util.Maps;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Component;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.adminpanel.v2.BannerSetting;
import su.reddot.domain.model.adminpanel.v2.common.BaseContentBlock;
import su.reddot.domain.model.adminpanel.v2.common.ContentBlockType;
import su.reddot.domain.model.adminpanel.v2.common.FilterableShelfContentBlock;
import su.reddot.domain.model.adminpanel.v2.filter.AbstractFilter;
import su.reddot.domain.service.adminpanel.v2.AdminPanelProductService;
import su.reddot.domain.service.adminpanel.v2.BannerSettingService;
import su.reddot.domain.service.dto.primary.FilterDTO;
import su.reddot.domain.service.dto.primary.PrimaryContentDTO;
import su.reddot.domain.service.dto.primary.PrimaryPageProductDTO;
import su.reddot.domain.service.dto.primary.ShelfWithFiltersDTO;
import su.reddot.domain.service.dto.primary.converter.PrimaryPageProductDtoConverter;
import su.reddot.domain.service.dto.primary.enums.PrimaryContentType;
import su.reddot.domain.service.filter.processor.filter.impl.BrandFilterProcessor;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.domain.service.primary.interfaces.PrimaryServiceReactor;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.product.ProductService.FilterSpecification;
import su.reddot.presentation.adminpanel.v2.dto.bannersetting.BannerSettingDto;
import su.reddot.presentation.adminpanel.v2.dto.bannersetting.CustomizableBannerSettingDto;
import su.reddot.presentation.adminpanel.v2.dto.converter.BannerSettingDtoConverter;
import su.reddot.presentation.adminpanel.v2.dto.segment.BaseContentBlockSegmentDto;
import su.reddot.presentation.adminpanel.v2.dto.segment.property.FilterableShelfSegmentPropertiesDto;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class FilterableShelfContentBlockToViewMapper extends AbstractContentBlockToViewMapper<FilterableShelfContentBlock> {

    private static final int DEFAULT_PAGE_SIZE = 10;
    private static final int BIG_PAGE_SIZE = 20;
    private static final int DEFAULT_BRAND_PAGE_SIZE = 20;

    private final AdminPanelProductService adminPanelProductService;
    private final BannerSettingService<CustomizableBannerSettingDto> bannerSettingService;
    private final ProductService productService;
    private final PrimaryPageProductDtoConverter primaryPageProductDtoConverter;
    private final BannerSettingDtoConverter bannerSettingDtoConverter;
    private final MessageSourceAccessor messageSourceAccessor;

    public FilterableShelfContentBlockToViewMapper(MicrometerService micrometerService,
                                                   AdminPanelProductService adminPanelProductService,
                                                   BannerSettingService<CustomizableBannerSettingDto> bannerSettingService,
                                                   ProductService productService,
                                                   PrimaryPageProductDtoConverter primaryPageProductDtoConverter,
                                                   BannerSettingDtoConverter bannerSettingDtoConverter,
                                                   MessageSourceAccessor messageSourceAccessor) {
        super(micrometerService);
        this.adminPanelProductService = adminPanelProductService;
        this.bannerSettingService = bannerSettingService;
        this.productService = productService;
        this.primaryPageProductDtoConverter = primaryPageProductDtoConverter;
        this.bannerSettingDtoConverter = bannerSettingDtoConverter;
        this.messageSourceAccessor = messageSourceAccessor;
    }

    @Override
    public boolean isApplicable(BaseContentBlock fromBlock, PrimaryServiceReactor.GetPrimaryPageDataContext context) {
        if (context.getBlockListVersion() < 2) return false;
        return ContentBlockType.FILTERABLE_SHELF.equals(fromBlock.getType()) && fromBlock.isEnabledForSegment(context.getSegmentDtoList());
    }

    @Override
    protected FilterableShelfContentBlock castToTargetClass(BaseContentBlock fromBlock) {
        return (FilterableShelfContentBlock) fromBlock;
    }

    @Override
    protected List<PrimaryContentDTO<?>> mapBlockToView(FilterableShelfContentBlock fromBlock,
                                                        PrimaryServiceReactor.GetPrimaryPageDataContext context,
                                                        PrimaryServiceReactor.GetPrimaryPageDataState state) {
        String bannerSettingId = fromBlock
                .getContentBySegmentDTO(context.getSegmentDtoList())
                .stream()
                .findFirst()
                .orElse(null);

        BannerSetting<AbstractFilter> bannerSetting = bannerSettingService.getRawBannerSetting(bannerSettingId);

        FilterSpecification spec = adminPanelProductService.mapToFilterSpecOrDefault(bannerSetting.getFilter());

        FilterDTO filterDTO = new FilterDTO();
        bannerSetting.getFilter().fillFilter(filterDTO);

        Boolean withFilterOptions = fromBlock.getSegmentByDTOs(context.getSegmentDtoList())
                .map(BaseContentBlockSegmentDto::getProperties)
                .map(FilterableShelfSegmentPropertiesDto::getWithFilterOptions)
                .orElse(false);
        ShelfWithFiltersDTO shelfWithFiltersDTO = new ShelfWithFiltersDTO();
        shelfWithFiltersDTO.setPresetFilter(filterDTO);
        shelfWithFiltersDTO.setWithFilterOptions(withFilterOptions);

        List<ShelfWithFiltersDTO.OptionDTO> optionDTOS;

        Boolean isWithFilterOptions = fromBlock.getSegmentByDTOs(context.getSegmentDtoList())
                .map(BaseContentBlockSegmentDto::getProperties)
                .map(FilterableShelfSegmentPropertiesDto::getWithFilterOptions)
                .orElse(Boolean.FALSE);

        if (isWithFilterOptions) {
            optionDTOS = getOptionsWithFilters(spec, context);
        } else {
            optionDTOS = getOptionsWithoutFilters(spec, context);
        }
        shelfWithFiltersDTO.setOptions(optionDTOS);

        BannerSettingDto bannerSettingDto = bannerSettingDtoConverter.convertTo(bannerSetting);
        return Collections.singletonList(new PrimaryContentDTO<ShelfWithFiltersDTO>()
                .setId(fromBlock.getBlockId())
                .setSegmentId(fromBlock.getSegmentIdByDTOs(context.getSegmentDtoList()))
                .setTitle(fromBlock.getTitleBySegmentDTO(context.getSegmentDtoList()))
                .setContentType(PrimaryContentType.FILTERABLE_SHELF)
                .setContent(shelfWithFiltersDTO)
                .setAdditionalData(new PrimaryContentDTO.PrimaryContentAdditionalData()
                        .setJumpLink(bannerSettingDto.getBannerDeeplink())
                        .setButtonTitle(messageSourceAccessor.getMessage("common.SeeMoreProducts"))
                )
        );
    }

    private List<ShelfWithFiltersDTO.OptionDTO> getOptionsWithFilters(FilterSpecification spec,
                                                                      PrimaryServiceReactor.GetPrimaryPageDataContext context) {
        List<Brand> brands = productService
                .getAvailableBrands(spec, 1, DEFAULT_BRAND_PAGE_SIZE, ProductService.UserType.HUMAN)
                .getItems();

        List<ShelfWithFiltersDTO.OptionDTO> optionDTOS = new ArrayList<>();
        for (int i = 0; i < brands.size(); i++) {

            Long brandId = brands.get(i).getId();
            String brandName = brands.get(i).getName();
            ShelfWithFiltersDTO.OptionDTO optionDTO = new ShelfWithFiltersDTO.OptionDTO()
                    .setTitle(brandName)
                    .setFilter(Maps.of(BrandFilterProcessor.FILTER_CODE, Collections.singletonList(brandId)));

            if (i == 0) {
                spec.interestingBrands(Collections.singletonList(brandId));
                optionDTO.setContents(getProductsBySpecAndContext(spec, context, DEFAULT_PAGE_SIZE));
            }
            optionDTOS.add(optionDTO);
        }
        return optionDTOS;
    }

    private List<ShelfWithFiltersDTO.OptionDTO> getOptionsWithoutFilters(FilterSpecification spec,
                                                                      PrimaryServiceReactor.GetPrimaryPageDataContext context) {
        ShelfWithFiltersDTO.OptionDTO optionDTO = new ShelfWithFiltersDTO.OptionDTO()
                .setContents(getProductsBySpecAndContext(spec, context, BIG_PAGE_SIZE));

        return Collections.singletonList(optionDTO);
    }

    private List<PrimaryContentDTO<?>> getProductsBySpecAndContext(FilterSpecification spec,
                                                                   PrimaryServiceReactor.GetPrimaryPageDataContext context,
                                                                   Integer pageSize) {
        return adminPanelProductService.getProductListPageable(spec, context.getCurrencyCode(), 0, pageSize)
                .getItems()
                .stream()
                .map(primaryPageProductDtoConverter::convertFrom)
                .map(it -> new PrimaryContentDTO<PrimaryPageProductDTO>()
                        .setContent(it)
                        .setContentType(PrimaryContentType.PRODUCT))
                .collect(Collectors.toList());
    }
}
