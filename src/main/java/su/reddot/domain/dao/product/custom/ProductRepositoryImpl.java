package su.reddot.domain.dao.product.custom;

import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import su.reddot.domain.dao.AbstractCustomRepository;
import su.reddot.domain.model.Brand;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.QProduct;
import su.reddot.domain.model.product.QProductAttributeValueBinding;
import su.reddot.domain.model.product.QProductItem;

import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static su.reddot.domain.model.product.QProductTagBinding.productTagBinding;

/**
 * Использует querydsl left join для построения запроса по связанным сущностям Product, ProductItem.
 */

public class ProductRepositoryImpl extends AbstractCustomRepository<Product> implements ProductRepositoryCustom {

    public ProductRepositoryImpl(@Autowired @Qualifier("mainEntityManager") EntityManager entityManager) {
        super(entityManager, Product.class);
    }

    @Override
    public Page<Product> findProducts(Predicate predicate, Pageable page, boolean joinProductItem,
                                      List<String> joinedAttributes, boolean joinTagBindings) {
        return readPage(getProductsQuery(predicate, joinProductItem, joinedAttributes, joinTagBindings), page);
    }

    @Override
    public Page<Long> findProductIds(Predicate predicate, Pageable page, boolean joinProductItem,
                                     List<String> joinedAttributes, boolean joinTagBindings){
        return readPage(getProductIdsQuery(predicate, joinProductItem, joinedAttributes, joinTagBindings), page);
    }

    @Override
    public long countProducts(Predicate predicate, boolean joinProductItem,
                              List<String> joinedAttributes, boolean joinTagBindings){
        return count(getProductsQuery(predicate, joinProductItem, joinedAttributes, joinTagBindings));
    }

    @Override
    public List<Long> findProductIds(Predicate predicate, boolean joinProductItem,
                                     List<String> joinedAttributes, boolean joinTagBindings){
        return read(getProductIdsQuery(predicate, joinProductItem, joinedAttributes, joinTagBindings), null);
    }

    public Page<Product> findProducts(BooleanBuilder predicate, Pageable page) {
        QProduct product = QProduct.product;

        JPAQuery<Product> result = new JPAQuery<Product>(getEntityManager()).from(product);

        return readPage(result.where(predicate), page);
    }


    public List<Product> findProductsList(BooleanBuilder predicate, Pageable page) {
        QProduct product = QProduct.product;

        JPAQuery<Product> result = new JPAQuery<Product>(getEntityManager()).from(product);

        return readPagedList(result.where(predicate), page);
    }

    @Override
    public Map<Long, List<Long>> findProductIdsGroupedByBrandIds(Predicate predicate, Sort sort, int productsCount, boolean joinProductItem,
                                                                 List<String> joinedAttributes, boolean joinProductTagBindings){
        QProduct product = QProduct.product;
        QProductItem productItem = QProductItem.productItem;

        JPAQuery<Tuple> query = new JPAQuery<Long>(getEntityManager()).select(product.id, product.brand.id).from(product);
        joinTables(product, query, joinProductItem, joinedAttributes, joinProductTagBindings);
        query = query.where(predicate).groupBy(product.id);

        List<Tuple> rows = read(query, sort);

        Map<Long, List<Long>> result = new HashMap<>();
        //Все идентификаторы добавленных в мап товаров будут лежать здесь
        List<Long> allProductIds = new ArrayList<>();
        result.put(null, allProductIds);

        for(Tuple t : rows){
            Long productId = t.get(0, Long.class);
            Long brandId = t.get(1, Long.class);
            List<Long> productIds = result.get(brandId);
            if(productIds == null){
                productIds = new ArrayList<>();
                result.put(brandId, productIds);
            }
            if(productIds.size() == productsCount) continue;
            productIds.add(productId);
            allProductIds.add(productId);
        }

        return result;
    }

    @Override
    public Map<ProductState, Integer> findProductCountsGroupedByProductState(Predicate predicate, boolean joinProductItem,
                                                                             List<String> joinedAttributes, boolean joinProductTagBindings) {
        QProduct product = QProduct.product;
        QProductItem productItem = QProductItem.productItem;

        JPAQuery<Tuple> query = new JPAQuery<Long>(getEntityManager()).select(product.productState, product.id.countDistinct()).from(product);
        joinTables(product, query, joinProductItem, joinedAttributes, joinProductTagBindings);

        query = query.where(predicate).groupBy(product.productState);

        List<Tuple> rows = read(query, null);

        Map<ProductState, Integer> result = new HashMap<>();

        for (Tuple t : rows) {
            ProductState productState = t.get(0, ProductState.class);
            Integer count = t.get(1, Long.class).intValue();
            result.put(productState, count);
        }

        return result;
    }


    @Override
    public Map<Long, Integer> findProductCountsGroupedByBrandIds(Predicate predicate, boolean joinProductItem,
                                                                 List<String> joinedAttributes, boolean joinProductTagBindings){
        QProduct product = QProduct.product;

        JPAQuery<Tuple> query = new JPAQuery<Long>(getEntityManager()).select(product.brand.id, product.id.countDistinct()).from(product);
        joinTables(product, query, joinProductItem, joinedAttributes, joinProductTagBindings);
        query = query.where(predicate).groupBy(product.brand.id);

        List<Tuple> rows = read(query, null);

        Map<Long, Integer> result = new HashMap<>();

        for(Tuple t : rows){
            Long brandId = t.get(0, Long.class);
            Integer count = t.get(1, Long.class).intValue();
            result.put(brandId, count);
        }

        return result;
    }

    @Override
    public Page<Brand> findBrands(Predicate predicate, Pageable p, boolean joinProductItem,
                                  List<String> joinedAttributes, boolean joinProductTagBindings) {
        return readPage(getBrandsQuery(predicate, joinProductItem, joinedAttributes, joinProductTagBindings), p);
    }

    @Override
    public long countBrands(Predicate predicate, boolean joinProductItem,
                            List<String> joinedAttributes, boolean joinProductTagBindings){
        return count(getBrandsQuery(predicate, joinProductItem, joinedAttributes, joinProductTagBindings));
    }

    private JPAQuery<Product> getProductsQuery(
            Predicate predicate,
            boolean joinProductItem,
            List<String> joinedAttributes,
            boolean joinTagBindings
    ){
        QProduct product = QProduct.product;
        JPAQuery<Product> result = new JPAQuery<Product>(getEntityManager()).from(product);
        joinTables(product, result, joinProductItem, joinedAttributes, joinTagBindings);
        return result.where(predicate).groupBy(product.id);
    }

    private JPAQuery<Long> getProductIdsQuery(Predicate predicate, boolean joinProductItem,
                                              List<String> joinedAttributes, boolean joinTagBindings){
        QProduct product = QProduct.product;
        JPAQuery<Long> result = new JPAQuery<Long>(getEntityManager()).select(product.id).from(product);
        joinTables(product, result, joinProductItem, joinedAttributes, joinTagBindings);
        return result.where(predicate).groupBy(product.id);
    }


    private JPAQuery<Brand> getBrandsQuery(Predicate predicate, boolean joinProductItem,
                                           List<String> joinedAttributes, boolean joinProductTagBindings){
        QProduct product = QProduct.product;
        JPAQuery<Brand> result =  new JPAQuery<Brand>(getEntityManager()).select(product.brand).from(product);
        joinTables(product, result, joinProductItem, joinedAttributes, joinProductTagBindings);
        // всегда исключать скрытые
        BooleanBuilder filterExpression = new BooleanBuilder();
        if (predicate != null) {
            filterExpression.and(predicate);
        }
        filterExpression.and(product.brand.isHidden.isFalse());
        return result.where(filterExpression).distinct();
    }

    private <T> void joinTables(QProduct product,
            JPAQuery<T> query,
            boolean joinProductItem,
            List<String> joinedAttributes,
            boolean joinTagBindings
    ) {
        if (joinProductItem) {
            QProductItem productItem = QProductItem.productItem;
            query = query.leftJoin(product.productItems, productItem);
        }
        if (CollectionUtils.isNotEmpty(joinedAttributes)) {
            for (String joinedAttribute : joinedAttributes) {
                query = query.leftJoin(product.attributeValues, new QProductAttributeValueBinding(joinedAttribute));
            }
        }
        if (joinTagBindings) {
            query = query.leftJoin(product.tagBindings, productTagBinding);
        }
    }
}
