package su.reddot.infrastructure.bank.payoutpart.swiftpeople;

import swift.people.models.CreateLead200Response;
import swift.people.models.CreateLeadRequest;
import swift.people.models.CreatePaymentRequestRequest;
import swift.people.models.StatusResponse;
import swift.people.models.UpdatePaymentRequest200Response;
import swift.people.models.UpdatePaymentRequestRequest;
import swift.people.models.UpdatePaymentRequestRequestInvoiceFilesInner;
import swift.people.models.UploadFiles200Response;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import su.reddot.domain.exception.PayoutRequestException;
import su.reddot.domain.model.agentreport.AgentReport;
import su.reddot.domain.model.banktransaction.BankPayment;
import su.reddot.domain.model.banktransaction.DestinationType;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderFileTypeName;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.payoutrequest.PayoutRequest;
import su.reddot.domain.model.payoutrequest.PayoutRequestAgentName;
import su.reddot.domain.model.payoutrequest.PayoutRequestStatus;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.user.UserContract;
import su.reddot.domain.service.dto.BankPaymentDTO;
import su.reddot.domain.service.dto.UpdatePayoutRequestStatusDTO;
import su.reddot.domain.service.dto.order.orderfile.OrderFileParams;
import su.reddot.domain.service.orderfile.OrderFileService;
import su.reddot.domain.service.user.contract.UserContractService;
import su.reddot.infrastructure.bank.commons.BankCommons;
import su.reddot.infrastructure.bank.impl.swiftpeople.SwiftPeopleExtendedApi;
import su.reddot.infrastructure.bank.impl.swiftpeople.SwiftPeopleProperties;
import su.reddot.infrastructure.bank.payoutpart.BasePayoutService;
import su.reddot.infrastructure.bank.payoutpart.swiftpeople.dto.SwiftPeoplePaymentRequestStatus;
import su.reddot.infrastructure.bank.payoutpart.swiftpeople.dto.SwiftPeoplePayoutInvoice;
import su.reddot.infrastructure.bank.payoutpart.swiftpeople.dto.SwiftPeoplePayoutInvoiceItem;
import su.reddot.infrastructure.bank.payoutpart.swiftpeople.dto.SwiftPeoplePayoutInvoiceReportData;

/**
 * Сервис покрывает работу с API Swift People
 * документация: dev http://87.228.8.227/api/v1/documentation#/
 * prod https://swiftpeople.finance/api/v1/documentation
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class SwiftPeoplePayoutService extends BasePayoutService {

	private final SwiftPeopleExtendedApi client;
	private final SwiftPeopleProperties swiftPeopleProperties;
	private final ObjectMapper objectMapper;
	private final TransactionTemplate transactionTemplate;
	private final OrderFileService orderFileService;
	private final UserContractService userContractService;

	public BankPaymentDTO taskTransferMoneyToSeller(@NonNull AgentReport agentReport, String bankName) {
		Order order = agentReport.getOrder();
		log.info("Оформление запроса на выплату продавцу (transferMoneyToSeller, заказ: {}, отчет агента: {})", order.getId(), agentReport.getId());
		//
		BankPaymentDTO bankPaymentDTO = prepareTransferMoneyToSeller(agentReport, bankName, false, true);
		if (Objects.isNull(bankPaymentDTO)) {
			return null;
		}
		if (TransactionState.PREPARED != bankPaymentDTO.getPaymentState()) {
			return bankPaymentDTO;
		}
		if (OrderState.MONEY_PAYMENT_TECHNICAL_ERROR == bankPaymentDTO.getOrderState()) {
			return null;
		}
		//
		UpdatePaymentRequest200Response paymentResp = null;
		UploadFiles200Response fileResp = null;
		try {
			UpdatePaymentRequestRequest request = getUpdatePaymentRequest(order);

			List<Resource> invoiceFiles = getInvoiceFiles(order.getId());
			if (CollectionUtils.isEmpty(invoiceFiles)) {
				log.error("Order {}: SwiftPeoplePayoutService get invoice failed", order.getId());
				return null;
			}

			Resource userContract = getBoutiqueContract(order);
			if (Objects.isNull(userContract)) {
				log.error("Order {}: SwiftPeoplePayoutService get contract failed", order.getId());
				return null;
			}

			for (Resource invoice : invoiceFiles) {
				fileResp = client.uploadFilesAsByteArray(invoice);
				if (Objects.nonNull(fileResp)
						&& Objects.nonNull(fileResp.getResult())
						&& CollectionUtils.isNotEmpty(fileResp.getResult().getFiles())
						&& client.isSuccess(fileResp.getErrorInfo())) {
					String swiftPeopleFileId = fileResp.getResult().getFiles().get(0).getId();
					request.addInvoiceFilesItem(new UpdatePaymentRequestRequestInvoiceFilesInner().id(swiftPeopleFileId));
					log.info("Order {}: SwiftPeople upload invoice file {}", order.getId(), invoice.getFilename());
				} else {
					log.error("Order {}: SwiftPeople upload file invoice failed. System has not found file_id in response. Response: {}", order.getId(), fileResp);
					return null;
				}
			}

			fileResp = client.uploadFilesAsByteArray(userContract);
			if (Objects.nonNull(fileResp)
					&& Objects.nonNull(fileResp.getResult())
					&& CollectionUtils.isNotEmpty(fileResp.getResult().getFiles())
					&& client.isSuccess(fileResp.getErrorInfo())) {
				String swiftPeopleFileId = fileResp.getResult().getFiles().get(0).getId();
				request.addContractFilesItem(new UpdatePaymentRequestRequestInvoiceFilesInner().id(swiftPeopleFileId));
				log.info("Order {}: SwiftPeople upload contract file {}", order.getId(), userContract.getFilename());
			} else {
				log.error("Order {}: SwiftPeople upload file contract failed. System has not found file_id in response. Response: {}", order.getId(), fileResp);
				return null;
			}

			paymentResp = client.leadToPaymentRequest(bankPaymentDTO.getUuid().toString(), request);
			if (Objects.nonNull(paymentResp)
					&& Objects.nonNull(paymentResp.getResult())
					&& Objects.nonNull(paymentResp.getResult().getPaymentRequest())
					&& Objects.nonNull(paymentResp.getResult().getPaymentRequest().getId2())
					&& bankPaymentDTO.getUuid().toString().equals(paymentResp.getResult().getPaymentRequest().getId2())
					&& client.isSuccess(paymentResp.getErrorInfo())) {
				bankPaymentDTO.setBankTransactionId(paymentResp.getResult().getPaymentRequest().getId2());
			} else {
				log.error("Order {}: SwiftPeople payment request success. System has not found valid id2 in response. Response {}", order.getId(), paymentResp);
				return null;
			}

			bankPaymentDTO
					.setPaymentState(TransactionState.INPROGRESS)
					.setOrderState(OrderState.MONEY_PAYMENT_WAIT);

			log.info("Оформление запроса на выплату продавцу (заказ {}, отчет агента {}): создан запрос {}", order.getId(), agentReport.getId(), bankPaymentDTO.getPaymentId());
		} catch (Exception e) {
			log.error("Order {}: SwiftPeople create payment request failed, {}", order.getId(), e.getMessage(), e);
			return bankPaymentDTO.setOrderState(OrderState.MONEY_PAYMENT_TECHNICAL_ERROR);
		}
		return bankPaymentDTO;
	}

	@SneakyThrows(JsonProcessingException.class)
	public BankPaymentDTO checkOrderStateForBankPayment(BankPayment payment) {
		Order order = orderService.getOrder(payment.getAgentReport().getOrder().getId());
		//
		if (payment.getAmount().compareTo(BigDecimal.ZERO) == 0) {
			BankPaymentDTO bankPaymentDTO = new BankPaymentDTO()
					.setPaymentVersion(order.getPaymentVersion())
					.setPaymentId(payment.getId())
					.setOrderId(order.getId())
					.setBank(payment.getBank())
					.setBankTransactionId(payment.getBankTransactionId())
					.setPaymentSystemTimeFormatted(ZonedDateTime.now(clock));

			bankPaymentDTO.setPaymentState(TransactionState.DONE);
			bankPaymentDTO.setFee(0L);
			bankPaymentDTO.setAmountSentViaApi(0L);
			bankPaymentDTO.setOriginalCurrencyCode(payment.getOriginalCurrencyCode());
			bankPaymentDTO.setOriginalCurrencyAmount(0L);
			bankPaymentDTO.setOrderState(OrderState.COMPLETED);
			//
			return bankPaymentDTO;
		}

		StatusResponse statusResp = null;
		try {
			statusResp = client.getPaymentRequestStatus(payment.getBankTransactionId());
			if (Objects.isNull(statusResp) || Objects.isNull(statusResp.getResult()) || Objects.isNull(statusResp.getResult().getCode())) {
				log.warn("Для заказа {} невозможно получит статус от банка SwiftPeople BankOperation#uuid = {}", order.getId(), payment.getUuid());
				return null;
			}
		} catch (RestClientException e) {
			log.error("Order {}: SwiftPeople check payout status error", order.getId(), e);
			return null;
		}

		BankPaymentDTO bankPaymentDTO = new BankPaymentDTO()
				.setPaymentVersion(order.getPaymentVersion())
				.setPaymentId(payment.getId())
				.setOrderId(order.getId())
				.setBank(payment.getBank())
				.setBankTransactionId(payment.getBankTransactionId())
				.setPaymentSystemCode(statusResp.getResult().getCode())
				.setPaymentSystemTimeFormatted(ZonedDateTime.now(clock))
				.setRawResponse(objectMapper.writeValueAsString(statusResp));

		if (SwiftPeoplePaymentRequestStatus.DONE.name().equals(statusResp.getResult().getCode())) {
			log.info("Для заказа {} операция перевода денег с SwiftPeople расчетного счета c номером заявки {} - выполнена",
					order.getId(), payment.getUuid());

			bankPaymentDTO.setPaymentState(TransactionState.DONE);
			bankPaymentDTO.setFee(0L);
			bankPaymentDTO.setAmountSentViaApi(BankCommons.toAmountInCents(payment.getAmount()));
			bankPaymentDTO.setOriginalCurrencyCode(payment.getOriginalCurrencyCode());
			bankPaymentDTO.setOriginalCurrencyAmount(BankCommons.toAmountInCents(payment.getOriginalCurrencyAmount()));
			bankPaymentDTO.setOrderState(OrderState.COMPLETED);
		} else if (SwiftPeoplePaymentRequestStatus.REFUND.name().equals(statusResp.getResult().getCode())) {
			log.info("Для заказа {} операция перевода денег с SwiftPeople расчетного счета c номером заявки {} - отменена",
					order.getId(), payment.getUuid());
			bankPaymentDTO.setPaymentState(TransactionState.CANCELED);

			if (payment.getDestination() == DestinationType.SELLER) {
				bankPaymentDTO.setOrderState(OrderState.MONEY_PAYMENT_ERROR);
			}
		} else {
			log.info("Для заказа {} операция перевода денег с SwiftPeople расчетного счета c номером заявки {} - в процессе",
					order.getId(), payment.getUuid());
			return null;
		}
		return bankPaymentDTO;
	}

	//Будет удален когда осуществим переход на резерв + обновление платежных реквизитов
	@Deprecated
	private CreatePaymentRequestRequest getPaymentRequest(Order order, BankPaymentDTO bankPaymentDTO) {
		AgentReport agentReport = order.getAgentReport();

		CreatePaymentRequestRequest request = new CreatePaymentRequestRequest();
		request.setId2(bankPaymentDTO.getUuid().toString());
		//id Oskelly в система платежного агента
		request.setOrganisationId(swiftPeopleProperties.getOrganisationId());
		//Валюта коммуникации Oskelly<-->SwiftPeople = RUB
		request.setPaymentMethod(swiftPeopleProperties.getBaseCurrency());

		//временное решение по устранению остатка
		request.setAmount(agentReport.getOriginalCurrencyAmount().setScale(2, RoundingMode.HALF_UP));
		request.setCurrency(agentReport.getOriginalCurrency().getIsoCode());

		request.setCompanyName(agentReport.getCompanyName());
		request.setCompanyAddress(agentReport.getCompanyAddress());
		request.setDirectorName(agentReport.getDirectorName());
		request.setBankName(agentReport.getBankName());
		request.setBankAddress(agentReport.getBankAddress());
		request.setSwiftCode(agentReport.getSwiftCode());
		request.setIban(agentReport.getIban());
		request.setUnifiedSocialCreditCode(agentReport.getUnifiedSocialCreditCode());
		request.setBusinessRegistrationNumber(agentReport.getBusinessRegistrationNumber());

		return request;
	}

	private UpdatePaymentRequestRequest getUpdatePaymentRequest(Order order) {
		AgentReport agentReport = order.getAgentReport();

		UpdatePaymentRequestRequest request = new UpdatePaymentRequestRequest();
		//Валюта коммуникации Oskelly<-->SwiftPeople = RUB
		request.setPaymentMethod(swiftPeopleProperties.getBaseCurrency());

		//временное решение по устранению остатка
		request.setAmount(agentReport.getOriginalCurrencyAmount().setScale(2, RoundingMode.HALF_UP));
		request.setCurrency(agentReport.getOriginalCurrency().getIsoCode());

		request.setCompanyName(agentReport.getCompanyName());
		request.setCompanyAddress(agentReport.getCompanyAddress());
		request.setDirectorName(agentReport.getDirectorName());
		request.setBankName(agentReport.getBankName());
		request.setBankAddress(agentReport.getBankAddress());
		request.setSwiftCode(agentReport.getSwiftCode());
		request.setIban(agentReport.getIban());
		request.setUnifiedSocialCreditCode(agentReport.getUnifiedSocialCreditCode());
		request.setBusinessRegistrationNumber(agentReport.getBusinessRegistrationNumber());

		return request;
	}

	private List<Resource> getInvoiceFiles(Long orderId) {
		try {
			//SEND EMPTY FILE
//			ByteArrayResource resource = new ByteArrayResource(new byte[0]) {
//				@Override
//				public String getFilename() {
//					return String.format("empty_oskelly_invoice_%d.xlsx", orderId);
//				}
//			};
//			return Collections.singletonList(resource);

			return orderFileService
					.getOrderFileRawResources(OrderFileParams.builder()
							.orderIdList(Collections.singletonList(orderId))
							.orderFileTypeNameList(Collections.singletonList(OrderFileTypeName.ORDER_INVOICE))
							.build())
					.stream()
					.map(r ->
							new ByteArrayResource(r.getContent()) {
								@Override
								public String getFilename() {
									return r.getFilename();
								}
							})
					.collect(Collectors.toList());
		} catch (Exception ex) {
			log.error("Order {}: Exception occurs during create swift people payout invoice file", orderId, ex);
			return null;
		}
	}

	private Resource getBoutiqueContract(Order order) {
		try {
			if (Objects.nonNull(order.getSeller()) && CollectionUtils.isNotEmpty(order.getSeller().getUserContracts())) {
				if (order.getSeller().getUserContracts().size() > 1) {
					log.error("Order {}: seller {} has more than 1 contract!", order.getId(), order.getSeller().getId());
					return null;
				}
				//must be only ONE
				UserContract contract = order.getSeller().getUserContracts().get(0);
				return Optional.ofNullable(userContractService.getContractRawResource(contract))
						.map(r ->
								new ByteArrayResource(r.getContent()) {
									@Override
									public String getFilename() {
										return r.getFilename();
									}
								})
						.orElse(null);
			}
			return null;
		} catch (Exception ex) {
			log.error("Order {}: Exception occurs during obtain swift people payout contract file", order.getId(), ex);
			return null;
		}
	}

	private SwiftPeoplePayoutInvoiceReportData getSwiftPeopleInvoice(Order order) {
		AgentReport agentReport = order.getAgentReport();
		List<OrderPosition> positions = order.getConfirmedOrderPositions();

		SwiftPeoplePayoutInvoice invoice = new SwiftPeoplePayoutInvoice();
		invoice.setInvoiceDate(agentReport.getCreateTime().toLocalDate().toString());
		invoice.setInvoiceNo(order.getId());

		invoice.setPayeeAccountName(agentReport.getCompanyName());
		invoice.setCompanyAddress(agentReport.getCompanyAddress());
		invoice.setBankName(agentReport.getBankName());
		invoice.setBankAddress(agentReport.getBankAddress());
		invoice.setSwiftCode(agentReport.getSwiftCode());
		invoice.setIban(agentReport.getIban());

		invoice.setBillToCompanyName(swiftPeopleProperties.getInvoice().getBillTo());
		invoice.setShipToAddress(swiftPeopleProperties.getInvoice().getShipToAddress());
		invoice.setBillToAddress(swiftPeopleProperties.getInvoice().getBillToAddress());

		List<SwiftPeoplePayoutInvoiceItem> list = new ArrayList<>();
		invoice.setList(list);
		invoice.setListUnitTotalAmount(agentReport.getOriginalCurrencyAmount());
		if (CollectionUtils.isNotEmpty(positions)) {
			ProductItem productItem = positions.get(0).getProductItem();
			final String currencySign = currencyService.getCurrencyByCode(productItem.getSellerCurrencyCode()).getSign();
			invoice.setCurrencySign(currencySign);

			positions.forEach(position -> {
				ProductItem pi = position.getProductItem();

				SwiftPeoplePayoutInvoiceItem item = new SwiftPeoplePayoutInvoiceItem();
				item.setQty(BigDecimal.ONE);
				item.setUnitDescription(pi.getProduct().getDescription());
				item.setUnitPrice(pi.getSellerPrice());
				item.setUnitAmount(pi.getSellerPrice().multiply(item.getQty()));
				item.setCurrencySign(currencySign);

				list.add(item);
			});
		}
		return new SwiftPeoplePayoutInvoiceReportData(invoice);
	}

	@Override
	public List<UpdatePayoutRequestStatusDTO> reserveMoney() {
		List<PayoutRequest> payoutRequests = payoutRequestService.getPayoutRequestsByStatusAndAgent(PayoutRequestStatus.NEW, PayoutRequestAgentName.SWIFT_PEOPLE);
		List<UpdatePayoutRequestStatusDTO> result = new ArrayList<>();
		payoutRequests.forEach(pr -> {
			try {
				StatusResponse statusResponse = existPayoutRequest(pr);
				if (Objects.nonNull(statusResponse)) {
					log.info("Payout request {} already existed in order {}", pr.getId(), pr.getOrderId());
					if (PayoutRequestStatus.NEW == pr.getStatus()) {
						result.add(createUpdateStatusDto(pr, PayoutRequestStatus.APPROVED, statusResponse.toString()));
					}
					return;
				}

				log.info("Reserve payout request {} in agent {} api call", pr.getId(), pr.getAgentName());
				CreateLead200Response lead = this.createLead(pr);
				if (client.isSuccess(lead.getErrorInfo())) {
					log.info("Reserve payout request {} in agent {} api call success.", pr.getId(), pr.getAgentName());
					result.add(createUpdateStatusDto(pr, PayoutRequestStatus.APPROVED, lead.toString()));
					log.info("SwiftPeoplePayoutService: success to reserve money in order {} by service {}.", pr.getOrderId(), pr.getAgentName().name());
				} else {
					log.warn("Reserve payout request {} in agent {} - api call error {}", pr.getId(), pr.getAgentName(), lead);
				}
			} catch (RestClientException e) {
				log.error("SwiftPeoplePayoutService: fail to reserve money in order {} by service {}. Skip it!", pr.getOrderId(), pr.getAgentName().name(), e);
			}
		});
		return result;
	}

	@Override
	public List<UpdatePayoutRequestStatusDTO> refuseReserveMoney() {
		List<PayoutRequest> payoutRequests = payoutRequestService.getPayoutRequestsByStatusAndAgent(PayoutRequestStatus.MARK_FOR_REFUSE, PayoutRequestAgentName.SWIFT_PEOPLE);
		List<UpdatePayoutRequestStatusDTO> result = new ArrayList<>();
		payoutRequests.forEach(pr -> {
			try {
				log.info("Refuse reserve payout request {} in agent {} api call", pr.getId(), pr.getAgentName());
				StatusResponse statusResponse = client.setRefusedStatus(pr.getId().toString());
				if (client.isSuccess(statusResponse.getErrorInfo())) {
					log.info("Refuse reserve payout request {} in agent {} api call success.", pr.getId(), pr.getAgentName());
					result.add(createUpdateStatusDto(pr, PayoutRequestStatus.REFUSED, statusResponse.toString()));
					log.info("SwiftPeoplePayoutService: success to refuse money reserve in order {} by service {}.", pr.getOrderId(), pr.getAgentName().name());
				} else {
					log.warn("Refuse reserve payout request {} in agent {} - api call error {}", pr.getId(), pr.getAgentName(), statusResponse);
				}
			} catch (RestClientException e) {
				log.error("SwiftPeoplePayoutService: fail to refuse money reserve in order {} by service {}. Skip it!", pr.getOrderId(), pr.getAgentName().name(), e);
			}
		});
		return result;
	}

	@Async
	@Override
	public void masterSavePayoutRequestStatus(List<UpdatePayoutRequestStatusDTO> list) {
		log.info("MasterSavePayoutRequestStatus: update payout request status in list with size {}", Optional.ofNullable(list).map(List::size).orElse(0));
		if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(list)) {
			list.stream().filter(dto -> PayoutRequestAgentName.SWIFT_PEOPLE == dto.getAgentName())
					.forEach(dto -> payoutRequestService.findById(dto.getId())
							.ifPresent(pr -> transactionTemplate.execute(new TransactionCallbackWithoutResult() {
								@Override
								protected void doInTransactionWithoutResult(TransactionStatus status) {
									log.info("MasterSavePayoutRequestStatus: payout request {} in order {} processing", dto.getId(), pr.getOrderId());
									PayoutRequestStatus oldStatus = pr.getStatus();
									pr.setStatus(dto.getStatus());
									pr.setAgentRawResponse(dto.getAgentRawResponse());
									payoutRequestService.save(pr);
									log.info("MasterSavePayoutRequestStatus: payout request {} in order {} changed status {} --> {}",
											pr.getId(), pr.getOrderId(), oldStatus, pr.getStatus());
								}
							})));
		}
	}

	//Создать быструю заявку = Резерв денежных средств в требуемой валюте
	private CreateLead200Response createLead(PayoutRequest pr) {
		CreateLeadRequest leadRequest = new CreateLeadRequest();
		leadRequest.setOrganisationId(swiftPeopleProperties.getOrganisationId());

		leadRequest.setId2(pr.getId().toString());
		leadRequest.setAmount(pr.getAmount());
		leadRequest.setCurrency(pr.getCurrency());

		leadRequest.setContractFiles(new ArrayList<>());
		leadRequest.setInvoiceFiles(new ArrayList<>());
		leadRequest.setOtherFiles(new ArrayList<>());
		return client.createLead(leadRequest);
	}

	private StatusResponse existPayoutRequest(PayoutRequest payoutRequest) {
		try {
			log.info("SwiftPeoplePayoutService: check --> does the payout request {} exist in agent for order {}", payoutRequest.getId().toString(), payoutRequest.getOrderId());
			StatusResponse res = client.getPaymentRequestStatus(payoutRequest.getId().toString());
			if (client.isSuccess(res.getErrorInfo())
					&& Objects.nonNull(res.getResult())
					&& Objects.nonNull(res.getResult().getAmount())
					&& Objects.nonNull(res.getResult().getCurrency())

					&& payoutRequest.getAmount().compareTo(res.getResult().getAmount()) == 0
					&& payoutRequest.getCurrency().equals(res.getResult().getCurrency())) {
				log.info("SwiftPeoplePayoutService: check --> payout request {} exists in agent for order {}", payoutRequest.getId().toString(), payoutRequest.getOrderId());
				return res;
			}
			log.error("SwiftPeoplePayoutService: check --> invalid response during status verification before creating payout request {} in order {}. Response: {}", payoutRequest, payoutRequest.getOrderId(), res);
			throw new PayoutRequestException("SwiftPeoplePayoutService: check --> invalid response during status verification before creating payout request %s", payoutRequest.getId().toString());
		} catch (HttpClientErrorException.NotFound e) {
			log.info("SwiftPeoplePayoutService: check --> payout request {} not exists in agent for order {}", payoutRequest.getId().toString(), payoutRequest.getOrderId());
			return null;
		}
	}

	private UpdatePayoutRequestStatusDTO createUpdateStatusDto(PayoutRequest payoutRequest, PayoutRequestStatus status, String rawResponse) {
		UpdatePayoutRequestStatusDTO dto = new UpdatePayoutRequestStatusDTO();
		dto.setId(payoutRequest.getId());
		dto.setAgentName(payoutRequest.getAgentName());
		dto.setStatus(status);
		dto.setAgentRawResponse(rawResponse);
		return dto;
	}
}

