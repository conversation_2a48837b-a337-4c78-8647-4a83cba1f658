package su.reddot.infrastructure.captcha.recaptcha;

import com.google.cloud.recaptchaenterprise.v1.RecaptchaEnterpriseServiceClient;
import com.google.recaptchaenterprise.v1.AnnotateAssessmentRequest;
import com.google.recaptchaenterprise.v1.AnnotateAssessmentRequest.Reason;
import com.google.recaptchaenterprise.v1.Assessment;
import com.google.recaptchaenterprise.v1.CreateAssessmentRequest;
import com.google.recaptchaenterprise.v1.Event;
import com.google.recaptchaenterprise.v1.ProjectName;
import com.google.recaptchaenterprise.v1.RiskAnalysis.ClassificationReason;
import com.google.recaptchaenterprise.v1.UserId;
import com.google.recaptchaenterprise.v1.UserInfo;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.stereotype.Service;
import su.reddot.domain.service.metric.MicrometerService;
import su.reddot.infrastructure.captcha.CaptchaUtils;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.configuration.captcha.recaptcha.RecaptchaProperties;

import java.util.stream.Collectors;

@RequiredArgsConstructor
@Slf4j
@Service
public class DefaultRecaptchaService implements RecaptchaService {

    private final RecaptchaProperties recaptchaProperties;
    private final RecaptchaEnterpriseServiceClient recaptchaClient;
    private final MicrometerService micrometerService;
    private final ConfigParamService configParamService;
    private final MessageSourceAccessor messageSourceAccessor;
    private final RecaptchaAssessmentsCache assessmentsCache;

    @Override
    public Assessment createAndVerifyAssessment(
        String recaptchaToken, String userAgent, Long userId, String phoneNumber
    ) {
        if (!recaptchaProperties.isEnabled()) {
            return null;
        }

        if (CaptchaUtils.shouldSkipRecaptchaVerification(configParamService, userAgent)) {
            log.info("reCAPTCHA verification skipped for User-Agent: {}", userAgent);
            micrometerService.increment("recaptcha.skipped");
            return null;
        }

        if (StringUtils.isBlank(recaptchaToken)) {
            log.info("reCAPTCHA verification failed: recaptchaToken is blank");
            throwVerificationError("recaptchaToken is blank");
        }

        // Set the properties of the event to be tracked.
        final UserInfo userInfo = UserInfo.newBuilder()
            .setAccountId((userId != null) ? userId.toString() : phoneNumber)
            .addUserIds(UserId.newBuilder().setPhoneNumber(phoneNumber).build())
            .build();

        final Event event = Event.newBuilder()
            .setSiteKey(recaptchaProperties.getSiteKey())
            .setToken(recaptchaToken)
            .setUserInfo(userInfo)
            .build();

        // Build the assessment request.
        final CreateAssessmentRequest createAssessmentRequest = CreateAssessmentRequest.newBuilder()
            .setParent(ProjectName.of(recaptchaProperties.getProjectId()).toString())
            .setAssessment(Assessment.newBuilder().setEvent(event).build())
            .build();

        // Get the assessment response.
        Assessment assessment = recaptchaClient.createAssessment(createAssessmentRequest);

        log(assessment);
        meter(assessment);
        verify(assessment);

        return assessment;
    }

    @Override
    public void onGenerateCode(String verificationToken, Assessment assessment) {
        if (recaptchaProperties.isEnabled() && assessment != null) {
            assessmentsCache.saveAssessmentNameByVerificationToken(verificationToken, assessment.getName());
            try {
                recaptchaClient.annotateAssessment(
                    AnnotateAssessmentRequest.newBuilder()
                        .setName(assessment.getName())
                        .addReasons(Reason.INITIATED_TWO_FACTOR)
                        .build()
                );
            } catch (Exception e) {
                log.error("Error while annotating reCAPTCHA assessment", e);
            }
        }
    }

    @Override
    public void onVerifyCode(String verificationToken, boolean isVerified) {
        if (recaptchaProperties.isEnabled()) {
            String assessmentName = assessmentsCache.findAssessmentNameByVerificationToken(verificationToken);
            if (assessmentName != null) {
                try {
                    recaptchaClient.annotateAssessment(
                        AnnotateAssessmentRequest.newBuilder()
                            .setName(assessmentName)
                            .addReasons(isVerified ? Reason.PASSED_TWO_FACTOR : Reason.FAILED_TWO_FACTOR)
                            .build()
                    );
                } catch (Exception e) {
                    log.error("Error while annotating reCAPTCHA assessment", e);
                }
            }
        }
    }


    private void log(Assessment assessment) {
        String logMessage = "reCAPTCHA verification ";

        if (!assessment.getTokenProperties().getValid()) {
            logMessage += "failed: " + assessment.getTokenProperties().getInvalidReason().name();
        } else if (!isScorePassing(assessment)) {
            logMessage += "failed: risk analysis score too low";
        } else if (!isSmsFraudRiskPassing(assessment)) {
            logMessage += "failed: SMS fraud risk too high";
        } else {
            logMessage += "passed";
        }

        logMessage += ". reCAPTCHA score is " + assessment.getRiskAnalysis().getScore();

        if (!assessment.getRiskAnalysis().getReasonsList().isEmpty()) {
            logMessage += getScoreReasons(assessment).collect(Collectors.joining(",", ". Score reasons: ", "."));
        }

        logMessage += ". SMS fraud risk is " + assessment.getPhoneFraudAssessment().getSmsTollFraudVerdict().getRisk();

        log.info(logMessage);
    }

    private void meter(Assessment assessment) {
        micrometerService.increment("recaptcha.verified",
            "valid", String.valueOf(assessment.getTokenProperties().getValid()),
            "invalidReason", assessment.getTokenProperties().getInvalidReason().name(),
            "score", String.valueOf(assessment.getRiskAnalysis().getScore()),
            "scorePassing", String.valueOf(isScorePassing(assessment)),
            "scoreReasons", getScoreReasons(assessment).collect(Collectors.joining(",")),
            "smsFraudRisk", String.valueOf(assessment.getPhoneFraudAssessment().getSmsTollFraudVerdict().getRisk()),
            "smsFraudRiskPassing", String.valueOf(isSmsFraudRiskPassing(assessment))
        );
    }

    private void verify(Assessment assessment) {
        if (!assessment.getTokenProperties().getValid()) {
            throwVerificationError(
                "reCAPTCHA verification failed: " + assessment.getTokenProperties().getInvalidReason().name()
            );
        } else if (!isScorePassing(assessment)) {
            throwVerificationError(
                "reCAPTCHA verification failed: risk analysis score too low"
            );
        } else if (!isSmsFraudRiskPassing(assessment)) {
            throwVerificationError(
                "reCAPTCHA verification failed: SMS fraud risk too high"
            );
        }
    }

    private boolean isScorePassing(Assessment assessment) {
        return assessment.getRiskAnalysis().getScore() >=
            configParamService.getValueAsDoubleCached(ConfigParamService.CONFIG_PARAM_RECAPTCHA_SCORE_PASSING_MIN);
    }

    private boolean isSmsFraudRiskPassing(Assessment assessment) {
        return assessment.getPhoneFraudAssessment().getSmsTollFraudVerdict().getRisk() <
            configParamService.getValueAsDoubleCached(ConfigParamService.CONFIG_PARAM_RECAPTCHA_SMS_FRAUD_RISK_PASSING_MAX);
    }

    private Stream<String> getScoreReasons(Assessment assessment) {
        return assessment.getRiskAnalysis().getReasonsList().stream().map(ClassificationReason::name);
    }

    private void throwVerificationError(String message) {
        micrometerService.increment("recaptcha.verification.failed");
        throw CaptchaUtils.verificationError(messageSourceAccessor, message);
    }
}
