package su.reddot.infrastructure.bitrix.jobs;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import su.reddot.domain.dao.address.AddressRepository;
import su.reddot.domain.dao.addressendpoint.AddressEndpointRepository;
import su.reddot.domain.dao.bitrix.BitrixCrossBorderRepository;
import su.reddot.domain.model.address.Address;
import su.reddot.domain.model.address.Country;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.agentreport.AgentReportState;
import su.reddot.domain.model.bitrix.BitrixCrossBorder;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderExtraPropInfo;
import su.reddot.domain.model.order.OrderExtraPropValue;
import su.reddot.domain.model.order.OrderFileTypeName;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.order.OrderStatusGroup;
import su.reddot.domain.model.payoutrequest.PayoutRequestAgentName;
import su.reddot.domain.service.address.CountryService;
import su.reddot.domain.service.addressendpoint.AddressEndpointService;
import su.reddot.domain.service.adminpanel.orders.AdminOrdersService;
import su.reddot.domain.service.agentreport.AgentReportService;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.dto.delivery.DeliveryParamRequestDTO;
import su.reddot.domain.service.dto.delivery.PickupInfoDTO;
import su.reddot.domain.service.dto.delivery.SetDeliveryStateDTO;
import su.reddot.domain.service.dto.delivery.TimeIntervalDTO;
import su.reddot.domain.service.dto.order.ConciergePaymentToSellerParams;
import su.reddot.domain.service.dto.order.PayoutRequestDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.order.impl.OrderExtraPropsService;
import su.reddot.domain.service.orderPosition.OrderPositionService;
import su.reddot.domain.service.orderPosition.params.ConfirmationParams;
import su.reddot.domain.service.payoutrequest.PayoutRequestService;
import su.reddot.infrastructure.bitrix.BitrixClient;
import su.reddot.infrastructure.bitrix.DefaultBitrixClient;
import su.reddot.infrastructure.bitrix.dto.BitrixRequestDTO;
import su.reddot.infrastructure.bitrix.dto.BitrixResponseListSmartProcessDTO;
import su.reddot.infrastructure.bitrix.dto.SmartProcessDTO;
import su.reddot.infrastructure.bitrix.service.Bitrix2OskellyRoute;
import su.reddot.infrastructure.bitrix.service.BitrixCrossBorderException;
import su.reddot.infrastructure.bitrix.service.BitrixSenderAddress;
import su.reddot.infrastructure.bitrix.service.ConfigParamBitrixCrossBorder;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.configuration.bitrix.BitrixProperty;
import su.reddot.infrastructure.delivery.DeliveryService;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.logistic.TimeIntervalsService;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "bitrix.jobs.cross-border.enabled", havingValue = "true")
public class BitrixCrossBorderJobs {
	private final BitrixClient bitrixClient;
	private final BitrixCrossBorderRepository bitrixCrossBorderRepository;
	private final ConfigParamService configParamService;
	private final OrderPositionService orderPositionService;
	private final OrderService orderService;
	private final AdminOrdersService adminOrdersService;
	private final DeliveryService deliveryService;
	private final AddressRepository addressRepository;
	private final AddressEndpointRepository addressEndpointRepository;
	private final BitrixProperty bitrixProperty;
	private final AgentReportService agentReportService;
	private final CounterpartyService counterpartyService;
	private final CountryService countryService;
	private final TimeIntervalsService timeIntervalsService;
	private final TransactionTemplate transactionTemplate;
	private final AddressEndpointService addressEndpointService;
	private final PayoutRequestService payoutRequestService;

	private final List<OrderStatusGroup> canProcessOrderDelivery = Arrays.asList(OrderStatusGroup.PAYMENT, OrderStatusGroup.FROM_SELLER);
	private final String oskellyPhoneFromSite = "8(800)707-53-08";
	private final String JOIN_ADDRESS = "%s, %s, %s";

	@Scheduled(cron = "${bitrix.jobs.cross-border.cron}")
	public void checkBitrixCrossBorderOrderState() {
		String uuid = UUID.randomUUID().toString();
		log.info("task:BitrixCrossBorderJob({})", uuid);

		//we can get from bitrix only 50 items at once - that's why we get 50 records from DB
		int pageSize = 50;
		int startingPage = 1;
		int currentPage = startingPage;
		boolean hasMoreRecords = true;
		while (hasMoreRecords) {
			Pageable pageable = PageRequest.of(currentPage - 1, pageSize);

			Page<BitrixCrossBorder> bitrixCrossBorderPage = bitrixCrossBorderRepository.findAllByCloseAtIsNullOrderByOskellyOrderIdAsc(pageable);
			log.info("BitrixCrossBorderJob: #{} ==> total items {} have been found", uuid, bitrixCrossBorderPage.getTotalElements());
			List<BitrixCrossBorder> bitrixCrossBorderList = bitrixCrossBorderPage.getContent();
			if (CollectionUtils.isNotEmpty(bitrixCrossBorderList)) {
				log.info("BitrixCrossBorderJob: #{} ==> process page {} items per page {}", uuid, currentPage, bitrixCrossBorderList.size());
				ConfigParamBitrixCrossBorder configs = configParamService
						.getValueAsObject(ConfigParamService.CONFIG_PARAM_BITRIX_CROSS_BORDER, ConfigParamBitrixCrossBorder.class);
				Objects.requireNonNull(configs, "BitrixCrossBorderJob: #" + uuid + " ==> invalid config param");

				List<SmartProcessDTO> bitrixDataList;
				BitrixProperty.SmartProcessProductStatus status = bitrixProperty.getFields().getSmartProcess().getProduct().getStatus();

				List<Long> bitrixProductIdList = bitrixCrossBorderList.stream().map(BitrixCrossBorder::getBitrixProductId).sorted().collect(Collectors.toList());
				try {
					BitrixResponseListSmartProcessDTO<List<SmartProcessDTO>> smartProcessResponse = bitrixClient.listSmartProcess(
							new BitrixRequestDTO()
									.setEntityTypeId(bitrixProperty.getSmartProcesses().getProductTypeId())
									.setSelect(Collections.singletonList("*"))
									.addFilter(DefaultBitrixClient.SmartProcessField.ID, bitrixProductIdList));

					if (smartProcessResponse.getTotal() > 0) {
						bitrixDataList = smartProcessResponse.getResult().getItems();
					} else {
						log.warn("BitrixCrossBorderJob: #{} ==> fetch data from bitrix box invalid. No items for processing. Stop job.", uuid);
						return;
					}
				} catch (Exception ex) {
					log.error("BitrixCrossBorderJob: #{} ==> Fail to fetch data from bitrix box! Search param order list = {}",
							uuid, bitrixCrossBorderList.stream().map(BitrixCrossBorder::getOskellyOrderId).collect(Collectors.toList()), ex);
					return;
				}
				log.info("BitrixCrossBorderJob: #{} ==> fetch data from bitrix box completed. Total items {}", uuid, bitrixDataList.size());

				//process each order isolated - one order in one transaction
				bitrixCrossBorderList.stream()
						.collect(Collectors.groupingBy(BitrixCrossBorder::getOskellyOrderId))
						.forEach((orderId, bitrixCrossBorders) -> {
							log.info("BitrixCrossBorderJob: process order {}", orderId);
							Set<Long> shipment = new HashSet<>();
							Set<Long> transit = new HashSet<>();
							Set<Long> delivered = new HashSet<>();
							boolean isFailed = false;

							try {
								transactionTemplate.execute(new TransactionCallbackWithoutResult() {
									@Override
									protected void doInTransactionWithoutResult(@NotNull TransactionStatus ts) {
										Order order = orderService.getOrder(orderId);
										Long orderSaleShipmentRoute = OrderExtraPropsService.getOrderExtraPropValue(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_SALE_SHIPMENT_ROUTE)
												.map(OrderExtraPropValue::getPropValue)
												.map(Long::parseLong)
												.orElse(-1L);
										bitrixCrossBorders.forEach(cb -> {
											Long positionId = cb.getOskellyOrderPositionId();

											try {
												bitrixDataList.stream()
														.filter(smartProcess -> cb.getBitrixProductId().equals(smartProcess.getId()))
														.filter(smartProcess -> Objects.nonNull(smartProcess.getEditStatusId()))
														.filter(smartProcess -> Objects.isNull(cb.getBitrixEditStatusId()) || !cb.getBitrixEditStatusId().equals(smartProcess.getEditStatusId()))
														.forEach(item -> {
															cb.updateEditStatus(item.getEditStatusId());
															bitrixCrossBorderRepository.save(cb);
															log.info("BitrixCrossBorderJob: update CB edit status id {} in order {} for position {}", cb.getBitrixEditStatusId(), orderId, positionId);
														});
											} catch (Exception ex) {
												log.error("BitrixCrossBorderJob: Fail to process edit status in order {}. Skip it.", orderId, ex);
												return;
											}

											if (OrderState.REFUND != order.getState()) {
												try {
													bitrixDataList.stream()
															.filter(smartProcess -> cb.getBitrixProductId().equals(smartProcess.getId()))
															.filter(smartProcess -> Objects.nonNull(smartProcess.getConfirmationStatusId()))
															.filter(smartProcess -> Objects.isNull(cb.getBitrixConfirmationStatusId()) || !cb.getBitrixConfirmationStatusId().equals(smartProcess.getConfirmationStatusId()))
															.forEach(item -> {
																if (OrderStatusGroup.PAYMENT == order.getStatus().getGroup()) {
																	ConfirmationParams confirmationParams = new ConfirmationParams();
																	confirmationParams.setOrderProcessingNotificationRequired(true);
																	if (status.getProductConfirmed().equals(item.getConfirmationStatusId())) {
																		confirmationParams.setDoConfirmSale(Boolean.TRUE);
																	} else if (status.getProductRejected().equals(item.getConfirmationStatusId())) {
																		confirmationParams.setDoConfirmSale(Boolean.FALSE);
																		cb.setCloseAt(ZonedDateTime.now());
																	} else {
																		log.warn("BitrixCrossBorderJob: confirmation step = invalid bitrix enum code id {} in order {} for position {}", item.getConfirmationStatusId(), orderId, positionId);
																		return;
																	}
																	cb.updateConfirmationStatus(item.getConfirmationStatusId());
																	bitrixCrossBorderRepository.save(cb);
																	log.info("BitrixCrossBorderJob: confirmation step = update CB confirmation status id {} in order {} for position {}", cb.getBitrixConfirmationStatusId(), orderId, positionId);

																	orderPositionService.getById(positionId)
																			.ifPresent(op -> {
																				orderPositionService.handleConfirm(op, confirmationParams);
																				log.info("BitrixCrossBorderJob: confirmation step = set confirmation status to {} in order {} for position {}", confirmationParams.getDoConfirmSale(), orderId, positionId);
																			});
																}
															});
													//confirm order
													if (order.getOrderPositions().stream().allMatch(op -> OrderPositionState.isConfirmationStepPassed(op.getState()))
															&& !order.isConfirmed()
															&& bitrixCrossBorders.stream().allMatch(i -> Objects.nonNull(i.getBitrixConfirmationStatusId()))) {
														//we can not confirm order without set seller address
														//if user has at least one address we use it or else use default china address (will be created if not exist)
														if (Objects.isNull(order.getPickupAddressEndpoint())) {
															List<AddressEndpoint> addressEndpoints = addressEndpointService.findAllUserActiveAddressEndpoints(order.getSellerUser());
															log.info("BitrixCrossBorderJob: confirmation step = user {} has {} addresses in order {}", order.getSellerUser().getId(), addressEndpoints.size(), orderId);

															AddressEndpoint finalAddressEndpoint;
															if (CollectionUtils.isNotEmpty(addressEndpoints)) {
																if (addressEndpoints.size() > 1) {
																	throw new BitrixCrossBorderException("BitrixCrossBorderJob: confirmation step = system found %d seller addresses for user %d in order %d",
																			addressEndpoints.size(), order.getSellerUser().getId(), orderId);
																} else {
																	finalAddressEndpoint = addressEndpoints.get(0);
																	log.info("BitrixCrossBorderJob: confirmation step = system found address endpoint {} for user {} in order {}", finalAddressEndpoint.getId(), order.getSellerUser().getId(), orderId);
																}
															} else {
																Bitrix2OskellyRoute route = configs.getRoutes().stream()
																		.filter(r -> r.getOskellyRouteId().equals(orderSaleShipmentRoute)).findFirst()
																		.orElseThrow(() -> new BitrixCrossBorderException("BitrixCrossBorderJob: confirmation step = system not found valid sale shipment route in order %d", orderId));
																Address addressEntity = addressRepository.save(createAddress(order, route.getSenderAddress()));
																finalAddressEndpoint = addressEndpointRepository.save(createAddressEndpoint(order, addressEntity, route.getSenderAddress()));
																log.info("BitrixCrossBorderJob: confirmation step = system not found address endpoint for user {} in order {}. Create default address", order.getSellerUser().getId(), orderId);
															}

															order.setPickupAddressEndpoint(finalAddressEndpoint);
															orderService.saveOrder(order);
															log.info("BitrixCrossBorderJob: confirmation step = set pick up address endpoint {} in order {}", finalAddressEndpoint.getId(), orderId);
														}
														//we can not confirm order without set logistic info
														//use default 1 interval and delivery company from config (each route has default delivery company)
														if (Objects.isNull(order.getSellerLogisticsInfo())) {
															List<TimeIntervalDTO> defaultIntervals = timeIntervalsService.getDefaultIntervals();
															if (CollectionUtils.isNotEmpty(defaultIntervals)) {
																long defaultIntervalId = defaultIntervals.get(0).getId();
																Long deliveryCompanyId = deliveryService.getCrossBorderDeliveryCompany(order).getId();
																deliveryService.setPickupInfoFromSeller(order.getId(), new PickupInfoDTO(deliveryCompanyId, OffsetDateTime.now(), defaultIntervalId), false);
																log.info("BitrixCrossBorderJob: confirmation step = add pick info with company {} and interval {} in order {}", deliveryCompanyId, defaultIntervalId, orderId);
															}
														}

														orderService.confirmOrder(order, true);
														log.info("BitrixCrossBorderJob: confirmation step = order {} confirmed.", orderId);
													}
													//payout to seller
													if (order.isConfirmed() && Objects.isNull(order.getAgentReport())) {
														//payout stage in confirmation step - seller_counterparty is mandatory
														if (Objects.isNull(order.getSellerCounterparty())) {
															List<Counterparty> counterpartyList = counterpartyService.findAllActiveUserCounterparty(order.getSellerUser());
															if (CollectionUtils.isNotEmpty(counterpartyList)) {
																if (counterpartyList.size() > 1) {
																	throw new BitrixCrossBorderException("BitrixCrossBorderJob: payout step = system found %d active counterparties for user %d in order %d",
																			counterpartyList.size(), order.getSellerUser().getId(), orderId);
																} else {
																	order.setSellerCounterparty(counterpartyList.get(0));
																}
															} else {
																throw new BitrixCrossBorderException("BitrixCrossBorderJob: payout step = counterparty not found for user %d in order %d",
																		order.getSellerUser().getId(), orderId);
															}
														}
														PayoutRequestDTO payoutRequestDTO = payoutRequestService.getApprovedPayoutRequestByOrderAndAgent(orderId, PayoutRequestAgentName.SWIFT_PEOPLE);
														if (Objects.isNull(payoutRequestDTO)) {
															log.error("BitrixCrossBorderJob: payout step = payout request not found in order {}", orderId);
															return;
														}
														if (CollectionUtils.isEmpty(order.getSeller().getUserContracts())
																|| CollectionUtils.isEmpty(order.getOrderFiles().stream()
																.filter(of -> OrderFileTypeName.ORDER_INVOICE == of.getType().getName()).collect(Collectors.toList()))
														) {
															log.error("BitrixCrossBorderJob: payout step = required documents (contract + invoice) were not found in order {}", orderId);
															return;
														}
														agentReportService.payMoneyToConcierge(new ConciergePaymentToSellerParams(order.getId(), true));
														log.info("BitrixCrossBorderJob: payout step = init payout to cross-border seller {} in order {}", order.getSellerUser().getId(), orderId);
													}
												} catch (Exception ex) {
													log.error("BitrixCrossBorderJob: Fail to process confirmation status in order {}. Skip it.", orderId, ex);
													return;
												}

												try {
													bitrixDataList.stream()
															.filter(smartProcess -> cb.getBitrixProductId().equals(smartProcess.getId()))
															.filter(smartProcess -> Objects.nonNull(smartProcess.getDeliveryStatusId()))
															.filter(smartProcess -> Objects.isNull(cb.getBitrixDeliveryStatusAt()) || !cb.getBitrixDeliveryStatusId().equals(smartProcess.getDeliveryStatusId()))
															.forEach(item -> {
//																if (status.getShipment().equals(item.getDeliveryStatusId())) {
//																	shipment.add(item.getOrderPositionId());
//																} else
																if (status.getTransit().equals(item.getDeliveryStatusId())) {
																	transit.add(item.getOrderPositionId());
																} else if (status.getDelivered().equals(item.getDeliveryStatusId())) {
																	delivered.add(item.getOrderPositionId());
																	cb.setCloseAt(ZonedDateTime.now());
																} else {
																	log.warn("BitrixCrossBorderJob: delivery step = invalid bitrix enum code id {} in order {} for position {}", item.getDeliveryStatusId(), orderId, positionId);
																	return;
																}
																cb.updateDeliveryStatus(item.getDeliveryStatusId());
																bitrixCrossBorderRepository.save(cb);
																log.info("BitrixCrossBorderJob: delivery step = update CB delivery status id {} in order {} for position {}", cb.getBitrixDeliveryStatusId(), orderId, positionId);
															});

													if (bitrixCrossBorders.stream().allMatch(i -> Objects.nonNull(i.getBitrixDeliveryStatusId()))
															&& Stream.of(shipment, transit, delivered).anyMatch(CollectionUtils::isNotEmpty)
															&& canProcessOrderDelivery.contains(order.getStatus().getGroup())
															&& order.isConfirmed()
															//payout done
															&& Objects.nonNull(order.getAgentReport())
															&& Objects.nonNull(order.getAgentReport().getState())
															&& AgentReportState.PAYMENT_COMPLETED == order.getAgentReport().getState()

															&& !DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE.equals(order.getDeliveryState())) {

//														[optimization]add skipped id(means id that not has any update) to correct choose delivery state in order
														bitrixCrossBorders.forEach(i -> {
															if (status.getShipment().equals(i.getBitrixDeliveryStatusId())) {
																shipment.add(i.getOskellyOrderPositionId());
															} else if (status.getTransit().equals(i.getBitrixDeliveryStatusId())) {
																transit.add(i.getOskellyOrderPositionId());
															} else if (status.getDelivered().equals(i.getBitrixDeliveryStatusId())) {
																delivered.add(i.getOskellyOrderPositionId());
															}
														});

														if (Objects.isNull(order.getDeliveryState())) {
															DeliveryParamRequestDTO params = DeliveryParamRequestDTO.builder()
																	.orderId(orderId)
																	.courierName("CrossBorder")
																	.courierPhone(oskellyPhoneFromSite)
																	.pickupDate(ZonedDateTime.now())
																	.isOrderProcessingNotificationRequired(true).build();
															adminOrdersService.confirmOrderAndSendToPickupOurselves(params);
															log.info("BitrixCrossBorderJob: delivery step = shipment order {}", orderId);
														}

														if (CollectionUtils.isNotEmpty(shipment)) {
															log.info("BitrixCrossBorderJob: delivery step = order {} is already in shipment for order positions {}", orderId, shipment);
														} else if (CollectionUtils.isNotEmpty(transit)) {
															if (DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE == order.getDeliveryState()) {
																log.info("BitrixCrossBorderJob: delivery step = order {} is already in transit by order positions {}", orderId, transit);
															} else {
																SetDeliveryStateDTO source = new SetDeliveryStateDTO(order.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
																source.setShouldValidateAuthority(false);
																deliveryService.setDeliveryState(source);
																log.info("BitrixCrossBorderJob: delivery step = transit order {} by order positions {}", orderId, transit);
															}
														} else if (CollectionUtils.isNotEmpty(delivered)) {
															if (DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE == order.getDeliveryState()) {
																log.info("BitrixCrossBorderJob: delivery step = order {} is already delivered by order positions {}", orderId, delivered);
															} else {
																SetDeliveryStateDTO source = new SetDeliveryStateDTO(order.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
																source.setShouldValidateAuthority(false);
																deliveryService.setDeliveryState(source);
																log.info("BitrixCrossBorderJob: delivery step = delivered order {} by order positions {}", orderId, delivered);
															}
														}
														log.info("BitrixCrossBorderJob: delivery step = set new delivery state [ {} ] in order {}", order.getDeliveryState(), orderId);
													}
												} catch (Exception ex) {
													log.error("BitrixCrossBorderJob: Fail to process delivery status in order {}. Skip it.", orderId, ex);
												}
											}
										});
									}
								});

							} catch (Exception ex) {
								log.error("BitrixCrossBorderJob: process order {} failed. Transaction rollback", orderId, ex);
								isFailed = true;
							}
							if (!isFailed) {
								log.info("BitrixCrossBorderJob: process order {} completed", orderId);
							}
						});
			} else {
				log.info("BitrixCrossBorderJob: #{} ==> no items for processing", uuid);
			}

			hasMoreRecords = bitrixCrossBorderPage.hasNext();
			currentPage++;
		}
		log.info("end task:BitrixCrossBorderJob({})", uuid);
	}

	private Address createAddress(Order order, BitrixSenderAddress bitrixAddress) {
		Address address = new Address();
		address.setUser(order.getSellerUser());
		address.setCountry(Optional.ofNullable(countryService.findByAlpha2Code(bitrixAddress.getCountryCode())).map(Country::getNameRu).orElse(""));
		address.setCity(bitrixAddress.getCity());
		address.setAddress(String.format(JOIN_ADDRESS, bitrixAddress.getDistrict(), bitrixAddress.getStreet(), bitrixAddress.getBuilding()));
		address.setIsCityValidated(true);
		address.setIsAddressValidated(true);
		address.setChecked(true);
		address.setLastAddressValidationTime(ZonedDateTime.now());
		address.setLastCityValidationTime(ZonedDateTime.now());
		return address;
	}

	private AddressEndpoint createAddressEndpoint(Order order, Address address, BitrixSenderAddress bitrixAddress) {
		AddressEndpoint addressEndpoint = new AddressEndpoint();
		addressEndpoint.setAddress(address);
		addressEndpoint.setUser(order.getSellerUser());
		addressEndpoint.setPhone(oskellyPhoneFromSite);

		String courierFullName = bitrixAddress.getName();
		if (StringUtils.isNotBlank(courierFullName)) {
			String[] nameArray = courierFullName.split(" ");
			if (nameArray.length > 1) {
				addressEndpoint.setFirstName(nameArray[0]);
				addressEndpoint.setLastName(nameArray[1]);
			} else {
				addressEndpoint.setFirstName(nameArray[0]);
				addressEndpoint.setLastName(nameArray[0]);
			}
		} else {
			addressEndpoint.setFirstName("Oskelly");
			addressEndpoint.setLastName("International");
		}
		return addressEndpoint;
	}
}
