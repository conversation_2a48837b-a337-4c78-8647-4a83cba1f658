package su.reddot.infrastructure.configuration;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.DefaultUriBuilderFactory;
import su.reddot.domain.service.concierge.ApiClient;
import su.reddot.domain.service.concierge.api.ConciergeCommentsControllerApi;
import su.reddot.domain.service.concierge.api.ConciergePersonalShopperControllerApi;
import su.reddot.domain.service.concierge.api.ConciergePurchaseOrderControllerApi;
import su.reddot.domain.service.concierge.api.ConciergeStateHistoryControllerApi;
import su.reddot.domain.service.concierge.api.ManualPurchaseOrderAssignmentControllerApi;
import su.reddot.domain.service.concierge.api.OffersControllerApi;
import su.reddot.domain.service.concierge.api.RejectionReasonControllerApi;
import su.reddot.domain.service.concierge.api.SalesManagerControllerApi;
import su.reddot.domain.service.concierge.api.ShipmentControllerApi;
import su.reddot.domain.service.concierge.api.OfferShopperControllerApi;

import java.time.Duration;

@Configuration
@ConditionalOnProperty("concierge-service.enabled")
@RequiredArgsConstructor
public class ConciergeServiceConfiguration {
    private final RestTemplateBuilder restTemplateBuilder;

    @Value("${concierge-service.host}")
    private String conciergeServiceHost;

    @Value("${concierge-service.connect-timeout}")
    private Duration connectTimeout;

    @Value("${concierge-service.read-timeout}")
    private Duration readTimeout;

    private ApiClient apiClient;

    @Bean
    public ConciergePurchaseOrderControllerApi conciergePurchaseOrderControllerApi() {
        return new ConciergePurchaseOrderControllerApi(getApiClient());
    }

    @Bean
    public ConciergeCommentsControllerApi conciergeCommentsControllerApi() {
        return new ConciergeCommentsControllerApi(getApiClient());
    }

    @Bean
    public ConciergeStateHistoryControllerApi conciergeStateHistoryControllerApi() {
        return new ConciergeStateHistoryControllerApi(getApiClient());
    }

    @Bean
    public RejectionReasonControllerApi rejectionReasonControllerApi() {
        return new RejectionReasonControllerApi(getApiClient());
    }

    @Bean
    public OffersControllerApi offersControllerApi() {
        return new OffersControllerApi(getApiClient());
    }

    @Bean
    public ShipmentControllerApi shipmentControllerApi() {
        return new ShipmentControllerApi(getApiClient());
    }

    @Bean
    public ConciergePersonalShopperControllerApi conciergePersonalShopperControllerApi() {
        return new ConciergePersonalShopperControllerApi(getApiClient());
    }

    @Bean
    public SalesManagerControllerApi salesManagerControllerApi() {
        return new SalesManagerControllerApi(getApiClient());
    }

    @Bean
    public ManualPurchaseOrderAssignmentControllerApi manualPurchaseOrderAssignmentControllerApi(){
        return new ManualPurchaseOrderAssignmentControllerApi(getApiClient());
    }

    @Bean
    public OfferShopperControllerApi shopperOffersControllerApi() {
        return new OfferShopperControllerApi(getApiClient());
    }

    private ApiClient getApiClient() {
        if (this.apiClient == null) {
            ApiClient newApiClient = new ApiClient(conciergeServiceTemplate(restTemplateBuilder));
            newApiClient.setBasePath(conciergeServiceHost);
            this.apiClient = newApiClient;
        }

        return this.apiClient;
    }

    private RestTemplate conciergeServiceTemplate(RestTemplateBuilder restTemplateBuilder) {

        DefaultUriBuilderFactory uriBuilderFactory = new DefaultUriBuilderFactory();
        uriBuilderFactory.setEncodingMode(DefaultUriBuilderFactory.EncodingMode.VALUES_ONLY);

        return restTemplateBuilder
                .uriTemplateHandler(uriBuilderFactory)
                .setConnectTimeout(connectTimeout)
                .setReadTimeout(readTimeout)
                .build();
    }
}
