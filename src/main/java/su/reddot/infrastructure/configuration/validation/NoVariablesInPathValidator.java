package su.reddot.infrastructure.configuration.validation;

import com.google.common.collect.ImmutableSet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class NoVariablesInPathValidator {

    private final RequestMappingHandlerMapping requestMappingHandlerMapping;

    private final Pattern paramPattern = Pattern.compile(".*\\{(\\w+)\\}.*");


    @EventListener
    public void handleContextRefresh(ContextRefreshedEvent event) {
        Collection<String> activeUrlsWithPathParams = requestMappingHandlerMapping.getHandlerMethods().keySet().stream()
                .map(it -> it.getPatternsCondition().getPatterns())
                .flatMap(Collection::stream)
                .filter(this::containsPathVars)
                .collect(Collectors.toSet());
        //
        Collection<String> allowsUrlsWithPathParams = new HashSet<>(ALLOWS_URLS_WITH_PATH_PARAMS);
        allowsUrlsWithPathParams.removeAll(activeUrlsWithPathParams);
        if (!allowsUrlsWithPathParams.isEmpty()) {
            log.warn("Endpoint(s) missin (please, take a look / remove em): {}", allowsUrlsWithPathParams);
        }
        //
        activeUrlsWithPathParams.removeAll(ALLOWS_URLS_WITH_PATH_PARAMS);
        if (activeUrlsWithPathParams.isEmpty()) {
            return;
        }
        throw new IllegalArgumentException("Please, don't use path variables (use queryParams instead): " + StringUtils.join(activeUrlsWithPathParams, ","));
    }

    private boolean containsPathVars(String apiUrl) {
        return paramPattern.matcher(apiUrl).matches();
    }

    private static final Set<String> ALLOWS_URLS_WITH_PATH_PARAMS = ImmutableSet.<String>builder()
            .add("/adminpanel/api/v1/integrations/orderprocessing/delivery/orders/{orderId}/address/info")
            .add("/adminpanel/api/v1/integrations/orderprocessing/delivery/orders/{orderId}/company/id")
            .add("/adminpanel/api/v1/integrations/orderprocessing/expertise/orders/{orderId}/orderposition/{orderPositionId}/finish")
            .add("/adminpanel/api/v1/integrations/orderprocessing/expertise/orders/{orderId}/orderposition/{orderPositionId}/precomputation/numbers")
            .add("/adminpanel/api/v1/integrations/orderprocessing/expertise/orders/{orderId}/precomputation")
            .add("/adminpanel/api/v1/integrations/orderprocessing/orders/{orderId}/comments")
            .add("/adminpanel/api/v1/integrations/orderprocessing/orders/{orderId}/comments/{commentId}")
            .add("/adminpanel/api/v1/integrations/orderprocessing/orders/{orderId}/delivery/info")
            .add("/adminpanel/api/v1/integrations/orderprocessing/orders/{orderId}/details")
            .add("/adminpanel/api/v1/integrations/orderprocessing/orders/{orderId}/product/oskelly-size")
            .add("/adminpanel/bans/all/{banType}")
            .add("/adminpanel/bans/all/{userId}/{banType}")
            .add("/adminpanel/bans/{banId}")
            .add("/adminpanel/bans/{userId}/all")
            .add("/adminpanel/comments/{commentId}")
            .add("/adminpanel/orders/act-of-acceptance/pdf/{orderPositionIds}")
            .add("/adminpanel/orders/act-of-return/pdf/{orderPositionIds}")
            .add("/adminpanel/orders/add-buyer-address/{orderId}")
            .add("/adminpanel/orders/add-counterparty/{orderId}")
            .add("/adminpanel/orders/add-seller-address/{orderId}")
            .add("/adminpanel/orders/certif/pdf/{orderPositionId}")
            .add("/adminpanel/orders/change-address-endpoint/{orderId}")
            .add("/adminpanel/orders/change-seller-counterparty/{orderId}")
            .add("/adminpanel/orders/charge/{orderId}")
            .add("/adminpanel/orders/close-order-ticket/{ticketId}")
            .add("/adminpanel/orders/confirm-agentreport/{orderId}")
            .add("/adminpanel/orders/confirm-order/{orderId}")
            .add("/adminpanel/orders/convert-hold-to-prepayment/{orderId}")
            .add("/adminpanel/orders/delivery/changestate/{orderId}")
            .add("/adminpanel/orders/details/{orderId}")
            .add("/adminpanel/orders/dispute/{orderId}")
            .add("/adminpanel/orders/export/bitrix/crossborder/{orderId}")
            .add("/adminpanel/orders/export/orderprocessing/{orderId}")
            .add("/adminpanel/orders/get-user-address/{orderId}/{addressId}")
            .add("/adminpanel/orders/get-user-counterparty/{counterpartyId}")
            .add("/adminpanel/orders/in-store-pickup/{orderId}")
            .add("/adminpanel/orders/nonauthcertif/pdf/{orderPositionId}")
            .add("/adminpanel/orders/orderpositions/{orderPositionId}/set-country-of-origin")
            .add("/adminpanel/orders/oskWaybillFromSeller/pdf/{orderId}")
            .add("/adminpanel/orders/oskWaybillToBuyer/pdf/{orderId}")
            .add("/adminpanel/orders/payment/{orderId}")
            .add("/adminpanel/orders/pickup-declined/{orderId}")
            .add("/adminpanel/orders/receipts/retry/{orderId}")
            .add("/adminpanel/orders/receipts/view/{orderId}")
            .add("/adminpanel/orders/refund-amount/{orderId}")
            .add("/adminpanel/orders/refund-try-again-on-fail/{orderId}")
            .add("/adminpanel/orders/refund/{orderId}")
            .add("/adminpanel/orders/refund/{orderId}/rollback")
            .add("/adminpanel/orders/return-completed/{orderId}")
            .add("/adminpanel/orders/return/{orderId}")
            .add("/adminpanel/orders/returned-to-seller/{orderId}")
            .add("/adminpanel/orders/sell-order-in-boutique/{orderId}")
            .add("/adminpanel/orders/seller-counterparty/{orderId}")
            .add("/adminpanel/orders/send-agentreport/{orderId}")
            .add("/adminpanel/orders/send-boutique-order-to-stock-queue/{orderId}")
            .add("/adminpanel/orders/send-confirmed-order/ourselves/{orderId}")
            .add("/adminpanel/orders/send-confirmed-order/{orderId}")
            .add("/adminpanel/orders/send-order-after-expertise/ourselves/{orderId}")
            .add("/adminpanel/orders/send-order-after-expertise/{orderId}")
            .add("/adminpanel/orders/set-concierge-additional-info/{orderId}")
            .add("/adminpanel/orders/shipment2boutique/{orderId}")
            .add("/adminpanel/orders/switch-debt-on-payout/{orderId}")
            .add("/adminpanel/orders/tkStickers/pdf/{waybillId}")
            .add("/adminpanel/orders/tkWaybill/pdf/{waybillId}")
            .add("/adminpanel/orders/transfer-money-to-seller/{orderId}")
            .add("/adminpanel/orders/{orderId}/delivery/seller/office/pickup-info")
            .add("/adminpanel/orders/{orderId}/delivery/seller/office/pickup-info-set-by-seller")
            .add("/adminpanel/orders/{orderId}/users/{userId}/addresses")
            .add("/adminpanel/orders/{orderId}/users/{userId}/deleted-addresses")
            .add("/adminpanel/primary/actualAttributes/{brandId}/{categoryId}")
            .add("/adminpanel/primary/allPrimaryType/{primaryType}")
            .add("/adminpanel/primary/attributes//{brandId}/{categoryId}")
            .add("/adminpanel/primary/attributesFalse/{categoryId}")
            .add("/adminpanel/primary/attributesTrue/{categoryId}")
            .add("/adminpanel/primary/brands/{categoryId}")
            .add("/adminpanel/primary/brandsSelect/{categoryId}")
            .add("/adminpanel/primary/brandsSelectJson/{categoryId}")
            .add("/adminpanel/primary/categories2/{parentId}")
            .add("/adminpanel/primary/categories3/{parentId}")
            .add("/adminpanel/primary/categories4/{parentId}")
            .add("/adminpanel/primary/categoryById/{id}")
            .add("/adminpanel/primary/categoryList/{parentId}")
            .add("/adminpanel/primary/colors/{categoryId}")
            .add("/adminpanel/primary/filterData/wt/{index}")
            .add("/adminpanel/primary/materials/{categoryId}")
            .add("/adminpanel/primary/oskellyChoice/{id}")
            .add("/adminpanel/primary/podcategoryList/{parentId}")
            .add("/adminpanel/primary/subcategory/{id}")
            .add("/adminpanel/primary/subcategoryList/{parentId}")
            .add("/adminpanel/tools/manual-payouts-csv/{payoutId}")
            .add("/adminpanel/tools/ps-import-payments/{orderId}")
            .add("/adminpanel/users/{userId}")
            .add("/adminpanel/users/{userId}/change-mail-addr")
            .add("/adminpanel/users/{userId}/delete")
            .add("/adminpanel/users/{userId}/payment-details")
            .add("/adminpanel/users/{userId}/payment-details/{counterpartyId}")
            .add("/adminpanel/users/{userId}/restore")
            .add("/api/v1/categories/{id}/childs")
            .add("/api/v1/discount/gift-cards/{id}/payment-request")
            .add("/api/v1/integration/gazprom/users/{id}")
            .add("/api/v2/account/addresses/{aggregationId}")
            .add("/api/v2/account/userfiles/{userfileId}")
            .add("/api/v2/acquirers/{paymentVersion}/verify-payment")
            .add("/api/v2/address/city-search-history-int/{cityId}")
            .add("/api/v2/address/countries/{countryId}")
            .add("/api/v2/admin/banner/customizablepage/{bannerId}")
            .add("/api/v2/admin/banner/delete/{bannerId}")
            .add("/api/v2/admin/banner/{bannerId}")
            .add("/api/v2/admin/bargains/{id}")
            .add("/api/v2/admin/bargains/{id}/restore-expired")
            .add("/api/v2/admin/concierge/purchase-order/{orderId}")
            .add("/api/v2/admin/concierge/purchase-order/{orderId}/assign/admin/sales/{userId}")
            .add("/api/v2/admin/concierge/purchase-order/{orderId}/assign/admin/sourcer/{userId}")
            .add("/api/v2/admin/concierge/purchase-order/{orderId}/assign/sales")
            .add("/api/v2/admin/concierge/purchase-order/{orderId}/assign/sourcer")
            .add("/api/v2/admin/concierge/purchase-order/{orderId}/comments")
            .add("/api/v2/admin/concierge/purchase-order/{orderId}/comments/{id}")
            .add("/api/v2/admin/concierge/purchase-order/{orderId}/comments/{id}/pin")
            .add("/api/v2/admin/concierge/purchase-order/{orderId}/history")
            .add("/api/v2/admin/concierge/purchase-order/{orderId}/transition")
            .add("/api/v2/admin/concierge/shipments/purchase-order/{orderId}")
            .add("/api/v2/admin/concierge/shipments/{shipmentId}")
            .add("/api/v2/admin/concierge/{orderId}/rejection")
            .add("/api/v2/admin/concierge/{orderId}/{comment}/returnToWork")
            .add("/api/v2/admin/estimation/orders/{orderId}")
            .add("/api/v2/admin/estimation/orders/{orderId}/expertise")
            .add("/api/v2/admin/estimation/orders/{orderId}/history")
            .add("/api/v2/admin/integration/logistic/export/orders/{orderId}")
            .add("/api/v2/admin/integration/logistic/orders/{orderId}")
            .add("/api/v2/admin/mercaux/concierge/purchase-order/{orderId}")
            .add("/api/v2/admin/mercaux/concierge/purchase-order/{orderId}/transition")
            .add("/api/v2/admin/mercaux/concierge/shipments/purchase-order/{orderId}")
            .add("/api/v2/admin/mercaux/offer/{productId}")
            .add("/api/v2/admin/offer/{productId}")
            .add("/api/v2/admin/orders/act-of-acceptance/pdf/{orderPositionIds}")
            .add("/api/v2/admin/orders/act-of-return/pdf/{orderPositionIds}")
            .add("/api/v2/admin/orders/logistic/act/pdf/{orderIds}")
            .add("/api/v2/admin/orders/orderpositions/{orderPositionId}/certif/pdf")
            .add("/api/v2/admin/orders/orderpositions/{orderPositionId}/confirm")
            .add("/api/v2/admin/orders/orderpositions/{orderPositionId}/decline")
            .add("/api/v2/admin/orders/orderpositions/{orderPositionId}/non-auth-certif/pdf")
            .add("/api/v2/admin/orders/waybills/{waybillId}/tkStickers/pdf")
            .add("/api/v2/admin/orders/waybills/{waybillId}/tkWaybill/pdf")
            .add("/api/v2/admin/orders/{orderId}")
            .add("/api/v2/admin/orders/{orderId}/add-buyer-address-endpoint")
            .add("/api/v2/admin/orders/{orderId}/add-seller-address-endpoint")
            .add("/api/v2/admin/orders/{orderId}/bankoperations")
            .add("/api/v2/admin/orders/{orderId}/change-product-item")
            .add("/api/v2/admin/orders/{orderId}/confirm-delivery-to-buyer")
            .add("/api/v2/admin/orders/{orderId}/dispute-history")
            .add("/api/v2/admin/orders/{orderId}/docs")
            .add("/api/v2/admin/orders/{orderId}/history")
            .add("/api/v2/admin/orders/{orderId}/logistic/details")
            .add("/api/v2/admin/orders/{orderId}/logistic/tracking")
            .add("/api/v2/admin/orders/{orderId}/order-payments")
            .add("/api/v2/admin/orders/{orderId}/oskWaybillFromSeller/pdf")
            .add("/api/v2/admin/orders/{orderId}/oskWaybillToBuyer/pdf")
            .add("/api/v2/admin/orders/{orderId}/payments")
            .add("/api/v2/admin/orders/{orderId}/pickup-options")
            .add("/api/v2/admin/orders/{orderId}/reset-last-synced-state")
            .add("/api/v2/admin/primary/customizablepage/setting/{id}")
            .add("/api/v2/admin/primary/page/setting/{id}")
            .add("/api/v2/admin/primary/promobanner/create/bannersetting/{bannerSettingId}")
            .add("/api/v2/admin/primary/promobanner/{id}")
            .add("/api/v2/admin/product/items/{productItemId}")
            .add("/api/v2/admin/productCollection/delete/{collectionId}")
            .add("/api/v2/admin/saleRequests/{requestId}")
            .add("/api/v2/admin/saleRequests/{requestId}/comments")
            .add("/api/v2/admin/saleRequests/{requestId}/comments/{commentId}")
            .add("/api/v2/admin/saleRequests/{requestId}/comments/{commentId}/pin")
            .add("/api/v2/admin/saleRequests/{requestId}/comments/{commentId}/unpin")
            .add("/api/v2/admin/saleRequests/{requestId}/history")
            .add("/api/v2/admin/saleRequests/{requestId}/order")
            .add("/api/v2/admin/saleRequests/{requestId}/product")
            .add("/api/v2/admin/saleRequests/{requestId}/product/{productId}")
            .add("/api/v2/admin/saleRequests/{requestId}/product/{productId}/cancel-decision")
            .add("/api/v2/admin/saleRequests/{requestId}/product/{productId}/confirm")
            .add("/api/v2/admin/saleRequests/{requestId}/product/{productId}/decline")
            .add("/api/v2/admin/saleRequests/{requestId}/send-to-bitrix")
            .add("/api/v2/admin/saleRequests/{requestId}/state")
            .add("/api/v2/admin/segment/upload/csv/{segmentId}")
            .add("/api/v2/admin/segment/{segmentId}")
            .add("/api/v2/admin/service/order/{orderId}/fix-payout")
            .add("/api/v2/admin/social/configured-feed-sections/{id}")
            .add("/api/v2/admin/social/configured-feed-sections/{id}/move")
            .add("/api/v2/admin/social/feed/objects-collections/{id}")
            .add("/api/v2/admin/social/posts/{postId}")
            .add("/api/v2/admin/social/posts/{postId}/activity")
            .add("/api/v2/admin/social/posts/{postId}/approve")
            .add("/api/v2/admin/social/posts/{postId}/comments")
            .add("/api/v2/admin/social/posts/{postId}/comments/media")
            .add("/api/v2/admin/social/posts/{postId}/comments/{commentId}")
            .add("/api/v2/admin/social/posts/{postId}/comments/{commentId}/restore")
            .add("/api/v2/admin/social/posts/{postId}/delete")
            .add("/api/v2/admin/social/posts/{postId}/hide")
            .add("/api/v2/admin/social/posts/{postId}/history")
            .add("/api/v2/admin/social/posts/{postId}/rating/explanation")
            .add("/api/v2/admin/social/posts/{postId}/reject")
            .add("/api/v2/admin/social/posts/{postId}/restore")
            .add("/api/v2/admin/social/posts/{postId}/short")
            .add("/api/v2/admin/social/posts/{postId}/show")
            .add("/api/v2/admin/social/tags/{tagId}/name")
            .add("/api/v2/admin/social/tags/{tagId}/pinned/order")
            .add("/api/v2/admin/social/tags/{tagId}/status")
            .add("/api/v2/admin/social/users/{userId}/activity/posts")
            .add("/api/v2/admin/social/users/{userId}/activity/posts/count")
            .add("/api/v2/admin/social/users/{userId}/moderation/rules")
            .add("/api/v2/agentreports/confirm/{agentReportId}")
            .add("/api/v2/agentreports/pdf/{agentReportId}")
            .add("/api/v2/bans/all/{banType}")
            .add("/api/v2/bans/all/{userId}/{banType}")
            .add("/api/v2/bans/{banId}")
            .add("/api/v2/bans/{userId}/all")
            .add("/api/v2/bargains/bargain-or-template/{productId}/{sizeId}")
            .add("/api/v2/bargains/{id}")
            .add("/api/v2/bargains/{productId}/{sizeId}")
            .add("/api/v2/cart/items/{orderPositionId}")
            .add("/api/v2/catalog/brands/liked/{userId}")
            .add("/api/v2/catalog/brands/liked/{userId}/page")
            .add("/api/v2/catalog/brands/name/{urlName}")
            .add("/api/v2/catalog/brands/{brandId}")
            .add("/api/v2/catalog/brands/{brandId}/dislike")
            .add("/api/v2/catalog/brands/{brandId}/like")
            .add("/api/v2/catalog/brands/{brandId}/toggle")
            .add("/api/v2/catalog/categories/{categoryId}")
            .add("/api/v2/catalog/products/liked-page/{userId}")
            .add("/api/v2/catalog/products/liked/{userId}")
            .add("/api/v2/catalog/products/{productId}")
            .add("/api/v2/catalog/products/{productId}/blog")
            .add("/api/v2/catalog/products/{productId}/dislike")
            .add("/api/v2/catalog/products/{productId}/followPrice")
            .add("/api/v2/catalog/products/{productId}/images")
            .add("/api/v2/catalog/products/{productId}/like")
            .add("/api/v2/catalog/products/{productId}/lite")
            .add("/api/v2/catalog/products/{productId}/toggle")
            .add("/api/v2/catalog/products/{productId}/toggle/dislike")
            .add("/api/v2/catalog/products/{productId}/toggle/like")
            .add("/api/v2/catalog/products/{productId}/unfollowPrice")
            .add("/api/v2/catalog/sizes/{categoryId}")
            .add("/api/v2/comments/product/{productId}")
            .add("/api/v2/comments/product/{productId}/tree")
            .add("/api/v2/comments/{commentId}")
            .add("/api/v2/community/privileges-requirements/{userId}")
            .add("/api/v2/concierge/purchase-order/{orderId}")
            .add("/api/v2/concierge/purchase-order/{orderId}/transition")
            .add("/api/v2/home/<USER>/promo-banner/{bannerSettingId}")
            .add("/api/v2/integration/orderprocessing/orders/positions/{orderPositionId}/confirmation")
            .add("/api/v2/integration/orderprocessing/orders/{orderId}/change-dto")
            .add("/api/v2/integration/orderprocessing/orders/{orderId}/export")
            .add("/api/v2/integration/orderprocessing/orders/{orderId}/export/orderprocessing")
            .add("/api/v2/filter-subscriptions/{subscriptionId}")
            .add("/api/v2/notifications/{notificationId}")
            .add("/api/v2/notifications/{notificationId}/birthdate")
            .add("/api/v2/notifications/{notificationId}/complete")
            .add("/api/v2/notifications/{notificationId}/read")
            .add("/api/v2/offers/{kind}/count")
            .add("/api/v2/offers/{offerId}")
            .add("/api/v2/offers/{offerId}/consume")
            .add("/api/v2/orders/returns/{returnInfoId}")
            .add("/api/v2/orders/{orderId}")
            .add("/api/v2/orders/{orderId}/availablePickupDates")
            .add("/api/v2/orders/{orderId}/confirm")
            .add("/api/v2/orders/{orderId}/confirmDelivery")
            .add("/api/v2/orders/{orderId}/confirmPosition")
            .add("/api/v2/orders/{orderId}/create-payout-request")
            .add("/api/v2/orders/{orderId}/deliveryAddressEndpoint/{deliveryAddressEndpointId}")
            .add("/api/v2/orders/{orderId}/deliveryAddressEndpointAggregation/{deliveryAddressEndpointAggregationId}")
            .add("/api/v2/orders/{orderId}/deliveryComment")
            .add("/api/v2/orders/{orderId}/documents")
            .add("/api/v2/orders/{orderId}/documents/{documentName}.{fmt}")
            .add("/api/v2/orders/{orderId}/hold")
            .add("/api/v2/orders/{orderId}/pickupAddressEndpoint")
            .add("/api/v2/orders/{orderId}/pickupAddressEndpointAggregation")
            .add("/api/v2/orders/{orderId}/pickupComment")
            .add("/api/v2/orders/{orderId}/pickupDateAndTimeInterval")
            .add("/api/v2/orders/{orderId}/pickupTimeInterval")
            .add("/api/v2/orders/{orderId}/refuse-payout-request")
            .add("/api/v2/orders/{orderId}/sellerCounterparty")
            .add("/api/v2/productRequest/comments/{commentId}")
            .add("/api/v2/productRequest/comments/{productRequestId}")
            .add("/api/v2/productRequest/comments/{productRequestId}/tree")
            .add("/api/v2/productRequests/dislike/{productRequestId}")
            .add("/api/v2/productRequests/like/{productRequestId}")
            .add("/api/v2/productRequests/liked-page/{userId}")
            .add("/api/v2/productRequests/publish/attributes/{categoryId}")
            .add("/api/v2/productRequests/publish/request/{productRequestId}")
            .add("/api/v2/productRequests/{productRequestId}")
            .add("/api/v2/productpublication/additional-sizes/{categoryId}")
            .add("/api/v2/productpublication/attributes/{categoryId}")
            .add("/api/v2/productpublication/categories/{categoryId}")
            .add("/api/v2/productpublication/product/{productId}")
            .add("/api/v2/productpublication/samples/{categoryId}")
            .add("/api/v2/productpublication/sizes/{categoryId}")
            .add("/api/v2/productpublication/step/{productId}")
            .add("/api/v2/products/search/photo/{searchId}")
            .add("/api/v2/publicprofile/following/{userId}")
            .add("/api/v2/publicprofile/{userId}")
            .add("/api/v2/saleRequests/{requestId}")
            .add("/api/v2/social/discovery/posts/{postId}/similar/by-image")
            .add("/api/v2/social/feed/sections/{code}")
            .add("/api/v2/social/media/{id}/similar-products/{token}")
            .add("/api/v2/social/posts/{id}")
            .add("/api/v2/social/posts/{id}/full")
            .add("/api/v2/social/posts/{id}/liked-users")
            .add("/api/v2/social/posts/{id}/mentioned-users")
            .add("/api/v2/social/posts/{postId}/comments")
            .add("/api/v2/social/posts/{postId}/comments/extended-result")
            .add("/api/v2/social/posts/{postId}/comments/{commentId}")
            .add("/api/v2/social/posts/{postId}/comments/{commentId}/extended-result")
            .add("/api/v2/social/posts/{postId}/like")
            .add("/api/v2/social/posts/{postId}/shared")
            .add("/api/v2/social/posts/{postId}/unlike")
            .add("/api/v2/social/posts/{postId}/viewed")
            .add("/api/v2/social/tags/{id}")
            .add("/api/v2/social/users/favorites/posts/{postId}")
            .add("/api/v2/social/users/tags/{tagId}")
            .add("/api/v2/social/users/{userId}/favorites/posts")
            .add("/api/v2/social/users/{userId}/tags")
            .add("/api/v3/admin/commissionGrid/listForUser/{userId}")
            .add("/api/v3/admin/commissionGrid/{id}")
            .add("/api/v3/admin/commissionGrid/{id}/rules")
            .add("/api/v3/admin/dictionary/categoryTree/{productId}")
            .add("/api/v3/admin/dictionary/sizesForCategory/{categoryId}")
            .add("/api/v3/admin/discount/start/{productId}")
            .add("/api/v3/admin/discount/stop/{productId}")
            .add("/api/v3/admin/product/getFullById/{id}")
            .add("/api/v3/admin/productComments/product/{productId}")
            .add("/api/v3/admin/productComments/product/{productId}/tree")
            .add("/api/v3/admin/productComments/restore/{commentId}")
            .add("/api/v3/admin/productComments/{commentId}")
            .add("/api/v3/admin/segments/user/{userId}")
            .add("/api/v3/admin/segments/{segmentId}")
            .add("/api/v3/admin/segments/{segmentId}/users")
            .add("/api/v3/admin/user/address/{userId}")
            .add("/api/v3/admin/user/counterparty/getForUser/{userId}")
            .add("/api/v3/admin/userCommonTags/allGroupsWithUserChecked/{userId}")
            .add("/api/v3/admin/userCommonTags/byGroup/{groupId}/usersCount")
            .add("/api/v3/admin/userCommonTags/byUser/{userId}")
            .add("/api/v3/admin/users/activity/{userId}")
            .add("/api/v3/admin/users/bonuses/balance-brief/{userId}")
            .add("/api/v3/admin/users/bonuses/balance-burning-schedule/{userId}")
            .add("/api/v3/admin/users/bonuses/history/{userId}")
            .add("/api/v3/admin/users/bonuses/templates/{code}")
            .add("/api/v3/admin/users/community/forceUpdateUserStatus/{userId}")
            .add("/api/v3/admin/users/community/userStatusHistory/{userId}")
            .add("/api/v3/admin/users/loyalty/accounts/{userId}/history")
            .add("/api/v3/admin/users/loyalty/accounts/{userId}/status")
            .add("/api/v3/admin/users/loyalty/accounts/{userId}/available-statuses")
            .add("/api/v3/admin/users/loyalty/accounts/{userId}")
            .add("/api/v3/admin/users/counters/{userId}")
            .add("/api/v3/admin/users/restore/{userId}")
            .add("/api/v3/admin/users/userDebt/{userId}")
            .add("/api/v3/admin/users/{userId}")
            .add("/api/v3/admin/users/{userId}/contracts/upload")
            .add("/api/v3/admin/users/{userId}/contracts/{contractId}")
            .add("/brands/{baseCategory}")
            .add("/brands/{baseCategory}/{url}")
            .add("/catalog/banner/{bannerId}")
            .add("/catalog/banner/{bannerId}/template")
            .add("/catalog/set/{setId}")
            .add("/catalog/set/{setId}/template")
            .add("/mobile/info/{url}")
            .add("/orders/{id}")
            .add("/orders/{id}/thanks")
            .add("/products/{id}/size_match")
            .add("/profile/{userId}/**")
            .add("/api/whatsapp/salesmen/{userId}/chats/{chatId}")
            .add("/api/v2/purchaserActivity/{userId}/counter")
            .add("/api/v2/product-selection-requests/{requestId}/send-to-client")
            .add("/api/v2/purchasers/{id}/counter")
            .add("/api/v2/purchaserActivity/{userId}/favourites")
            .add("/api/v2/purchaserActivity/cart/{userId}")
            .add("/api/v2/product-selection-requests/{requestId}/selections")
            .add("/api/v2/product-selection-requests/{id}")
            .add("/api/v2/admin/sales-plan/{storeName}")
            .add("/api/v2/admin/sales-plan/{id}")
            .add("/api/v2/purchasers/{id}")
            .add("/api/v2/admin/salesman/{salesmanId}/clients")
            .add("/api/v2/social/users/purchasers/{userId}/favorites/posts")
            .add("/api/whatsapp/salesmen/{userId}/chats/{chatId}/{messageId}")
            .add("/api/v2/product-selection-requests/{requestId}/send")
            .add("/api/v2/selections/{id}")
            .add("/api/v2/purchaserActivity/{userId}/comments")
            .add("/api/v2/orders/purchaser/{userId}/cancel")
            .add("/api/v2/orders/purchaser/{userId}/boutique/from-selections")
            .add("/api/v2/selections/{id}/activities")
            .add("/api/v2/selections/purchaser/{purchaserId}")
            .add("/api/v2/purchasers/{id}/full")
            .add("/api/v2/catalog/products/liked-page/boutique/{userId}")
            .add("/api/v2/purchaserActivity/{userId}/productResponses")
            .add("/api/v2/admin/sales-plan/salesman/{id}")
            .add("/api/v2/notes/purchaser/{purchaserId}")
            .add("/api/v2/purchaserActivity/{userId}/search-history")
            .add("/api/v2/admin/work-schedule/settings/salesman/{salesmanId}")
            .add("/api/v2/orders/purchaser/{userId}/online/from-selections")
            .add("/api/v2/purchasers/{id}/events/{eventId}")
            .add("/api/v2/salesman/segments/{id}")
            .add("/api/v2/selections/{id}/purchaser-view")
            .add("/api/v2/purchaserActivity/{userId}/bargains")
            .add("/api/v2/admin/work-schedule/settings/salesman/{salesmanId}/update")
            .add("/api/v2/purchasers/{id}/events")
            .add("/api/v2/notes/common/{noteId}")
            .add("/api/v2/admin/work-schedule/day/{workScheduleDayId}/update")
            .add("/api/v2/orders/purchaser/{userId}/boutique")
            .add("/api/v2/orders/purchaser/{userId}/online")
            .add("/api/v2/notes/purchaser/{noteId}")
            .add("/api/v2/purchaserActivity/{userId}/productRequests")
            .add("/api/v2/purchasers/{id}/personalInfo")
            .build();

}
