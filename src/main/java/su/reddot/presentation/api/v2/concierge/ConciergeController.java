package su.reddot.presentation.api.v2.concierge;

import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import su.reddot.domain.exception.OskellyException;
import su.reddot.domain.exception.PhoneException;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.service.concierge.ConciergeService;
import su.reddot.domain.service.concierge.model.ConciergeOrderDetailsResponse;
import su.reddot.domain.service.concierge.model.PurchaseOrderFullDTO;
import su.reddot.domain.service.concierge.model.RejectionEventRequest;
import su.reddot.domain.service.concierge.oskellyconcierge.PurchaseOrderService;
import su.reddot.domain.service.dto.CurrencyDTO;
import su.reddot.domain.service.dto.CurrencyRateWithEffectiveRateDTO;
import su.reddot.domain.service.dto.concierge.ConciergeApplicationFormDto;
import su.reddot.domain.service.dto.concierge.OrderInformationDto;
import su.reddot.domain.service.dto.concierge.PurchaseOrderMobileCreateRequest;
import su.reddot.domain.service.dto.concierge.PurchaseOrderMobileUpdateRequest;
import su.reddot.domain.service.productpublication.Conversion;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.presentation.PhoneUtils;
import su.reddot.presentation.api.v2.Api2Response;

import java.math.BigDecimal;
import java.util.List;
import java.util.function.Consumer;

@RestController
@RequiredArgsConstructor
@Slf4j
public class ConciergeController implements ConciergeControllerDelegate {
    private final ConciergeService conciergeService;
    private final PhoneUtils phoneUtils;
    private final MessageSourceAccessor messageSourceAccessor;
    private final PurchaseOrderService purchaseOrderService;
    private final SecurityService securityService;

    @Value("${internationalVersion}")
    private Boolean isInternationalVersion;


    @Override
    public Api2Response<List<String>> uploadItemImage(List<MultipartFile> images) {
        Api2Response<List<String>> response = Api2Response.create();
        return response.success(conciergeService.uploadImage(images));
    }

    @Override
    public Api2Response<String> submitApplication(ConciergeApplicationFormDto applicationForm) {
        return submitApplication(applicationForm, conciergeService::submitApplication);
    }

    @Override
    public Api2Response<String> submitSellerApplication(ConciergeApplicationFormDto applicationForm) {
        return submitApplication(applicationForm, conciergeService::submitSellerApplication);
    }

    private Api2Response<String> submitApplication(ConciergeApplicationFormDto applicationForm,
                                                   Consumer<ConciergeApplicationFormDto> submitFunction) {
        Api2Response<String> response = Api2Response.create();
        if (StringUtils.isEmpty(applicationForm.getClientName()))
            throw new OskellyException(messageSourceAccessor.getMessage("presentation.api.v2.concierge.ConciergeController.NoName"));
        if (StringUtils.isEmpty(applicationForm.getPhoneNumber()))
            throw new OskellyException(messageSourceAccessor.getMessage("presentation.api.v2.concierge.ConciergeController.NoPhone"));
        try {
            if (!isInternationalVersion) {
                // позже этап подготовки номера можно пропустить
                // пока его надо оставить для обратной совместимости со старыми версиями клиента на iOS
                String fixedPhone = phoneUtils.cleanAndPreparePhone(applicationForm.getPhoneNumber());
                phoneUtils.validatePhone(fixedPhone);
                applicationForm.setPhoneNumber(fixedPhone);
            }
            submitFunction.accept(applicationForm);
        } catch (PhoneException e) {
            log.error("Unable to parse phone number from request to concierge-service", e);
            return Api2Response.error(e);
        } catch (Exception e) {
            log.error("Unable to send request to concierge-service", e);
            return Api2Response.error(e, "Unable to send request to concierge-service", "Unable to send request to concierge-service");
        }
        return response.success("Application was submitted", "OK");
    }

    @Override
    public ResponseEntity<OrderInformationDto> getOrderInformationCallback(@NonNull Long orderId) {
        try {
            return ResponseEntity.ok(new OrderInformationDto(conciergeService.getOrderInfoById(orderId)));
        } catch (Exception e) {
            log.error("Couldn't obtain information about order with orderId: " + orderId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /*
     * Методы относящиеся к консьерж-сервису для покупателя
     */

    /**
     * Получение заявки на покупку из сервиса консьержа
     *
     * @param orderId id заявки
     * @return данные по заявке
     */
    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<ConciergeOrderDetailsResponse> getPurchaseOrderApp(long orderId) {
        Api2Response<ConciergeOrderDetailsResponse> response = new Api2Response<>();
        return response.success("Get Purchase Order successfully",
                purchaseOrderService.getPurchaseOrderForApp(orderId));
    }

    /**
     * Заведение новой заявки на покупку
     *
     * @param request запрос создания заявки
     * @return данные по заявке
     */
    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<PurchaseOrderFullDTO> createPurchaseOrder(PurchaseOrderMobileCreateRequest request) {
        Api2Response<PurchaseOrderFullDTO> response = new Api2Response<>();
        return response.success("Purchase order created",
                purchaseOrderService.createPurchaseOrder(request));
    }

    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<PurchaseOrderFullDTO> purchaseOrderEventSend(Long orderId, String eventCode) {
        Api2Response<PurchaseOrderFullDTO> response = new Api2Response<>();
        return response.success("Purchase order event sent",
                purchaseOrderService.eventSendForUser(orderId, eventCode));
    }

    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<PurchaseOrderFullDTO> updatePurchaseOrder(Long orderId,
                                                                  PurchaseOrderMobileUpdateRequest request) {
        Api2Response<PurchaseOrderFullDTO> response = new Api2Response<>();

        return response.success("Purchase order updated",
                purchaseOrderService.updatePurchaseOrder(orderId, request));
    }

    @Override
    public Api2Response<ConciergeOrderDetailsResponse> createRejectionEventApp(RejectionEventRequest request, Long orderId) {
        return new Api2Response<ConciergeOrderDetailsResponse>()
                .success("Rejection event created", purchaseOrderService.createRejectionEventToApp(request, orderId));
    }

    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<?> getAllCurrencies() {
        Api2Response<List<CurrencyDTO>> response = new Api2Response<>();
        return response.success("Все валюты", conciergeService.getAllCurrenciesForConciergeToPayment());
    }

    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<List<CurrencyRateWithEffectiveRateDTO>> getAllRates() {
        Api2Response<List<CurrencyRateWithEffectiveRateDTO>> response = new Api2Response<>();
        return response.success("Все курсы валют", conciergeService.getAllRates());
    }

    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<BigDecimal> getPriceWithoutCommissionInBaseCurrency(String currencyCode, BigDecimal priceInCurrency, Long shopperId) {
        Long effectiveUserId = shopperId;
        if (effectiveUserId == null) {
            effectiveUserId = securityService.getCurrentAuthorizedUserId();
        }

        Api2Response<BigDecimal> response = new Api2Response<>();
        return response.success("Цена успешно конвертирована", conciergeService.getPriceWithoutCommissionInBaseCurrency(currencyCode, priceInCurrency, effectiveUserId));
    }

    @Override
    @PreAuthorize("isFullyAuthenticated()")
    public Api2Response<Conversion> getConversion(BigDecimal priceWithoutCommission, BigDecimal priceWithCommission, SalesChannel salesChannel, Long sellerId, String currencyCode) {
        Api2Response<Conversion> response = new Api2Response<>();
        return response.success("Преобразование успешно получено", conciergeService.getConversionValue(priceWithoutCommission, priceWithCommission, salesChannel, sellerId, currencyCode));
    }
}
