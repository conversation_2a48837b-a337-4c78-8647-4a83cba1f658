package su.reddot.presentation.api.v2;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import com.google.recaptchaenterprise.v1.Assessment;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.dao.address.CountryRepository;
import su.reddot.domain.exception.PhoneAlreadyExistException;
import su.reddot.domain.model.address.Country;
import su.reddot.domain.service.dto.verification.GenerationPayload;
import su.reddot.domain.service.dto.verification.VerificationPayload;
import su.reddot.domain.service.user.UserService;
import su.reddot.domain.service.verification.GenerationPayloadDto;
import su.reddot.domain.service.verification.PhoneNumberVerificationResultDto;
import su.reddot.domain.service.verification.VerificationMapper;
import su.reddot.domain.service.verification.VerificationPayloadDto;
import su.reddot.domain.service.verification.VerificationService;
import su.reddot.infrastructure.captcha.recaptcha.RecaptchaService;
import su.reddot.infrastructure.captcha.turnstile.TurnstileService;
import su.reddot.infrastructure.security.SecurityService;
import su.reddot.infrastructure.util.ProductionEnvironment;
import su.reddot.presentation.PhoneUtils;

import javax.validation.Valid;
import java.util.UUID;

@Slf4j
@Validated
@RestController
@RequestMapping("/api/v2/verification")
@RequiredArgsConstructor
public class VerificationControllerApiV2 {
    private final VerificationService verificationService;
    private final SecurityService securityService;
    private final VerificationMapper verificationMapper;
    private final UserService userService;
    private final MessageSourceAccessor messageSourceAccessor;
    private final CountryRepository countryRepository;
    private final TurnstileService turnstileService;
    private final RecaptchaService recaptchaService;
    @Value("${internationalVersion}")
    private Boolean isInternationalVersion;

    /**
     * Проверяет номер телефона на существование и отправляет запрос на генерацию кода подтверждения
     *
     * @param payload объект с полями phoneNumber, operationId, context
     * @return UUID токен для подтверждения операции
     */
    @PostMapping("/generate")
    public Api2Response<String> generateCode(@RequestBody @Valid GenerationPayloadDto payload,
            @RequestHeader(value = "Turnstile-Token", required = false) String turnstileToken,
            @RequestHeader(value = "Recaptcha-Token", required = false) String recaptchaToken,
            @RequestHeader(value = HttpHeaders.USER_AGENT, required = false) String userAgent,
            @RequestHeader(value = "CF-Connecting-IP", required = false) String ipAddress) throws Exception {
        try {
            log.info("Phone verification. Got request on /generate " + payload);
            turnstileService.verifyTokenAndThrowErrorForUserAgentIfNeeded(turnstileToken, userAgent, ipAddress);

            Api2Response<String> response = Api2Response.create();
            String normalizedPhoneNumber = PhoneUtils.normalizePhoneNumberOrThrowException(payload.getPhoneNumber(), "VerificationController.generateCode", messageSourceAccessor, log);
            payload.setPhoneNumber(normalizedPhoneNumber);
            if (payload.getOperation() == GenerationPayload.OperationEnum.VERIFY_PHONE_NUMBER &&
                    userService.isPhoneExistAndVerified(payload.getPhoneNumber())) {
                log.error("Trying verify already verified phone number: {} ({}) // {}",
                        payload.getPhoneNumber(),
                        securityService.getCurrentAuthorizedUserId(),
                        payload);
                throw new PhoneAlreadyExistException(messageSourceAccessor.getMessage("exception.user.phone.exist"));
            }

            String guestToken = securityService.getGuestToken();
            Long userId = securityService.getCurrentAuthorizedUserId();

            Assessment recaptchaAssessment = recaptchaService.createAndVerifyAssessment(
                recaptchaToken, userAgent, userId, normalizedPhoneNumber
            );

            ProductionEnvironment environment = findCountryEnvironmentByPhone(normalizedPhoneNumber);
            String clientRemoteAddr = securityService.getClientRemoteAddress();

            GenerationPayload dto = verificationMapper.toDto(payload, userId, guestToken, environment, clientRemoteAddr);

            UUID token = verificationService.generateCode(dto);
            if (recaptchaAssessment != null && token != null) {
                recaptchaService.onGenerateCode(token.toString(), recaptchaAssessment);
            }

            Api2Response<String> handledResponse = token != null
                    ? response.success(messageSourceAccessor.getMessage("exception.verification.generate.success"), String.valueOf(token))
                    : Api2Response.error(new RuntimeException(messageSourceAccessor.getMessage("exception.verification.generate.failed")));
            log.info("Phone verification. Response on /generate " + handledResponse);
            return handledResponse;
        } catch (Exception e) {
            log.error("Phone verification. Error when handling request on /generate " + payload, e);
            throw e;
        }
    }

    private ProductionEnvironment findCountryEnvironmentByPhone(String phone) throws NumberParseException {
        String isoAlpha2 = findCountryISOAlpha2ByPhone(phone);
        if (isoAlpha2 == null) {
            return getDefaultEnv();
        }

        Country country = countryRepository.findByIsoCodeAlpha2(isoAlpha2);
        if (country != null && country.getEnvironment() != null) {
            return country.getEnvironment();
        }
        
        return getDefaultEnv();
    }

    private String findCountryISOAlpha2ByPhone(String phone) {
        PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();

        String defaultRegion = isInternationalVersion ? "AE" : "RU";
        Phonenumber.PhoneNumber phoneNumber = null;
        try {
            phoneNumber = phoneUtil.parseAndKeepRawInput(phone, defaultRegion);
        } catch (NumberParseException e) {
            return null;
        }

        return phoneUtil.getRegionCodeForNumber(phoneNumber);
    }

    private ProductionEnvironment getDefaultEnv() {
        return isInternationalVersion != null ?
                (isInternationalVersion ? ProductionEnvironment.INT : ProductionEnvironment.RU)
                : ProductionEnvironment.RU;
    }

    /**
     * Верифицирует номер телефона, проверяя код подтверждения
     *
     * @param payload объект с полями phoneNumber, operationId, context, token, code
     * @return true если номер телефона верифицирован, false если нет
     */
    @PreAuthorize("isFullyAuthenticated()")
    @PostMapping("/verifyPhoneNumber")
    public Api2Response<Boolean> verifyPhoneNumber(@RequestBody @Valid VerificationPayloadDto payload) {
        try {
            log.info("Phone verification. Got request on /verifyPhoneNumber " + payload);
            Api2Response<Boolean> response = Api2Response.create();
            VerificationPayload dto = verificationMapper.toDtoConfirmation(payload);
            boolean isVerified = verificationService.verifyPhoneNumber(dto);
            recaptchaService.onVerifyCode(payload.getToken(), isVerified);
            Api2Response<Boolean> handledResponse = response.success(isVerified);
            log.info("Phone verification. Response on /verifyPhoneNumber " + handledResponse);
            return handledResponse;
        } catch (Exception e) {
            log.error("Phone verification. Error when handling request on /verifyPhoneNumber " + payload, e);
            throw e;
        }
    }

    @PreAuthorize("isFullyAuthenticated()")
    @PostMapping("/verifyPhoneNumberAndGetReAuthToken")
    public Api2Response<PhoneNumberVerificationResultDto> verifyPhoneNumberAndGetReAuthToken(@RequestBody @Valid VerificationPayloadDto payload) {
        try {
            log.info("Phone verification. Got request on /verifyPhoneNumberAndGetReauthToken " + payload);
            Api2Response<PhoneNumberVerificationResultDto> response = Api2Response.create();
            VerificationPayload dto = verificationMapper.toDtoConfirmation(payload);
            boolean isVerified = verificationService.verifyPhoneNumber(dto);
            recaptchaService.onVerifyCode(payload.getToken(), isVerified);
            Api2Response<PhoneNumberVerificationResultDto> handledResponse;
            if (!isVerified) {
                return response.success(new PhoneNumberVerificationResultDto().setPhoneNumberVerificationSuccess(false));
            } else {
                String reAuthToken = securityService.generatePhoneNumberReAuthJwtTokenForCurrentUser();
                handledResponse = response.success(new PhoneNumberVerificationResultDto()
                        .setPhoneNumberVerificationSuccess(true)
                        .setReAuthToken(reAuthToken));
            }
            log.info("Phone verification. Response on /verifyPhoneNumberAndGetReauthToken {}", handledResponse);
            return handledResponse;
        } catch (Exception e) {
            log.error("Phone verification. Error when handling request on /verifyPhoneNumberAndGetReauthToken " + payload, e);
            throw e;
        }
    }

    @PostMapping("/verify")
    public Api2Response<Boolean> verify(@RequestBody @Valid VerificationPayloadDto payload) {
        try {
            log.info("Simple OTP verification. Got request on /verify " + payload);
            Api2Response<Boolean> response = Api2Response.create();
            String normalizedPhoneNumber = PhoneUtils.normalizePhoneNumberOrThrowException(payload.getPhoneNumber(), "/verify", messageSourceAccessor, log);
            payload.setPhoneNumber(normalizedPhoneNumber);
            String guestToken = securityService.getGuestToken();
            Long userId = securityService.getCurrentAuthorizedUserId();
            VerificationPayload dto = verificationMapper.toDtoConfirmation(payload, userId, guestToken);
            boolean isVerified = verificationService.verifyCode(dto);
            recaptchaService.onVerifyCode(payload.getToken(), isVerified);
            Api2Response<Boolean> handledResponse = response.success(isVerified);
            log.info("Phone verification. Response on /verify " + handledResponse);
            return handledResponse;
        } catch (Exception e) {
            log.error("Phone verification. Error when handling request on /verify " + payload, e);
            throw e;
        }
    }

    /**
     * Верифицирует номер телефона, проверяя код подтверждения
     *
     * @param payload объект с полями phoneNumber, operationId, context, token, code
     * @return jwt токен с информацией о проверки номера телефона
     */
    @PostMapping("/verifyPhoneNumberAndGetJWT")
    public Api2Response<String> verifyPhoneNumberWithJWT(@RequestBody @Valid VerificationPayloadDto payload) {
        Api2Response<String> response = Api2Response.create();
        String normalizedPhoneNumber = PhoneUtils.normalizePhoneNumberOrThrowException(payload.getPhoneNumber(), "VerificationController.verifyPhoneNumberWithJWT", messageSourceAccessor, log);
        payload.setPhoneNumber(normalizedPhoneNumber);
        String guestToken = securityService.getGuestToken();
        Long userId = securityService.getCurrentAuthorizedUserId();
        VerificationPayload dto = verificationMapper.toDtoConfirmation(payload, userId, guestToken);

        try {
            String jwtToken = verificationService.verifyCodeAndGenerateJWT(dto);
            recaptchaService.onVerifyCode(payload.getToken(), true);
            return response.success("Verification jwt token", jwtToken);
        } catch (BadCredentialsException bce) {
            recaptchaService.onVerifyCode(payload.getToken(), false);
            throw bce;
        }
    }

}
