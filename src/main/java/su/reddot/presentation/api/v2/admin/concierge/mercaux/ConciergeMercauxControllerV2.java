package su.reddot.presentation.api.v2.admin.concierge.mercaux;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.concierge.model.MercauxPurchaseOrderFullDTO;
import su.reddot.domain.service.concierge.model.OrdersForConciergeDTO;
import su.reddot.domain.service.concierge.model.PurchaseOrderFilter;
import su.reddot.domain.service.concierge.model.PurchaseOrderFullDTO;
import su.reddot.domain.service.concierge.model.PurchaseOrderWithShipmentsCreateRequest;
import su.reddot.domain.service.concierge.model.RequestsFilter;
import su.reddot.domain.service.concierge.model.ShipmentCommentDTO;
import su.reddot.domain.service.concierge.model.ShipmentRequestDTO;
import su.reddot.domain.service.concierge.model.ShipmentResponseDTO;
import su.reddot.domain.service.concierge.oskellyconcierge.ShipmentService;
import su.reddot.domain.service.concierge.oskellyconcierge.MercauxPurchaseOrderService;
import su.reddot.domain.service.dto.Page;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
public class ConciergeMercauxControllerV2 implements ConciergeMercauxControllerV2Delegate {
    private final ShipmentService shipmentService;
    private final MercauxPurchaseOrderService mercauxPurchaseOrderService;

    @Override
    @PreAuthorize("hasAnyAuthority(" +
        "'CONCIERGE_SALES_ADMIN'," +
        "'SALES'," +
        "'STOLESHNIKOV_ADMIN'," +
        "'STOLESHNIKOV_BOUTIQUE_SALESMAN'," +
        "'KUZNETSKY_BRIDGE_ADMIN'," +
        "'KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN'" +
    ")")
    public Api2Response<PurchaseOrderFullDTO> submitPurchaseOrderMercaux(PurchaseOrderWithShipmentsCreateRequest request) {
        return new Api2Response<PurchaseOrderFullDTO>()
            .success("Purchase order created from Mercaux", mercauxPurchaseOrderService.createPurchaseOrder(request));
    }

    @Override
    @PreAuthorize("hasAnyAuthority(" +
        "'CONCIERGE_SALES_ADMIN'," +
        "'SALES'," +
        "'STOLESHNIKOV_ADMIN'," +
        "'STOLESHNIKOV_BOUTIQUE_SALESMAN'," +
        "'KUZNETSKY_BRIDGE_ADMIN'," +
        "'KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN'" +
    ")")
    public Api2Response<List<ShipmentResponseDTO>> addShipmentsToOrderMercaux(Long orderId, List<ShipmentRequestDTO> shipments) {
        return new Api2Response<List<ShipmentResponseDTO>>().success(
            "Добавлены товары в заявку из приложения Mercaux",
            mercauxPurchaseOrderService.addShipmentsToOrder(orderId, shipments)
        );
    }

    @Override
    @PreAuthorize("hasAnyAuthority('SOURCER', 'CONCIERGE_SOURCERS_ADMIN')")
    public Api2Response<ShipmentResponseDTO> addCommentToShipmentMercaux(ShipmentCommentDTO comment) {
        return new Api2Response<ShipmentResponseDTO>()
                .success("Comment added", shipmentService.addCommentToShipment(comment));
    }

    @Override
    @PreAuthorize("hasAnyAuthority(" +
        "'MERCAUX_ADMIN'," +
        "'CONCIERGE_SALES_ADMIN'," +
        "'SALES'," +
        "'CONCIERGE_SOURCERS_ADMIN'," +
        "'SOURCER'," +
        "'STOLESHNIKOV_ADMIN'," +
        "'STOLESHNIKOV_BOUTIQUE_SALESMAN'," +
        "'KUZNETSKY_BRIDGE_ADMIN'," +
        "'KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN'" +
    ")")
    public Api2Response<Page<OrdersForConciergeDTO>> getPurchaseOrdersMercaux(RequestsFilter filter, String searchText) {
        return new Api2Response<Page<OrdersForConciergeDTO>>().success(
            "Список заявок успешно получен",
            mercauxPurchaseOrderService.getPurchaseOrders(filter, searchText)
        );
    }

    @Override
    @PreAuthorize("hasAnyAuthority(" +
        "'MERCAUX_ADMIN'," +
        "'CONCIERGE_SALES_ADMIN'," +
        "'SALES'," +
        "'CONCIERGE_SOURCERS_ADMIN'," +
        "'SOURCER'," +
        "'STOLESHNIKOV_ADMIN'," +
        "'STOLESHNIKOV_BOUTIQUE_SALESMAN'," +
        "'KUZNETSKY_BRIDGE_ADMIN'," +
        "'KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN'" +
    ")")
    public Api2Response<PurchaseOrderFilter> getFilterPurchaseOrdersMercaux(PurchaseOrderFilter filter) {
        return new Api2Response<PurchaseOrderFilter>().success(
            "Данные фильтра успешно получены",
            mercauxPurchaseOrderService.getFilter(filter)
        );
    }

    @Override
    @PreAuthorize("hasAnyAuthority(" +
        "'CONCIERGE_SALES_ADMIN'," +
        "'SALES'," +
        "'CONCIERGE_SOURCERS_ADMIN'," +
        "'SOURCER'," +
        "'STOLESHNIKOV_ADMIN'," +
        "'STOLESHNIKOV_BOUTIQUE_SALESMAN'," +
        "'KUZNETSKY_BRIDGE_ADMIN'," +
        "'KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN'" +
        ")")
    public Api2Response<PurchaseOrderFullDTO> eventSendMercaux(Long orderId, String eventCode) {
        log.info("Processing event {} for order: {}", eventCode, orderId);

        return new Api2Response<PurchaseOrderFullDTO>().success(
            "Статус изменен",
            mercauxPurchaseOrderService.eventSend(orderId, eventCode)
        );
    }

    @Override
    @PreAuthorize("hasAnyAuthority(" +
            "'MERCAUX_ADMIN'," +
            "'CONCIERGE_SALES_ADMIN'," +
            "'SALES'," +
            "'CONCIERGE_SOURCERS_ADMIN'," +
            "'SOURCER'," +
            "'STOLESHNIKOV_ADMIN'," +
            "'STOLESHNIKOV_BOUTIQUE_SALESMAN'," +
            "'KUZNETSKY_BRIDGE_ADMIN'," +
            "'KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN'" +
            ")")
    public Api2Response<MercauxPurchaseOrderFullDTO> getPurchaseOrderMercaux(long orderId) {
        return new Api2Response<MercauxPurchaseOrderFullDTO>().success(
                "Завяка успешно получена",
                mercauxPurchaseOrderService.getPurchaseOrderById(orderId)
        );
    }
}
