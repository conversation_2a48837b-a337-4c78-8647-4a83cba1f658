package su.reddot.presentation.api.v2.concierge;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.StringToClassMapItem;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Encoding;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import su.reddot.domain.service.concierge.model.ConciergeOrderDetailsResponse;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.concierge.model.PurchaseOrderFullDTO;
import su.reddot.domain.service.concierge.model.RejectionEventRequest;
import su.reddot.domain.service.dto.CurrencyRateDTO;
import su.reddot.domain.service.dto.CurrencyRateWithEffectiveRateDTO;
import su.reddot.domain.service.dto.concierge.ConciergeApplicationFormDto;
import su.reddot.domain.service.dto.concierge.OrderInformationDto;
import su.reddot.domain.service.dto.concierge.PurchaseOrderMobileCreateRequest;
import su.reddot.domain.service.dto.concierge.PurchaseOrderMobileUpdateRequest;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.service.productpublication.Conversion;
import su.reddot.presentation.api.v2.Api2Response;

import java.math.BigDecimal;
import java.util.List;

@RequestMapping("/api/v2/concierge")
@Tag(
        name = "Клиентский API консьерж-сервиса (Bitrix + Self)",
        description = "Управление заявками консьерж-сервиса"
)
public interface ConciergeControllerDelegate {

    @PostMapping("/image/upload")
    @Operation(
            summary = "Bitrix: Загрузка изображений",
            description = "Загрузка изображений для заявок консьерж-сервиса",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                content = @Content(
                    mediaType = "multipart/form-data",
                    schema = @Schema(
                            requiredProperties = {"image"},
                            type = "object",
                            properties = {
                                    @StringToClassMapItem(
                                            key = "image",
                                            value = MultipartFile.class
                                    )
                            }
                    ),
                    encoding = @Encoding(
                            name = "data",
                            contentType = "text/plain"
                    )
                )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Изображения успешно загружены",
                            content = @Content(schema = @Schema(implementation = ImageUploadResponse.class))
                    )
            }
    )
    Api2Response<List<String>> uploadItemImage(
            @Parameter(
                    name = "image",
                    description = "Изображения для загрузки",
                    required = true,
                    in = ParameterIn.QUERY
            )
            @RequestParam("image") List<MultipartFile> images);

    @PostMapping("/submit-application")
    @Operation(
            summary = "Bitrix: Отправка заявки",
            description = "Отправка заявки консьерж-сервиса",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Заявка успешно отправлена",
                            content = @Content(schema = @Schema(implementation = Api2Response.class))
                    )
            }
    )
    Api2Response<String> submitApplication(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные заявки",
                    required = true,
                    content = @Content(schema = @Schema(implementation = ConciergeApplicationFormDto.class))
            )
            @RequestBody ConciergeApplicationFormDto applicationForm);

    @PostMapping("/submit-seller-application")
    @Operation(
        summary = "Bitrix: Отправка заявки продавца",
        description = "Отправка заявки продавца",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Заявка успешно отправлена",
                content = @Content(schema = @Schema(implementation = Api2Response.class))
            )
        }
    )
    Api2Response<String> submitSellerApplication(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные заявки",
                    required = true,
                    content = @Content(schema = @Schema(implementation = ConciergeApplicationFormDto.class))
            )
            @RequestBody ConciergeApplicationFormDto applicationForm);

    @GetMapping("/order-information-callback")
    @Operation(
            summary = "Bitrix: Получение информации о заявке",
            description = "Получение информации о заявке",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Информация о заявке успешно получена",
                            content = @Content(schema = @Schema(implementation = OrderInformationDto.class))
                    )
            }
    )
    ResponseEntity<OrderInformationDto> getOrderInformationCallback(
            @Parameter(
                    name = "orderID",
                    description = "Идентификатор заявки",
                    required = true,
                    in = ParameterIn.QUERY
            )
            @RequestParam("orderID") Long orderId);

    @GetMapping("/purchase-order/{orderId}")
    @Operation(
            summary = "Self: Получение заявки на покупку",
            description = "Получение заявки на покупку в внутреннем консьерж-сервисе",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Заявка на покупку успешно получена",
                            content = @Content(schema = @Schema(implementation = PurchaseOrderFullResponse.class))
                    )
            }
    )
    Api2Response<ConciergeOrderDetailsResponse> getPurchaseOrderApp(
            @Parameter(
                    name = "orderId",
                    description = "Идентификатор заявки",
                    example = "123",
                    required = true,
                    in = ParameterIn.PATH
            )
            @PathVariable long orderId);

    @PostMapping("/purchase-order")
    @Operation(
            summary = "Self: Создание заявки на покупку",
            description = "Создание заявки на покупку в внутреннем консьерж-сервисе",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Заявка на покупку успешно создана",
                            content = @Content(schema = @Schema(implementation = PurchaseOrderFullResponse.class))
                    )
            }
    )
    Api2Response<PurchaseOrderFullDTO> createPurchaseOrder(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные заявки",
                    required = true,
                    content = @Content(schema = @Schema(implementation = PurchaseOrderMobileCreateRequest.class))
            )
            @RequestBody PurchaseOrderMobileCreateRequest request);

    @PostMapping("/purchase-order/{orderId}/transition")
    @Operation(
            summary = "Self: Смена статуса заявки",
            description = "Перевод заявки в новый статус по событию",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Статус изменен",
                            content = @Content(schema = @Schema(implementation = PurchaseOrderFullResponse.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Некорректное событие"
                    ),
                    @ApiResponse(
                            responseCode = "409",
                            description = "Конфликт статусов"
                    )
            }
    )
    Api2Response<PurchaseOrderFullDTO> purchaseOrderEventSend(
            @Parameter(
                    name = "orderId",
                    description = "Идентификатор заявки",
                    example = "789",
                    required = true,
                    in = ParameterIn.PATH
            )
            @PathVariable Long orderId,
            @Parameter(
                    name = "eventCode",
                    description = "Код события",
                    example = "APPROVE",
                    required = true,
                    in = ParameterIn.QUERY
            )
            @RequestParam("eventCode") String eventCode
    );

    @PatchMapping("/purchase-order/{orderId}")
    @Operation(
            summary = "Self: Обновление заявки на покупку",
            description = "Обновление заявки на покупку в внутреннем консьерж-сервисе",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Заявка на покупку успешно обновлена",
                            content = @Content(schema = @Schema(implementation = PurchaseOrderFullResponse.class))
                    )
            }
    )
    Api2Response<PurchaseOrderFullDTO> updatePurchaseOrder(
            @Parameter(
                    name = "orderId",
                    description = "Идентификатор заявки",
                    example = "123",
                    required = true,
                    in = ParameterIn.PATH
            )
            @PathVariable Long orderId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные заявки",
                    required = true,
                    content = @Content(schema = @Schema(implementation = PurchaseOrderMobileUpdateRequest.class))
            )
            @RequestBody PurchaseOrderMobileUpdateRequest request
    );

    @GetMapping("/get-all-currencies")
    @Operation(
            summary = "Получение всех валют",
            description = "Получение списка всех доступных валют",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Список валют успешно получен",
                            content = @Content(schema = @Schema(implementation = Api2Response.class))
                    )
            }
    )
    Api2Response<?> getAllCurrencies();

    @GetMapping("/get-all-rates")
    @Operation(
            summary = "Получение всех курсов валют",
            description = "Получение списка всех курсов валют",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Список курсов валют успешно получен",
                            content = @Content(schema = @Schema(implementation = CurrencyRatesResponse.class))
                    )
            }
    )
    Api2Response<List<CurrencyRateWithEffectiveRateDTO>> getAllRates();

    @GetMapping("/getPriceWithoutCommissionInBaseCurrency")
    @Operation(
            summary = "Получение цены без комиссии в базовой валюте",
            description = "Конвертация цены в базовую валюту без учета комиссии",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Цена успешно конвертирована",
                            content = @Content(schema = @Schema(implementation = Api2Response.class))
                    )
            }
    )
    Api2Response<BigDecimal> getPriceWithoutCommissionInBaseCurrency(
            @Parameter(
                    description = "ISO код валюты",
                    example = "1",
                    required = true,
                    in = ParameterIn.QUERY
            )
            @RequestParam String currencyCode,
            @Parameter(
                    description = "Цена в указанной валюте",
                    example = "100.50",
                    required = true,
                    in = ParameterIn.QUERY
            )
            @RequestParam BigDecimal priceInCurrency,
            @Parameter(
                    description = "Идентификатор шопера",
                    example = "1",
                    required = false,
                    in = ParameterIn.QUERY
            )
            @RequestParam(required = false) Long shopperId
    );

    /**
     * Возвращает преобразование цены товара на сайте в суму, которую получит продавец.
     * На входе может быль любое из двух значений (цена с коммиссией (цена на сайте) или цена без комиссии (которую получит продавец))
     */
    @GetMapping("/conversion")
    @Operation(
            summary = "Получение преобразования цены товара",
            description = "Возвращает преобразование цены товара на сайте в суму, которую получит продавец",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Преобразование цены успешно получено",
                            content = @Content(schema = @Schema(implementation = ConversionResponse.class))
                    )
            }
    )
    Api2Response<Conversion> getConversion(
            @Parameter(
                    description = "Цена без комиссии",
                    example = "100.00",
                    required = false,
                    in = ParameterIn.QUERY
            )
            @RequestParam(required = false) BigDecimal priceWithoutCommission,
            @Parameter(
                    description = "Цена с комиссией",
                    example = "110.00",
                    required = false,
                    in = ParameterIn.QUERY
            )
            @RequestParam(required = false) BigDecimal priceWithCommission,
            @Parameter(
                    description = "Канал продаж",
                    example = "WEBSITE",
                    required = false,
                    in = ParameterIn.QUERY
            )
            @RequestParam(required = false) SalesChannel salesChannel,
            @Parameter(
                    description = "Идентификатор продавца",
                    example = "1",
                    required = false,
                    in = ParameterIn.QUERY
            )
            @RequestParam(required = false) Long sellerId,
            @Parameter(
                    description = "Код валюты",
                    example = "USD",
                    required = false,
                    in = ParameterIn.QUERY
            )
            @RequestParam(required = false) String currencyCode
    );



    @PostMapping(value = "/rejection", produces = "application/json")
    @Operation(
            summary = "Создание события отклонения",
            description = "Добавляет событие отклонения к заявке.",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Событие отклонения успешно добавлено",
                            content = @Content(schema = @Schema(implementation = ConciergeOrderDetailsResponse.class))
                    ),
                    @ApiResponse(
                            responseCode = "400",
                            description = "Некорректный запрос"
                    ),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Заявка или причина отклонения не найдена"
                    )
            }
    )
    Api2Response<ConciergeOrderDetailsResponse> createRejectionEventApp(
            @Parameter(description = "Данные для создания события отклонения", required = true)
            @RequestBody RejectionEventRequest request,
            @Parameter(
                    name = "orderId",
                    description = "Идентификатор заявки",
                    example = "789",
                    required = true,
                    in = ParameterIn.QUERY
            )
            @PathVariable Long orderId
    );

    @Schema(description = "Полная информация о заявке")
    class PurchaseOrderFullResponse extends Api2Response<PurchaseOrderFullDTO> {
        @Schema(description = "Данные заявки на покупку")
        private PurchaseOrderFullDTO data;
    }

    @Schema(description = "Список загруженных изображений")
    class ImageUploadResponse extends Api2Response<List<String>> {
        @Schema(description = "Список загруженных изображений")
        private List<String> data;
    }

    @Schema(description = "Список курсов валют")
    class CurrencyRatesResponse extends Api2Response<List<CurrencyRateDTO>> {
        @Schema(description = "Список курсов валют")
        private List<CurrencyRateDTO> data;
    }

    @Schema(description = "Преобразование цены товара")
    class ConversionResponse extends Api2Response<Conversion> {
        @Schema(description = "Данные преобразования цены")
        private Conversion data;
    }
}
