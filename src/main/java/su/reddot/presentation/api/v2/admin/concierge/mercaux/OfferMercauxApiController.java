package su.reddot.presentation.api.v2.admin.concierge.mercaux;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.concierge.model.ProposedOfferDTO;
import su.reddot.domain.service.concierge.model.SendOffersToClientRequest;
import su.reddot.domain.service.concierge.model.ShipmentOffersDTO;
import su.reddot.domain.service.concierge.oskellyconcierge.OfferService;
import su.reddot.presentation.api.v2.Api2Response;

import java.util.List;

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class OfferMercauxApiController implements OfferMercauxApiDelegate {
    private final OfferService offerService;
    @Override
    @PreAuthorize("hasAnyAuthority('CONCIERGE_SOURCERS_ADMIN', 'SOURCER')")
    public Api2Response<ShipmentOffersDTO> addingProductsMercaux(ShipmentOffersDTO shipmentOffers) {
        return new Api2Response<ShipmentOffersDTO>()
                .success(offerService.addingProducts(shipmentOffers));
    }

    @Override
    @PreAuthorize("hasAnyAuthority('CONCIERGE_SOURCERS_ADMIN', 'SOURCER')")
    public Api2Response<ShipmentOffersDTO> addingShoppersMercaux(ShipmentOffersDTO shipmentOffers) {
        return new Api2Response<ShipmentOffersDTO>()
                .success(offerService.addingShoppers(shipmentOffers));
    }

    @Override
    @PreAuthorize("hasAnyAuthority('CONCIERGE_SOURCERS_ADMIN', 'SOURCER')")
    public Api2Response<Void> deleteProductMercaux(Long productId) {
        return new Api2Response<Void>()
                .success("Успешно удалено", offerService.deleteProduct(productId));
    }

    @Override
    @PreAuthorize("hasAnyAuthority('CONCIERGE_SOURCERS_ADMIN', 'SOURCER')")
    public Api2Response<ShipmentOffersDTO> addShopperOfferMercaux(ShipmentOffersDTO shipmentOffers) {
        return new Api2Response<ShipmentOffersDTO>()
                .success("Успешно добавлено", offerService.addShopperOffer(shipmentOffers));
    }

    @Override
    @PreAuthorize("hasAnyAuthority(" +
            "'CONCIERGE_SALES_ADMIN'," +
            "'SALES'," +
            "'STOLESHNIKOV_ADMIN'," +
            "'STOLESHNIKOV_BOUTIQUE_SALESMAN'," +
            "'KUZNETSKY_BRIDGE_ADMIN'," +
            "'KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN'" +
            ")")
    public Api2Response<Void> sendOffersMercaux(SendOffersToClientRequest request) {
        return new Api2Response<Void>()
                .success("Предложения отправлены", offerService.sendOffers(request));
    }

    @Override
    @PreAuthorize("hasAnyAuthority('CONCIERGE_SOURCERS_ADMIN', 'SOURCER')")
    public Api2Response<List<ProposedOfferDTO>> getProposedOffersByOfferMercaux(Long offerId) {
        return new Api2Response<List<ProposedOfferDTO>>()
            .success("Офферы получены успешно",
                offerService.getProposedOffersByOffer(offerId));
    }
}
