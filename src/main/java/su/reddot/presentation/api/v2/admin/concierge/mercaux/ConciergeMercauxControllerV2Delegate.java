package su.reddot.presentation.api.v2.admin.concierge.mercaux;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import su.reddot.domain.service.concierge.model.MercauxPurchaseOrderFullDTO;
import su.reddot.domain.service.concierge.model.PurchaseOrderFullDTO;
import su.reddot.domain.service.concierge.model.PurchaseOrderWithShipmentsCreateRequest;
import su.reddot.domain.service.concierge.model.ShipmentCommentDTO;
import su.reddot.domain.service.concierge.model.ShipmentRequestDTO;
import su.reddot.domain.service.concierge.model.ShipmentResponseDTO;
import org.springframework.web.bind.annotation.RequestParam;
import su.reddot.domain.service.concierge.model.OrdersForConciergeDTO;
import su.reddot.domain.service.concierge.model.PurchaseOrderFilter;
import su.reddot.domain.service.concierge.model.RequestsFilter;
import su.reddot.domain.service.dto.Page;
import su.reddot.presentation.api.v2.Api2Response;

import javax.validation.Valid;

import java.util.List;

@RequestMapping("/api/v2/admin/mercaux/concierge")
@Tag(
    name = "Администрирование консьерж-сервиса через приложение Mercaux (V2)",
    description = "Управление заявками на покупку в консьерж-сервисе через приложение Mercaux"
)
public interface ConciergeMercauxControllerV2Delegate {

    @PostMapping("/purchase-order")
    @Operation(
        summary = "Создание заявки на покупку",
        description = "Требуемые роли: CONCIERGE_SALES_ADMIN, SALES, STOLESHNIKOV_ADMIN, STOLESHNIKOV_BOUTIQUE_SALESMAN, KUZNETSKY_BRIDGE_ADMIN, KUZNETSKY_BRIDGE_BOUTIQUE_SALESMAN",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Заявка успешно создана",
                content = @Content(schema = @Schema(implementation = MercauxPurchaseOrderFullResponse.class))
            ),
            @ApiResponse(
                responseCode = "403",
                description = "Доступ запрещен"
            )
        }
    )
    @io.swagger.v3.oas.annotations.parameters.RequestBody(
        description = "Данные для создания заявки",
        required = true,
        content = @Content(schema = @Schema(implementation = PurchaseOrderWithShipmentsCreateRequest.class)))
    Api2Response<PurchaseOrderFullDTO> submitPurchaseOrderMercaux(
        @RequestBody PurchaseOrderWithShipmentsCreateRequest request
    );

    @Operation(
        operationId = "addShipmentsToOrderMercaux",
        summary = "Добавление нескольких товаров в заявку",
        description = "Добавляет несколько новых товаров в указанную заявку",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Товары успешно добавлены",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    array = @ArraySchema(schema = @Schema(implementation = ShipmentResponseDTO.class))
                )
            ),
            @ApiResponse(
                responseCode = "404",
                description = "Заявка не найдена"
            )
        }
    )
    @PostMapping(
        value = "/shipments/purchase-order/{orderId}",
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    Api2Response<List<ShipmentResponseDTO>> addShipmentsToOrderMercaux(
        @Parameter(name = "orderId", description = "ID заявки", required = true, in = ParameterIn.PATH)
        @PathVariable Long orderId,

        @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "Запрос с данными для создания товаров в заявке",
            required = true,
            content = @Content(
                mediaType = MediaType.APPLICATION_JSON_VALUE,
                array = @ArraySchema(schema = @Schema(implementation = ShipmentRequestDTO.class))
            )
        )
        @RequestBody List<ShipmentRequestDTO> shipments
    );

    @Operation(
            operationId = "addCommentToShipmentMercaux",
            summary = "Добавление комментария к товару",
            description = "Добавляет комментарий к товару в заявке",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Комментарий успешно добавлен",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ShipmentResponseDTO.class)
                            )
                    ),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Товар не найден"
                    )
            }
    )
    @PutMapping(
            value = "/shipments/comment",
            produces = {"application/json"},
            consumes = {"application/json"}
    )
    default Api2Response<ShipmentResponseDTO> addCommentToShipmentMercaux(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Комментарий к товару",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ShipmentCommentDTO.class)
                    ))
            @RequestBody ShipmentCommentDTO comment) {
        return new Api2Response<>();
    }

    @PostMapping(
        value = "/purchase-orders",
        consumes = MediaType.APPLICATION_JSON_VALUE,
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    @Operation(
        summary = "Получение списка заявок по фильтру для приложения Mercaux",
        description = "Возвращает список заявок, отфильтрованных по заданным параметрам.  " +
            "Для администраторов возвращаются все заявки, для обычных пользователей - только их собственные. " +
            "Поддерживает пагинацию, сортировку и полнотекстовый поиск.",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Успешный запрос. Возвращает список DTO заявок",
                content = @Content(
                    mediaType = MediaType.APPLICATION_JSON_VALUE,
                    array = @ArraySchema(schema = @Schema(implementation = OrdersForConciergeDTO.class))
                )
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Неверные параметры запроса (невалидный фильтр)"
            ),
            @ApiResponse(
                responseCode = "500",
                description = "Внутренняя ошибка сервера"
            )
        }
    )
    Api2Response<Page<OrdersForConciergeDTO>> getPurchaseOrdersMercaux(
        @RequestBody @Valid RequestsFilter filter,
        @Parameter(
            name = "searchText",
            description = "Текст для полнотекстового поиска по заявкам",
            example = "срочный заказ"
        )
        @RequestParam(name = "searchText", required = false) String searchText
    );

    @PostMapping(
        value = "/filter",
        produces = MediaType.APPLICATION_JSON_VALUE
    )
    @Operation(
        summary = "Получение данных для фильтрации заявок для приложения Mercaux",
        description = "Формирование фильтра для получения заявок",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "OK",
                content = {
                    @Content(
                        mediaType = MediaType.APPLICATION_JSON_VALUE,
                        schema = @Schema(implementation = PurchaseOrderFilter.class)
                    )
                }
            ),
            @ApiResponse(responseCode = "400", description = "Bad Request")
        }
    )
    Api2Response<PurchaseOrderFilter> getFilterPurchaseOrdersMercaux(
        @Parameter(
            name = "filter",
            description = "Фильтр для получения заявок",
            required = true,
            schema = @Schema(implementation = PurchaseOrderFilter.class)
        )
        @RequestBody @Valid PurchaseOrderFilter filter
    );

    @PostMapping("/purchase-order/{orderId}/transition")
    @Operation(
        summary = "Смена статуса заявки",
        description = "Перевод заявки в новый статус по событию",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Статус изменен",
                content = @Content(schema = @Schema(implementation = MercauxPurchaseOrderFullResponse.class))
            ),
            @ApiResponse(
                responseCode = "400",
                description = "Некорректное событие"
            ),
            @ApiResponse(
                responseCode = "409",
                description = "Конфликт статусов"
            )
        }
    )
    Api2Response<PurchaseOrderFullDTO> eventSendMercaux(
        @Parameter(
            name = "orderId",
            description = "Идентификатор заявки",
            example = "789",
            required = true,
            in = ParameterIn.PATH
        )
        @PathVariable Long orderId,

        @Parameter(
            name = "eventCode",
            description = "Код события",
            example = "APPROVE",
            required = true,
            in = ParameterIn.QUERY
        )
        @RequestParam("eventCode") String eventCode
    );

    @GetMapping("/purchase-order/{orderId}")
    @Operation(
            summary = "Получение заявки по ID",
            description = "Доступные роли: CONCIERGE_SALES_ADMIN, CONCIERGE_SOURCERS_ADMIN, SALES, SOURCER",
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Заявка найдена",
                            content = @Content(schema = @Schema(implementation = MercauxPurchaseOrderFullDTO.class))
                    ),
                    @ApiResponse(
                            responseCode = "404",
                            description = "Заявка не существует"
                    )
            }
    )
    Api2Response<MercauxPurchaseOrderFullDTO> getPurchaseOrderMercaux(
            @Parameter(
                    name = "orderId",
                    description = "Идентификатор заявки",
                    example = "123",
                    required = true,
                    in = ParameterIn.PATH
            )
            @PathVariable long orderId
    );
}

@Schema(description = "Полная информация о заявке")
class MercauxPurchaseOrderFullResponse extends Api2Response<PurchaseOrderFullDTO> {
    @Schema(description = "Данные заявки на покупку")
    private PurchaseOrderFullDTO data;
}
