package su.reddot.presentation.api.v2.concierge.shopper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.concierge.miniapps.ShopperService;
import su.reddot.domain.service.concierge.model.OfferDetailsDTO;
import su.reddot.domain.service.concierge.model.PaginatedOffersResult;
import su.reddot.domain.service.concierge.model.ShopperFilterItemsRequest;
import su.reddot.domain.service.filter.model.filter.ProductFilter;
import su.reddot.domain.service.filter.model.filter.ProductFilters;
import su.reddot.presentation.api.v2.Api2Response;
import su.reddot.presentation.api.v2.filter.ProductFilterInfoRequest;

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@PreAuthorize("hasAnyRole('PERSONAL_SHOPPER')")
public class ShopperController implements ShopperControllerDelegate {

    private final ShopperService shopperService;

    @Override
    public Api2Response<ProductFilter> getFilterInfo(ProductFilterInfoRequest request, String code) {
        Api2Response<ProductFilter> response = Api2Response.create();
        ProductFilter filterInfo = shopperService.getFilterInfo(request, code);
        return response.success("Информация о фильтре успешно получена", filterInfo);
    }

    @Override
    public Api2Response<PaginatedOffersResult> getFilterItems(ShopperFilterItemsRequest request) {
        Api2Response<PaginatedOffersResult> response = Api2Response.create();
        PaginatedOffersResult filterItems = shopperService.getFilterItems(request);
        return response.success("Элементы фильтра успешно получены", filterItems);
    }

    @Override
    public Api2Response<OfferDetailsDTO> getOfferDetails(Long offerId) {
        log.info("Получение деталей оффера {}", offerId);
        Api2Response<OfferDetailsDTO> response = Api2Response.create();
        OfferDetailsDTO offerDetails = shopperService.getOfferDetails(offerId);
        return response.success("Детали оффера успешно получены", offerDetails);
    }

    @Override
    public Api2Response<ProductFilters> getAvailableFilters(ShopperFilterItemsRequest request) {
        log.info("Получение доступных фильтров для шопера");
        Api2Response<ProductFilters> response = Api2Response.create();
        ProductFilters availableFilters = shopperService.getAvailableFilters(request);
        return response.success("Доступные фильтры успешно получены", availableFilters);
    }
}
