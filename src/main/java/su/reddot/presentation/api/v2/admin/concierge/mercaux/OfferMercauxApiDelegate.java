package su.reddot.presentation.api.v2.admin.concierge.mercaux;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import su.reddot.domain.service.concierge.model.OfferDTO;
import su.reddot.domain.service.concierge.model.ProposedOfferDTO;
import su.reddot.domain.service.concierge.model.SendOffersToClientRequest;
import su.reddot.domain.service.concierge.model.ShipmentOffersDTO;
import su.reddot.presentation.api.v2.Api2Response;

import javax.validation.Valid;
import java.util.List;

@Validated
@Tag(name = "mercaux-offer-controller", description = "API для взаимодействия с предложениями")
@RequestMapping("/api/v2/admin/mercaux/offer")
public interface OfferMercauxApiDelegate {
    @Operation(
            summary = "Добавление товара с платформы в предложение",
            description = "Добавление товара с платформы в предложение",
            responses = @ApiResponse(responseCode = "200", description = "Товары успешно добавлены в предложение.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class))
            })
    )
    @PostMapping(
            value = "/addProducts",
            produces = {"application/json"}
    )
    default Api2Response<ShipmentOffersDTO> addingProductsMercaux(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Список товаров с платформы",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class)))
            @RequestBody @Valid ShipmentOffersDTO shipmentOffers
    ) {
        return new Api2Response<>();
    }

    @Operation(
            summary = "Добавление шоперов в предложение",
            description = "Добавление шоперов в предложение",
            responses = @ApiResponse(responseCode = "200", description = "Шопер(ы) успешно добавлен в предложение.", content = {
                    @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class))
            })
    )
    @PostMapping(
            value = "/addingShoppers",
            produces = {"application/json"}
    )
    default Api2Response<ShipmentOffersDTO> addingShoppersMercaux(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные шоперов",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class)))
            @RequestBody @Valid ShipmentOffersDTO shipmentOffers
    ) {
        return new Api2Response<>();
    }

    @Operation(
            summary = "Удаление товара из предложения",
            description = "Удаление товара из предложения",
            responses = @ApiResponse(responseCode = "200", description = "Товар успешно удален из предложения.")
    )
    @DeleteMapping(
            value = "/{productId}",
            produces = {"application/json"}
    )
    default Api2Response<Void> deleteProductMercaux(
            @Parameter(name = "productId", description = "ID товара", required = true, in = ParameterIn.PATH)
            @PathVariable Long productId
    ) {
        return new Api2Response<>();
    }

    @Operation(
            summary = "Добавить предложение шопера",
            description = "Добавить предложение шопера",
            responses = @ApiResponse(responseCode = "200", description = "Предложение успешно добавлено",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class)))
    )
    @PostMapping(
            value = "/shopperOffer",
            produces = {"application/json"}
    )
    default Api2Response<ShipmentOffersDTO> addShopperOfferMercaux(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные по предложению шоперов",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ShipmentOffersDTO.class)))
            @RequestBody @Valid ShipmentOffersDTO shipmentOffers
    ){
        return new Api2Response<>();
    }


    @Operation(
            summary = "Отправка предложений клиенту в WhatsApp",
            description = "Отправить несколько предложений клиенту в WhatsApp",
            responses = @ApiResponse(
                    responseCode = "200",
                    description = "Предложения успешно отравлены",
                    content = @Content(mediaType = MediaType.APPLICATION_JSON_VALUE)
            )
    )
    @PostMapping(
            value = "/send",
            produces = MediaType.APPLICATION_JSON_VALUE
    )
    default Api2Response<Void> sendOffersMercaux(
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные по предложениям для отправки сообщения",
                    required = true,
                    content = @Content(
                            mediaType = MediaType.APPLICATION_JSON_VALUE,
                            schema = @Schema(implementation = SendOffersToClientRequest.class)
                    )
            )
            @RequestBody @Valid SendOffersToClientRequest request
    ){
        return new Api2Response<>();
    }

    @Operation(
        summary = "Получение ProposedOffer по идентификатору Offer",
        description = "Возвращает список ProposedOffer со вложенными сущностями по идентификатору Offer.",
        responses = {
            @ApiResponse(
                responseCode = "200",
                description = "Успешный запрос",
                content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(type = "array", implementation = ProposedOfferDTO.class)
                )),
            @ApiResponse(
                responseCode = "400",
                description = "Неверные параметры запроса"
            )
        }
    )
    @GetMapping(
        value = "/proposedOffers",
        produces = {"application/json"}
    )
    default Api2Response<List<ProposedOfferDTO>> getProposedOffersByOfferMercaux(
        @Parameter(
            name = "offerId",
            description = "ID оффера для поиска предложений",
            required = true,
            example = "12345")
        @RequestParam(name = "offerId") Long offerId){

        return new Api2Response<>();
    }
}
