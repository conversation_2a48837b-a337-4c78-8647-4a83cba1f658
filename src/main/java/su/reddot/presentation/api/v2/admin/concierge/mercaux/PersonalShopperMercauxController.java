package su.reddot.presentation.api.v2.admin.concierge.mercaux;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import su.reddot.domain.service.concierge.model.PaginatedShoppersResult;
import su.reddot.domain.service.concierge.model.PersonalShopper;
import su.reddot.domain.service.concierge.model.PersonalShopperCreateRequest;
import su.reddot.domain.service.concierge.model.PersonalShopperFilter;
import su.reddot.domain.service.concierge.oskellyconcierge.PersonalShopperService;
import su.reddot.presentation.api.v2.Api2Response;

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
public class PersonalShopperMercauxController implements PersonalShopperMercauxApiDelegate {
    private final PersonalShopperService personalShopperService;
    @Override
    @PreAuthorize("hasAnyAuthority('CONCIERGE_SOURCERS_ADMIN', 'SOURCER')")
    public Api2Response<PersonalShopper> createPersonalShopper(PersonalShopperCreateRequest request) {
        return new Api2Response<PersonalShopper>()
                .success("Шопер создан успешно",
                        personalShopperService.createPersonalShopper(request));
    }

    @Override
    @PreAuthorize("hasAnyAuthority('CONCIERGE_SOURCERS_ADMIN', 'SOURCER')")
    public Api2Response<PersonalShopperFilter> filterPersonalShopperMercaux() {
        return new Api2Response<PersonalShopperFilter>()
                .success("Фильтр получен успешно",
                        personalShopperService.filterPersonalShopper());
    }

    @Override
    @PreAuthorize("hasAnyAuthority('CONCIERGE_SOURCERS_ADMIN', 'SOURCER')")
    public Api2Response<PaginatedShoppersResult> getPersonalShoppersMercaux(PersonalShopperFilter filter, String searchText) {
        return new Api2Response<PaginatedShoppersResult>()
                .success("Шоперы получены успешно",
                        personalShopperService.getPersonalShoppers(filter, searchText));
    }
}
