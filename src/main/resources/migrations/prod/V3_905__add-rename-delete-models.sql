UPDATE product_model SET name = 'Le City', url = 'le-city' WHERE name = 'City';

INSERT INTO product_model (name, url) VALUES ('Allegra', 'allegra') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('<PERSON> <PERSON>', 'c-de-cartier') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Destiny', 'destiny') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Etincelle', 'etincelle') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('<PERSON>', 'le-baiser') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Chrono', 'chrono') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Flot', 'flot') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Poitiers', 'poitiers') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Plumet', 'plumet') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Island', 'island') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Job', 'job') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Kid', 'kid') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Deep', 'deep') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Antigua', 'antigua') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Mini Pop H', 'mini-pop-h') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Kiddy', 'kiddy') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Cadenas', 'cadenas') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Get', 'get') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Destiny', 'destiny') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Heure H', 'heure-h') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Karl', 'karl') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Jackson', 'jackson') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Klement', 'klement') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Quicker', 'quicker') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Boomerang', 'boomerang') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Ignacio', 'ignacio') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Icone ', 'icone') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Clou de forge', 'clou-de-forge') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Beverly', 'beverly') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Claudia', 'claudia') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Chrissie', 'chrissie') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Waterfront', 'waterfront') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Sunset', 'sunset') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Alpha', 'alpha') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Trunk', 'trunk') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Rivoli', 'rivoli') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Takeoff', 'takeoff') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Bumbag', 'bumbag') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Nolita', 'nolita') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Jerry', 'jerry') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Sade', 'sade') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('90''s Bag', '90s-bag') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Metro', 'metro') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Harmony', 'harmony') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Setting', 'setting') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Socrate', 'socrate') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Sweet Hearts', 'sweet-hearts') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Tendrement ', 'tendrement') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Super Star', 'super-star') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Ball Star', 'ball-star') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('True Star', 'true-star') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Marathon', 'marathon') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Mid Star', 'mid-star') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('V-Star', 'v-star') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Ice Cube', 'ice-cube') ON CONFLICT DO NOTHING;
INSERT INTO product_model (name, url) VALUES ('Happy Diamonds', 'happy-diamonds') ON CONFLICT DO NOTHING;

INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('C de Cartier')), (SELECT id FROM brand WHERE lower(name) = lower('CARTIER VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Destiny')), (SELECT id FROM brand WHERE lower(name) = lower('CARTIER VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Etincelle')), (SELECT id FROM brand WHERE lower(name) = lower('CARTIER VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Le Baiser')), (SELECT id FROM brand WHERE lower(name) = lower('CARTIER VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Chrono')), (SELECT id FROM brand WHERE lower(name) = lower('CHRISTIAN DIOR VINTAGE'))) ON CONFLICT DO NOTHING;

INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Island')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Job')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Kid')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Deep')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Antigua')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Mini Pop H')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Kiddy')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Cadenas')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Get')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Destiny')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Heure H')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Karl')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Jackson')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Klement')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Quicker')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Boomerang')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Ignacio')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Icone ')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Clou de forge')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Beverly')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Claudia')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Chrissie')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Waterfront')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Sunset')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Alpha')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Trunk')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Rivoli')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Takeoff')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Bumbag')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON VINTAGE'))) ON CONFLICT DO NOTHING;

INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Chrono')), (SELECT id FROM brand WHERE lower(name) = lower('DIOR HOMME'))) ON CONFLICT DO NOTHING;

INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Island')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Job')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Kid')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Deep')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Antigua')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Mini Pop H')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Kiddy')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Cadenas')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Get')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Destiny')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Heure H')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Karl')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Jackson')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Klement')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Quicker')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Boomerang')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Ignacio')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Icone ')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Clou de forge')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES VINTAGE'))) ON CONFLICT DO NOTHING;

INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Allegra')), (SELECT id FROM brand WHERE lower(name) = lower('BULGARI'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('C de Cartier')), (SELECT id FROM brand WHERE lower(name) = lower('Cartier'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Destiny')), (SELECT id FROM brand WHERE lower(name) = lower('Cartier'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Etincelle')), (SELECT id FROM brand WHERE lower(name) = lower('Cartier'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Le Baiser')), (SELECT id FROM brand WHERE lower(name) = lower('Cartier'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Chrono')), (SELECT id FROM brand WHERE lower(name) = lower('CHRISTIAN DIOR PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Flot')), (SELECT id FROM brand WHERE lower(name) = lower('GOYARD'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Poitiers')), (SELECT id FROM brand WHERE lower(name) = lower('GOYARD'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Plumet')), (SELECT id FROM brand WHERE lower(name) = lower('GOYARD'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Island')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Job')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Kid')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Deep')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Antigua')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Mini Pop H')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Kiddy')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Cadenas')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Get')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Destiny')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Heure H')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Karl')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Jackson')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Klement')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Quicker')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Boomerang')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Ignacio')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Icone ')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Clou de forge')), (SELECT id FROM brand WHERE lower(name) = lower('HERMES'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Beverly')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Claudia')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Chrissie')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Waterfront')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Sunset')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Alpha')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Trunk')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Rivoli')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Takeoff')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Bumbag')), (SELECT id FROM brand WHERE lower(name) = lower('LOUIS VUITTON PRE-OWNED'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Nolita')), (SELECT id FROM brand WHERE lower(name) = lower('Saint Laurent'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Jerry')), (SELECT id FROM brand WHERE lower(name) = lower('Saint Laurent'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Sade')), (SELECT id FROM brand WHERE lower(name) = lower('Saint Laurent'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('90''s Bag')), (SELECT id FROM brand WHERE lower(name) = lower('The Row'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Metro')), (SELECT id FROM brand WHERE lower(name) = lower('TIFFANY&CO'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Harmony')), (SELECT id FROM brand WHERE lower(name) = lower('TIFFANY&CO'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Setting')), (SELECT id FROM brand WHERE lower(name) = lower('TIFFANY&CO'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Socrate')), (SELECT id FROM brand WHERE lower(name) = lower('VAN CLEEF & ARPELS'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Sweet Hearts')), (SELECT id FROM brand WHERE lower(name) = lower('VAN CLEEF & ARPELS'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Tendrement ')), (SELECT id FROM brand WHERE lower(name) = lower('VAN CLEEF & ARPELS'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Super Star')), (SELECT id FROM brand WHERE lower(name) = lower('GOLDEN GOOSE DELUXE BRAND'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Ball Star')), (SELECT id FROM brand WHERE lower(name) = lower('GOLDEN GOOSE DELUXE BRAND'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('True Star')), (SELECT id FROM brand WHERE lower(name) = lower('GOLDEN GOOSE DELUXE BRAND'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Marathon')), (SELECT id FROM brand WHERE lower(name) = lower('GOLDEN GOOSE DELUXE BRAND'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Mid Star')), (SELECT id FROM brand WHERE lower(name) = lower('GOLDEN GOOSE DELUXE BRAND'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('V-Star')), (SELECT id FROM brand WHERE lower(name) = lower('GOLDEN GOOSE DELUXE BRAND'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Ice Cube')), (SELECT id FROM brand WHERE lower(name) = lower('CHOPARD'))) ON CONFLICT DO NOTHING;
INSERT INTO product_model_brand_binding (model_id, brand_id) VALUES ((SELECT id FROM product_model WHERE lower(name) = lower('Happy Diamonds')), (SELECT id FROM brand WHERE lower(name) = lower('CHOPARD'))) ON CONFLICT DO NOTHING;

--

update product
set product_model_id = NULL
where product_model_id
          in (select id from product_model where lower(name) in (lower('BV'), lower('Tom')));

--

update product
set product_model_id = (select id from product_model where lower(name) = lower('Pochette Metis'))
where product_model_id
          in (select id from product_model where lower(name) in (lower('Metis')));

--

delete from product_model_brand_binding where model_id in (select id from product_model where lower(name) in (lower('BV'), lower('Metis'), lower('Tom')));
delete from product_model where lower(name) in (lower('BV'), lower('Metis'), lower('Tom'));
