SELECT
    --- Номер ЗАКАЗА в системе
	o.id AS o_id,
	--- Сосотяние, типа "оплата производится", "проблемы с картой", и т. д.
	o.state AS o_state,
	o.state_time AS o_state_time,
	o.hold_time AS o_hold_time,
	o.confirmed_time AS o_confirmed_time,
	o.buyer_id AS o_buyer_id,
	o.buyer_counterparty_id AS o_buyer_counterparty_id,
	o.seller_counterparty_id AS o_seller_counterparty_id,
	--- Сумма по заказу, с учетом скидок - столько списывается по чеку
	ROUND(o.effective_amount, 2) AS o_effective_amount,
	--- Стоимость заказа, которая состоит из стоимости всех позиций и стоимости доставки каждой позиции.
	--- При оплате заказа именно на эту сумму резервируются средства у покупателя.
	ROUND(o.amount, 2) AS o_hold_amount,
    COALESCE(o.delivery_cost, 0) AS o_delivery_cost,
    --- Дата продажи (не используется с 22 года)
    '..20 ::' as o_buyer_check_time,
    ag.sent_time AS ag_sent_time,
    ag.confirmed_time AS ag_confirmed_time,
    --- Дата поступления
    wb.delivery_time as oaud_delivery_time,
    oaud_completed_time,
    oaud_money_transferred_time,
    promo_code.id AS promo_code_id,
    promo_code.dtype AS promo_code_dtype,
    promo_code.value AS promo_code_value,
    promo_code.absolute_value AS promo_code_absolute_value,
    -- ID подарочного сертификата
    null AS gift_card_id,
    --- Код подарочного сертификата
    null AS gift_card_code,
    --- Значение подарочного сертификата
    null AS gift_card_amount,
    op.id AS op_id,
    op.state AS op_state,
    op.state_time AS op_state_time,
    op.confirmed_time AS op_confirmed_time,
    op.product_item_id AS op_product_item_id,
    ROUND(coalesce(op.seller_payout_amount_net, 0), 2) AS op_amount_minus_commission,
    ROUND(coalesce(marketplace_commission_amount_net, 0), 2) + coalesce(op.promocode_amount, 0) AS op_commission,
    op.amount AS op_amount,
    coalesce(op.promocode_amount, 0) AS op_promocode_amount,
    coalesce(e.defect_discount_price, 0) + (coalesce(e.defect_discount, 0) * op.amount) AS op_defects_discount_amount,
    --- Стоимость доставки
    op.delivery_cost AS op_delivery_cost,
    --- Тут дубль 52 строки op_confirmed_time, но джоин уберем, в 1С уточним \ посмотрим, что все ок
    op.confirmed_time AS opaud_state_time,
    e.cleaning_price AS exp_cleaning_price,
    pr.seller_id AS pr_seller_id,
    --- Дата и время публиации (имеется в виду, что модератор пропустил вещь)
    pr.publish_time AS pr_publish_time,
    sc.dtype AS sc_dtype
FROM "order" o
JOIN agent_report ag ON ag.order_id = o.id
LEFT JOIN waybill wb ON wb.order_id = o.id and wb.delivery_destination_type = 'OFFICE'
LEFT JOIN (SELECT id, MIN(state_time) AS oaud_completed_time FROM order_aud WHERE state = 'COMPLETED' GROUP BY id) AS oaud_completed ON oaud_completed.id = o.id
LEFT JOIN (SELECT id, MIN(state_time) AS oaud_money_transferred_time FROM order_aud WHERE state = 'MONEY_TRANSFERRED' GROUP BY id) AS oaud_money_transferred ON oaud_money_transferred.id = o.id
LEFT JOIN promo_code ON promo_code.id = o.promo_code_id
--- ВАЖНО: не учтено, что общая стоимость заказа может быть меньше стоимости всех вещей в заказе
--- за счет: Скидки (промокода), предоплаченного сертификата, баллов (которые можно потратить на покупку).
LEFT JOIN order_position op ON op.order_id = o.id AND op.is_effective = TRUE
LEFT JOIN product_item pri ON op.product_item_id = pri.id
LEFT JOIN product pr ON pr.id = pri.product_id
LEFT JOIN counterparty sc on sc.id = o.seller_counterparty_id
JOIN (
  SELECT
    MAX(id) AS e_id,
    order_position_id AS op_id
  FROM expertise
  GROUP BY order_position_id
) AS q_exp ON q_exp.op_id = op.id
JOIN expertise e ON e.id = q_exp.e_id
WHERE o.delivery_state = 'DELIVERED_TO_BUYER'
    AND COALESCE(o.order_source, '') <> 'BOUTIQUE'
    AND ag.sent_time >= '%startTime%' AND ag.sent_time <= '%endTime%'
-- Убираем заказы кроссбордера
    AND o.id IN (SELECT o.id
                 FROM "order" o
                 WHERE NOT EXISTS (SELECT 1 FROM order_extra_prop_value oepv WHERE o.id = oepv.order_id and oepv.order_extra_prop_id = 30))
ORDER BY o.id, op.id
/*LIMIT*/