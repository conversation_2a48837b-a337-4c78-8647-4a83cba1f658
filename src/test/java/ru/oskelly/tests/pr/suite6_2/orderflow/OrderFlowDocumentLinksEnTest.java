package ru.oskelly.tests.pr.suite6_2.orderflow;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.test.context.TestPropertySource;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.domain.service.dto.order.DocumentLinkDTO;
import su.reddot.domain.service.dto.order.DocumentType;
import su.reddot.presentation.api.v2.Api2Response;

import javax.annotation.PostConstruct;
import java.util.List;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
@TestPropertySource(properties = {"internationalVersion=true"})
public class OrderFlowDocumentLinksEnTest extends OrderFlowDocumentLinksRuTest {

    @PostConstruct
    private void init() {
        requestMoreCtx();
    }

        @Override
    protected void validateAdminsDocsOnHold(Api2Response<List<DocumentLinkDTO>> adminsOrderDocuments) {
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.CERTIFICATE)).hasSize(5);
        Assertions.assertThat(adminsOrderDocuments.getData()).hasSize(5);
    }

    @Override
    protected void validateClientDocsOnHold(Api2Response<List<DocumentLinkDTO>> clientOrderDocuments) {
        Assertions.assertThat(clientOrderDocuments.getData()).isNull();
    }

    @Override
    protected void validateAdminsDocsOnCompleted(Api2Response<List<DocumentLinkDTO>> adminsOrderDocuments) {
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.CERTIFICATE)).hasSize(5);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_AGENT_REPORT_EN)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_ORDER_LABELS)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_INVOICE_TAX_DELIVERY)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_INVOICE_TAX_GOODS)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_ORDER_ARAMEX_O2B_INVOICE)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_ORDER_ARAMEX_S2O_INVOICE)).hasSize(1);
        Assertions.assertThat(adminsOrderDocuments.getData()).hasSize(11);
    }

    @Override
    protected void validateClientDocsOnCompleted(Api2Response<List<DocumentLinkDTO>> clientOrderDocuments) {
        Assertions.assertThat(clientOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_INVOICE_TAX_DELIVERY)).hasSize(1);
        Assertions.assertThat(clientOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_INVOICE_TAX_GOODS)).hasSize(1);
        Assertions.assertThat(clientOrderDocuments.getData()).hasSize(2);
    }

    @Override
    protected void validateSellerDocsOnCompleted(Api2Response<List<DocumentLinkDTO>> clientOrderDocuments) {
        Assertions.assertThat(clientOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_AGENT_REPORT_EN)).hasSize(1);
        Assertions.assertThat(clientOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_INVOICE_TAX_GOODS)).hasSize(1);
        Assertions.assertThat(clientOrderDocuments.getData().stream().filter(it -> it.getType() == DocumentType.DOC_ORDER_ARAMEX_S2O_INVOICE)).hasSize(1);
        Assertions.assertThat(clientOrderDocuments.getData()).hasSize(3);
    }

    protected void callLoadSaleReport(Long ordersId) {
        // No default sale report: only with documents list
    }

}
