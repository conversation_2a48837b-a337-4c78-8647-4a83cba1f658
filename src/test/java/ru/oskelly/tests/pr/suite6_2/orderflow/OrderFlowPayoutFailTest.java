package ru.oskelly.tests.pr.suite6_2.orderflow;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.model.agentreport.AgentReportState;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.BankPaymentDTO;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.jobs.ScheduledBankRunner;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.infrastructure.util.Utils;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;


@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class OrderFlowPayoutFailTest extends OrderFlowTest {

    @Autowired
    private UserService userService;
    @Autowired
    private ScheduledBankRunner scheduledBankRunner;


    @Autowired
    private CallInTransaction callInTransaction;
    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;

    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private Long prepareAdminUser() {
        User adminUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PREPAYMENTS, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        orderFlowTestUtils.enableUserAuthority(adminUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        return adminUser.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        //
        callInTransaction.runInNewTransaction(this::prepareAdminUser);
        //
        User buyer = userService.getUserByEmail(buyerEmail);
        //
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        //
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.changeToSimpleUserNoCB(usualSellerId));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
    }

    @Test
    @Transactional
    public void _01_01_usualFlow_payoutCreateFail() {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        orderFlowTestUtils.getOrdersPayoutUpdateDoneList(this, true);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.PAYOUT_TRANSFER_MONEY_TO_SELLER)

                .noAgentReportCompare(true)
                .sellerPayoutAmount(7_500_00L)

                .build());
        orderFlowTestTcbMock.addFailOnAmountCreate(BigDecimal.valueOf(7_500_00, 2));
        //
        List<BankPaymentDTO> bankPaymentTransferDtos = scheduledBankRunner.transferMoneyToSellers();
        Utils.sleepMillis(1000);
        rollbackAndStartNewTransaction();
        Assertions.assertThat(orderFlowTestUtils.getOrdersPayoutUpdateDoneList(this, true)).contains(testOrder.getId());
        BankPaymentDTO payoutPaymentDTO = bankPaymentTransferDtos.stream()
                .filter(it -> Objects.equals(it.getOrderId(), testOrder.getId()))
                .findFirst()
                .orElse(null);
        Assertions.assertThat(payoutPaymentDTO).isNotNull();
        Assertions.assertThat(payoutPaymentDTO.getOrderState()).isEqualTo(OrderState.MONEY_PAYMENT_TECHNICAL_ERROR);
        Assertions.assertThat(orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_PAYMENT_TECHNICAL_ERROR)).satisfies(it ->
                Assertions.assertThat(it.getAgentReport().getState()).isEqualTo(AgentReportState.PAYMENT_PREPARED)
        );
        //
        List<BankPaymentDTO> bankPaymentTransferStep02Dtos = scheduledBankRunner.transferMoneyToSellers();
        Utils.sleepMillis(1000);
        rollbackAndStartNewTransaction();
        Assertions.assertThat(orderFlowTestUtils.getOrdersPayoutUpdateDoneList(this, true)).contains(testOrder.getId());
        BankPaymentDTO payoutPaymentStep02DTO = bankPaymentTransferStep02Dtos.stream()
                .filter(it -> Objects.equals(it.getOrderId(), testOrder.getId()))
                .findFirst()
                .orElse(null);
        Assertions.assertThat(payoutPaymentStep02DTO).isNotNull();
        Assertions.assertThat(payoutPaymentStep02DTO.getOrderState()).isEqualTo(OrderState.MONEY_PAYMENT_WAIT);
        Assertions.assertThat(orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_PAYMENT_WAIT)).satisfies(it ->
                Assertions.assertThat(it.getAgentReport().getState()).isEqualTo(AgentReportState.PAYMENT_INPROGRESS)
        );
    }

    @Test
    @Transactional
    public void _01_02_usualFlow_payoutTracksFail() {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        orderFlowTestUtils.getOrdersPayoutUpdateDoneList(this, true);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(2)

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.PAYOUT_VALIDATE_MONEY_TO_SELLER)

                .noAgentReportCompare(true)
                .sellerPayoutAmount(22_500_00L)

                .build());
        orderFlowTestTcbMock.addFailOnAmountStatus(BigDecimal.valueOf(22_500_00, 2));
        //
        List<BankPaymentDTO> bankPaymentVerifyDtos = scheduledBankRunner.checkTransferMoneyToSeller();
        Utils.sleepMillis(1000);
        rollbackAndStartNewTransaction();
        Assertions.assertThat(orderFlowTestUtils.getOrdersPayoutUpdateDoneList(this, true)).doesNotContain(testOrder.getId());
        //
        orderFlowTestUtils.transferAgentPaymentsMoneyToSellers();
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_PAYMENT_WAIT);
    }

    @Test
    @Transactional
    public void _02_01_conciergeFlow_payoutCreateFail() {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        orderFlowTestUtils.getOrdersPayoutUpdateDoneList(this, true);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .conciergeOrder(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.PAYOUT_TRANSFER_MONEY_TO_SELLER)

                .noAgentReportCompare(true)
                .sellerPayoutAmount(7_500_00L)

                .build());
        orderFlowTestTcbMock.addFailOnAmountCreate(BigDecimal.valueOf(7_500_00, 2));
        //
        List<BankPaymentDTO> bankPaymentTransferDtos = scheduledBankRunner.transferMoneyToSellers();
        Utils.sleepMillis(1000);
        rollbackAndStartNewTransaction();
        Assertions.assertThat(orderFlowTestUtils.getOrdersPayoutUpdateDoneList(this, true)).contains(testOrder.getId());
        BankPaymentDTO payoutPaymentDTO = bankPaymentTransferDtos.stream()
                .filter(it -> Objects.equals(it.getOrderId(), testOrder.getId()))
                .findFirst()
                .orElse(null);
        Assertions.assertThat(payoutPaymentDTO).isNotNull();
        Assertions.assertThat(payoutPaymentDTO.getOrderState()).isEqualTo(OrderState.MONEY_PAYMENT_TECHNICAL_ERROR);
        Assertions.assertThat(orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD)).satisfies(it ->
                Assertions.assertThat(it.getAgentReport().getState()).isEqualTo(AgentReportState.PAYMENT_PREPARED)
        );
        //
        List<BankPaymentDTO> bankPaymentTransferStep02Dtos = scheduledBankRunner.transferMoneyToSellers();
        Utils.sleepMillis(1000);
        rollbackAndStartNewTransaction();
        Assertions.assertThat(orderFlowTestUtils.getOrdersPayoutUpdateDoneList(this, true)).contains(testOrder.getId());
        BankPaymentDTO payoutPaymentStep02DTO = bankPaymentTransferStep02Dtos.stream()
                .filter(it -> Objects.equals(it.getOrderId(), testOrder.getId()))
                .findFirst()
                .orElse(null);
        Assertions.assertThat(payoutPaymentStep02DTO).isNotNull();
        Assertions.assertThat(payoutPaymentStep02DTO.getOrderState()).isEqualTo(OrderState.MONEY_PAYMENT_WAIT);
        Assertions.assertThat(orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD)).satisfies(it ->
                Assertions.assertThat(it.getAgentReport().getState()).isEqualTo(AgentReportState.PAYMENT_INPROGRESS)
        );
    }

    @Test
    @Transactional
    public void _02_02_conciergeFlow_payoutTracksFail() {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        orderFlowTestUtils.getOrdersPayoutUpdateDoneList(this, true);
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .conciergeOrder(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(2)

                .confirmPositions(ImmutableList.of(1, 2))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.PAYOUT_TRANSFER_MONEY_TO_SELLER)

                .noAgentReportCompare(true)
                .sellerPayoutAmount(22_500_00L)

                .build());
        orderFlowTestTcbMock.addFailOnAmountStatus(BigDecimal.valueOf(22_500_00, 2));
        //
        List<BankPaymentDTO> bankPaymentTransferDtos = scheduledBankRunner.transferMoneyToSellers();
        Utils.sleepMillis(1000);
        rollbackAndStartNewTransaction();
        Assertions.assertThat(orderFlowTestUtils.getOrdersPayoutUpdateDoneList(this, true)).contains(testOrder.getId());
        //
        BankPaymentDTO payoutPaymentCreateDTO = bankPaymentTransferDtos.stream()
                .filter(it -> Objects.equals(it.getOrderId(), testOrder.getId()))
                .findFirst()
                .orElse(null);
        Assertions.assertThat(payoutPaymentCreateDTO).isNotNull();
        Assertions.assertThat(payoutPaymentCreateDTO.getOrderState()).isEqualTo(OrderState.MONEY_PAYMENT_WAIT);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
        //
        List<BankPaymentDTO> bankPaymentVerifyDtos = scheduledBankRunner.checkTransferMoneyToSeller();
        Utils.sleepMillis(1000);
        rollbackAndStartNewTransaction();
        Assertions.assertThat(orderFlowTestUtils.getOrdersPayoutUpdateDoneList(this, true)).doesNotContain(testOrder.getId());
        //
        BankPaymentDTO payoutPaymentTracksDTO = bankPaymentTransferDtos.stream()
                .filter(it -> Objects.equals(it.getOrderId(), testOrder.getId()))
                .findFirst()
                .orElse(null);
        Assertions.assertThat(payoutPaymentTracksDTO).isNotNull();
        Assertions.assertThat(payoutPaymentTracksDTO.getOrderState()).isEqualTo(OrderState.MONEY_PAYMENT_WAIT);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
    }

}
