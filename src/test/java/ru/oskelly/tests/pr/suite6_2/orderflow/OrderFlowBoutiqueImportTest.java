package ru.oskelly.tests.pr.suite6_2.orderflow;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Disabled;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2ParseException;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.CartControllerV2Test;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestTcbMock;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.AccountTestSupport;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.service.dto.AccountDTO;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.SizeValueDTO;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.dto.order.adminpanel.AdminPanelOrderDetailsDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.infrastructure.bank.BoutiqueBankService;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.presentation.api.v2.Api2Response;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collection;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@Disabled("Use this test to import bunch of products / orders in boutique (details in DEVALAN-528)")
public class OrderFlowBoutiqueImportTest extends OrderFlowTest {

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    private AccountTestSupport accountTestSupport;

    private final String boutiqueMail = "<EMAIL>";                      // MARS: <EMAIL>    PROD: <EMAIL>
    private final String boutiquePass = "password4boutique";                     // MARS: btqe1988           PROD: boutiquePass!
    private Long sellerOfficePickupId = 14224L; // Need to append on fresh dump! // MARS: 17831L             PROD: 59987L
    private Long boutiqueDeliveryToId = null;   // Точка доставки для покупателя, если одна, то задавать не нужно: выберется она

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private ApiV2Client apiV2Client;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private ExecutorService executor = Executors.newFixedThreadPool(10);

    private long getUserId(ApiV2Client apiV2Client) {
        AccountDTO userInfo = accountTestSupport.getAccountSuccessful(apiV2Client, true);
        return userInfo.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.init(boutiqueMail, boutiquePass);
        apiV2Client = new ApiV2Client(boutiqueMail, boutiquePass);
        cartTestSupport.setUserId(getUserId(apiV2Client));
        cartTestSupport.setApiV2Client(apiV2Client);
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
    }

    @AfterEach
    public void cleanup() throws InterruptedException {
        executor.shutdown();
        executor.awaitTermination(4, TimeUnit.HOURS);
    }

    @AfterAll
    public static void done() {
        if (Objects.nonNull(orderFlowTestTcbMock)) orderFlowTestTcbMock.stop();
    }

    private long chooseDeliveryId() {
        if (Objects.nonNull(boutiqueDeliveryToId)) {
            return boutiqueDeliveryToId;
        }
        AccountDTO accountInfo = accountTestSupport.getAccountSuccessful(apiV2Client, true);
        if (accountInfo.getAddressEndpoints().size() == 1) {
            return accountInfo.getAddressEndpoints().get(0).getId();
        }
        throw new IllegalStateException(String.format("Unable to create order with user '%s': unable to find address endpoint (%d items present, must be exactly 1 item)",
                boutiqueMail, accountInfo.getAddressEndpoints().size()));
    }

    private OrderService.InitOrderResult holdCard(long sellerId, long productId, boolean testMode) {
        try {
            return cartTestSupport.holdCartWithParams(sellerId, null);
        } catch (ApiV2ParseException e) {
            if (testMode) throw e;
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(e.getRawData());
            log.error("Product {}: fail to move, {}", productId, errorRsp.getMessage());
            return null;
        }
    }

    private void moveOneOrder(long sellerId, long productId, long sizeId, boolean testMode) {
        // TODO: Найти заказ для указанных product item, если есть - ошибка
        CartControllerV2Test.CartAddRequest cartItem = new CartControllerV2Test.CartAddRequest()
                .setProductId(productId)
                .setSizeId(sizeId)
                .setCount(1);
        orderFlowTestUtils.fillCartWithList(Lists.newArrayList(cartItem));
        //
        long deliveryAepId = chooseDeliveryId();
        orderFlowTestUtils.setCartAddressEndpoint(deliveryAepId);
        //
        OrderService.InitOrderResult initOrder = holdCard(sellerId, productId, testMode);
        if (Objects.isNull(initOrder)) {
            log.info("Product {}, size {}: fail to move", productId, sizeId);
            return;
        }
        Assertions.assertThat(initOrder.getPaymentSystem()).isEqualTo(BoutiqueBankService.BOUTIQUE_SCHEMA);
        //
        OrderDTO testOrder = orderFlowTestUtils.loadOrderSuccessfull(initOrder.getOrderId(), true);
        Assertions.assertThat(testOrder.getDeliveryCost()).isEqualByComparingTo(BigDecimal.ZERO);
        //
        String holdCallbackUrl = initOrder.getBank_url();
        if (!holdCallbackUrl.contains("https://oskelly.ru")) {
            holdCallbackUrl = holdCallbackUrl.replace("https://", "http://");
        }
        orderFlowTestUtils.callOrderHoldCallback(initOrder.getOrderId(), holdCallbackUrl);
        //
        testOrder.getItems().forEach(op -> orderFlowTestUtils.rejectOrApprovePosition(op.getId(), true));
        //
        // TODO: Add / find office address in seller`s addresses
        orderFlowTestUtils.changeAddressEndpoint(testOrder.getId(), sellerOfficePickupId, deliveryAepId);
        AdminPanelOrderDetailsDTO orderWithDeliveryInfo = orderFlowTestUtils.loadAdminOrderSuccessful(null, initOrder.getOrderId(), false);
        Assertions.assertThat(orderWithDeliveryInfo.getOrderInfo().getOrderId()).isEqualTo(testOrder.getId());
        Assertions.assertThat(orderWithDeliveryInfo.getUserInfoDelivery().getBuyer().getAddress().getCity()).isEqualTo("Москва");
        Assertions.assertThat(orderWithDeliveryInfo.getUserInfoDelivery().getBuyer().getAddress().getAddressLine())
                .matches(".*Бережковская набережная,.*16.*");
        //
        OrderFlowTestUtils.OskellyDeliveryInfo s2oDelivery = OrderFlowTestUtils.OskellyDeliveryInfo.builder()
                .courierName("Moving-products-to-boutique").courierPhone("+79687854693").courierDate(LocalDate.now()).build();
        orderFlowTestUtils.takeOurselves(testOrder.getId(), s2oDelivery);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        for (OrderPositionDTO orderPosition : testOrder.getItems()) {
            orderFlowTestUtils.adminsApi1expertise(orderPosition.getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_PASSED_OK, null);
        }
        //
        orderFlowTestUtils.adminPanel_Charge(testOrder.getId());
        //
        executor.submit(() -> {
            // Ждем 2 минуты + 10 секунд
            TestUtils.sleep(130);
            OrderFlowTestUtils.OskellyDeliveryInfo o2bDelivery = OrderFlowTestUtils.OskellyDeliveryInfo.builder()
                    .courierName("Moving-products-to-boutique").courierPhone("+79687854693").courierDate(LocalDate.now()).build();
            ResponseEntity<String> sendResponse = orderFlowTestUtils.sendOurselves(testOrder.getId(), o2bDelivery);
            Assertions.assertThat(sendResponse.getStatusCode().is2xxSuccessful()).isTrue();
            //
            orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
            orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
            //
            log.info("Product {}, size {}: created order {}", productId, sizeId, testOrder.getId());
        });
    }

    private ProductDTO loadProductInfo(long productId, boolean testMode) {
        try {
            return orderFlowTestUtils.getProduct(productId).getData();
        } catch (Exception e) {
            if (testMode) throw e;
            return null;
        }
    }

    private void _XX_OrderFlow_Boutique_MoveOneItemToBoutique(long productId, boolean createMultipleOrders, boolean testMode) {
        ProductDTO productInfo = loadProductInfo(productId, testMode);
        if (Objects.isNull(productInfo)) {
            log.error("Product {}: fail to move, unable to load product info", productId);
            return;
        }
        if (Objects.isNull(productInfo.getSizes())) {
            log.error("Product {}: fail to move, size list is null", productId);
            return;
        }
        productInfo.getSizes().forEach(size -> {
            try {
                int itemCount = createMultipleOrders ? size.getCount() : 1;
                for (int i = 0; i < itemCount; i++) {
                    moveOneOrder(productInfo.getSeller().getId(), productInfo.getProductId(), size.getId(), testMode);
                }
                if (itemCount > 1) {
                    log.info("Product {}, size {}: created {} orders", productId, size.getId(), itemCount);
                }
            } catch (Exception e) {
                if (testMode) throw e;
                log.error("Product {}, size {}: fail to move, {}", productId, size.getId(), e.getMessage());
            }
        });
    }

    //@Test
    public void _01_OrderFlow_Boutique_MoveOneItemToBoutique() {
        // LOCAL: 2542, MARS: 113799
        _XX_OrderFlow_Boutique_MoveOneItemToBoutique(2542, false, true);
    }

    private void _XX_OrderFlow_Boutique_MoveNItemsToBoutique(long productId, long count, boolean testMode) {
        ProductDTO productInfo = loadProductInfo(productId, testMode);
        if (Objects.isNull(productInfo)) {
            log.error("Product {}: fail to move, unable to load product info", productId);
            return;
        }
        if (Objects.isNull(productInfo.getSizes())) {
            log.error("Product {}: fail to move, size list is null", productId);
            return;
        }
        if (productInfo.getSizes().size() > 1) {
            log.error("Product {}: fail to move, multiple sizes: {}", productId, productInfo.getSizes().size());
            return;
        }
        SizeValueDTO size = productInfo.getSizes().get(0);
        if (size.getCount() < count) {
            log.error("Product {}: fail to move, not enough amount: {}, requested: {}", productId, size.getCount(), count);
            return;
        }
        try {
            for (int i = 0; i < count; i++) {
                moveOneOrder(productInfo.getSeller().getId(), productInfo.getProductId(), size.getId(), testMode);
            }
            log.info("Product {}, size {}: created {} orders", productId, size.getId(), count);
        } catch (Exception e) {
            if (testMode) throw e;
            log.error("Product {}, size {}: fail to move, {}", productId, size.getId(), e.getMessage());
        }
    }

    private void _XX_OrderFlow_Boutique_MoveNItemsToBoutique(long productId, long sizeId, long count, boolean testMode) {
        ProductDTO productInfo = loadProductInfo(productId, testMode);
        if (Objects.isNull(productInfo)) {
            log.error("Product {}: fail to move, unable to load product info", productId);
            return;
        }
        if (Objects.isNull(productInfo.getSizes())) {
            log.error("Product {}: fail to move, size list is null", productId);
            return;
        }
        Optional<SizeValueDTO> size = productInfo.getSizes().stream()
                .filter(sizeValueDTO -> sizeValueDTO.getId() == sizeId)
                .findAny();
        if (!size.isPresent()) {
            log.error("Product {}: fail to move, cannot find size: {}", productId, sizeId);
            return;
        }
        if (size.get().getCount() < count) {
            log.error("Product {}: fail to move, not enough amount: {}, requested: {}", productId, size.get().getCount(), count);
            return;
        }
        try {
            for (int i = 0; i < count; i++) {
                moveOneOrder(productInfo.getSeller().getId(), productInfo.getProductId(), sizeId, testMode);
            }
            log.info("Product {}, size {}: created {} orders", productId, sizeId, count);
        } catch (Exception e) {
            if (testMode) throw e;
            log.error("Product {}, size {}: fail to move, {}", productId, sizeId, e.getMessage());
        }
    }

    //@Test
    public void _02_OrderFlow_Boutique_MoveItemLstToBoutique() {
        Collection<Long> importBela000 = Arrays.asList(562030L, 552632L, 539380L, 539073L, 539445L, 538916L, 538954L, 538963L, 539049L, 539451L, 539337L, 539351L, 539300L, 539315L, 539088L, 538895L, 538995L, 538899L, 539435L, 539438L, 539156L, 539416L, 539404L, 538985L, 539143L, 552633L, 539794L, 539393L, 539097L, 539042L, 151499L, 151508L, 539130L, 538987L, 539408L, 538914L, 538934L, 538035L, 538058L, 538313L, 538322L, 537991L, 531600L, 531585L, 531570L, 530965L, 531000L, 531046L, 151402L, 151427L, 153645L, 484333L, 530727L, 531046L, 539136L, 680574L, 680170L, 596718L, 596724L, 591225L, 680165L, 680055L, 679926L, 680562L, 689790L, 682546L, 689781L, 689774L, 680170L, 613901L, 613340L, 639243L, 640039L, 645741L, 645761L, 646603L, 645686L, 646669L, 645709L, 639074L, 645693L, 638795L, 630115L, 630173L, 631849L, 631882L, 631922L, 634323L, 633004L, 634327L, 633018L, 633034L, 633049L, 632996L, 633023L, 633037L, 633046L, 613542L);
        Collection<Long> importBela100 = Arrays.asList(621898L, 622442L, 621976L, 613591L, 613682L, 613704L, 619915L, 619907L, 613287L, 613724L, 613982L, 613936L, 613284L, 613292L, 613242L, 680503L, 680101L, 680020L, 680019L, 680013L, 679889L, 677539L, 677552L, 679833L, 679899L, 679861L, 680000L, 679922L, 679914L, 398856L, 415011L, 414900L, 415272L, 415094L, 414907L, 415134L, 414995L, 415138L, 415159L, 415151L, 414935L, 414538L, 393480L, 398867L, 398819L, 398880L, 398886L, 398890L, 398851L, 398821L, 398805L, 398928L, 393478L, 394328L, 393023L, 395148L, 389949L, 392053L, 394294L, 395205L, 395127L, 394349L, 394278L, 394286L, 393459L, 392037L, 394504L, 395121L, 395119L, 395129L, 389928L, 601741L, 602509L, 663817L, 663804L, 663799L, 663809L, 663808L, 596824L, 601528L, 602370L, 601569L, 601620L, 601726L, 601460L, 596890L, 596882L, 642302L, 642390L, 642298L, 541294L, 538999L, 576576L, 538168L, 537735L, 537730L, 640143L, 654859L, 46294L, 46305L);
        Collection<Long> importBela200 = Arrays.asList(420301L, 420271L, 206265L, 341151L, 211781L, 214622L, 215469L, 214628L, 214586L, 214556L, 259951L, 259943L, 259888L, 192596L, 192589L, 525501L, 539803L, 539805L, 606320L, 606316L, 308598L, 308221L, 478550L, 308205L, 308544L, 308535L, 689893L, 689900L, 689901L, 689903L, 346952L, 689921L, 689924L, 304089L, 307106L, 304065L, 306576L, 307466L, 306381L, 306598L, 304239L, 304075L, 306392L, 202031L, 202481L, 202452L, 616769L, 588085L, 588094L, 588574L, 408077L, 408381L, 408378L, 408087L, 358890L, 359655L, 359664L, 359622L, 359641L, 359647L, 359673L, 359666L, 358827L, 368837L, 358820L, 358799L, 358794L, 359178L, 277951L, 277938L, 277934L, 679955L, 679943L, 679932L, 679901L, 679897L, 679879L, 490435L, 490382L, 490412L, 490529L, 490479L, 627367L, 627372L, 627363L, 627166L, 626631L, 626636L, 626643L, 627412L, 627117L, 627130L, 627271L, 626542L, 627134L, 627321L, 627343L, 627355L, 626524L, 627105L);
        Collection<Long> importBela300 = Arrays.asList(278757L, 494527L, 524523L, 494536L, 494550L, 184742L, 184334L, 184740L, 184338L, 403138L, 456796L, 456799L, 558955L, 381776L, 381733L, 381708L, 346129L, 404857L, 384007L, 558946L, 381669L, 403085L, 634411L, 619527L, 464342L, 558992L, 559036L, 603899L, 269523L, 558927L, 401174L, 464163L, 404806L, 404776L, 559019L, 384169L, 464282L, 384163L, 272388L, 464269L, 384239L, 642539L, 638113L, 635099L, 618141L, 642456L, 517636L, 519178L, 456760L, 672914L, 585876L, 634340L, 559437L, 641194L, 559418L, 433562L, 433544L, 433285L, 427418L, 426970L, 426931L, 426153L, 426315L, 426012L, 425143L, 425138L, 692745L, 692763L, 692740L, 692760L, 692717L, 692733L, 692737L, 451142L, 337524L, 405385L, 405403L, 335663L, 577792L, 577797L);
        Collection<Long> importBeegzDB = Arrays.asList(
                /* sellerId = 245992 */ 692552L, 692549L, 692550L, 692558L, 692556L, 692548L, 692560L, 692561L, 692559L, 692553L, 692557L, 692555L,
                /* sellerId =  59996 */ 474648L, 387635L, 385966L, 178422L, 474637L, 611816L, 165351L, 601251L, 510608L, 614107L, 560888L, 259416L, 474891L, 511822L, 560901L, 564714L, 564722L, 564725L, 601245L, 607777L, 616582L, 623800L, 616578L, 474625L, 582757L, 188035L, 336694L, 474641L, 474677L, 616574L, 616585L, 616588L, 474674L, 510618L, 611995L, 614112L, 614084L, 403749L, 390536L, 403735L, 616525L, 616536L, 623787L, 623795L, 616540L, 616541L, 619654L, 619659L, 474668L, 386653L, 614122L, 614128L, 582748L);
        Collection<Long> importFailLst = Arrays.asList(381708L, 559036L, 634411L, 464342L, 558992L, 603899L, 269523L, 558927L, 401174L, 384169L, 464282L, 384163L, 464269L, 642539L, 638113L, 635099L, 618141L, 585876L, 634340L, 539805L, 368837L, 679955L, 214628L, 689900L, 679899L);
        //
        Collection<Long> importList = Arrays.asList(811127L,811128L,811129L,811130L,811131L,804949L,804950L);
        importList.forEach(productId -> {
            log.info("Product {}: init", productId);
            _XX_OrderFlow_Boutique_MoveOneItemToBoutique(productId, true, false);
            log.info("Product {}: done", productId);
        });
    }

    //@Test
    public void _02_OrderFlow_Boutique_MoveToBoutique_FromBela_10_Nov_2022() {
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(668571, 4, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(668618, 5, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(299908, 5, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(349124, 5, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(508952, 3, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(512018, 2, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(668562, 3, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(527278, 8, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(477277, 18, false);
    }

    //@Test
    public void _02_OrderFlow_Boutique_MoveToBoutique_Invent_10_Nov_2022_NoSize() {
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(272388, 1, false); - товар продан
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(508952, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(527255, 1, false);
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(560416, 11, false); - товар скрыт
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(603899, 1, false); - товар продан
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(668562, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(668571, 1, false);
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(670698, 5, false); - товар скрыт
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(699727, 1, false); - товар продан
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(701985, 1, false); - товар продан
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(715789, 2, false); - товар продан
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(720791, 1, false); - товар продан
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(721314, 2, false);
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(723394, 2, false); - количество указано 2, но в наличии только 1
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(723411, 1, false); - товар продан
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(723422, 1, false); - товар продан
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(760073, 1, false);
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(762170, 1, false); - товар продан
    }

    //@Test
    public void _02_OrderFlow_Boutique_MoveToBoutique_Invent_10_Nov_2022_WithSize() {
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(591225, 5, 1, false); - уже перемещается 1338580
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(642867, 249, 1, false);
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(696372, 274, 1, false); - уже перемещается 1341603
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(702312, 413, 1, false); - уже перемещается 1345244
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(702951, 20, 2, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(702955, 16, 3, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(702955, 20, 3, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(702955, 21, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(702959, 279, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(702969, 20, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(702986, 15, 2, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(703286, 21, 1, false);
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(703289, 18, 1, false); - уже перемещается 1344871
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(703293, 19, 1, false); - уже перемещается 1344578
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(706956, 312, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(706958, 314, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(706962, 80, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(706964, 78, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(706964, 77, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(706965, 79, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(706966, 77, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(706975, 79, 1, false);
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(745572, 249, 1, false); - товар продан
        //_XX_OrderFlow_Boutique_MoveNItemsToBoutique(757449, 407, 2, false); - уже перемещается 1358809, 1369257
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(776196, 272, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(776199, 12, 1, false);
        _XX_OrderFlow_Boutique_MoveNItemsToBoutique(776199, 17, 1, false);
    }
}
