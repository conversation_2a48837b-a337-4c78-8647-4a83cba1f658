package ru.oskelly.tests.pr.suite6_2.orderflow;

import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;

/**
 * Run with:
 *   mvn -Dtest=ru.oskelly.tests.pr.suite6_2.orderflow.TestSuite06_2 test
 * TODO: Fix "HHH000485: Illegally attempted to associate a proxy for entity [] with id [1427] with two open sessions.
 * when running order OrderFlowOrderTagsTest.class, OrderFlowDocumentLinksEnTest.class, OrderFlowOrderDeliveryTest.class
 */
@Suite
@SelectClasses({
        // These tests with shared contexts (fine)
        OrderFlowDocumentLinksRuTest.class,
        OrderFlowOrderTagsTest.class,
        OrderFlowOrderDeliveryTest.class,
        OrderFlowPayoutFailTest.class,
        OrderFlowCrossBorderTest.class,
        // These tests requires more contexts (refactor)
        OrderFlowDraftOrdersTest.class,
        OrderFlowSplitTest.class,
        OrderFlowDocumentLinksEnTest.class,
        // These tets (refactor if we need)
        OrderFlowBoutiqueImportTest.class,
        OrderFlowCrossBorderCaptureRefundTest.class
})
public class OrderFlow06_2TestSuite {

}
