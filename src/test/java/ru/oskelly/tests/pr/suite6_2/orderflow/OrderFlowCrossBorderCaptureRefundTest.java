package ru.oskelly.tests.pr.suite6_2.orderflow;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.test.context.TestPropertySource;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;

@Slf4j

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
@Disabled("Need to adjust test behaviours on capture-refund")
@TestPropertySource(properties = {"payments.cb-fallback-mode=plutus/auto-capture"})
public class OrderFlowCrossBorderCaptureRefundTest extends OrderFlowCrossBorderTest {

    // Just call each test from super and ensure that everything is fine

}
