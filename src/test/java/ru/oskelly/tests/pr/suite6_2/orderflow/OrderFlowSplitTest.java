package ru.oskelly.tests.pr.suite6_2.orderflow;

import com.google.common.collect.ImmutableList;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestFixtures;
import ru.oskelly.tests.pr.suite6_1.orderflow.OrderFlowTestUtils;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderExtraPropInfo;
import su.reddot.domain.model.order.OrderPayment;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.dto.order.adminpanel.AdminPanelOrderDetailsDTO;
import su.reddot.domain.service.dto.order.adminpanel.AdminPanelSplitOrderResponseDTO;
import su.reddot.domain.service.task.ScheduledSplitOrderTaskRunner;
import su.reddot.infrastructure.bank.BoutiqueBankService;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.presentation.api.v2.order.OrderSplitRequest;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;
import static su.reddot.infrastructure.configparam.ConfigParamService.CONFIG_PARAM_SOME_ITEMS_IN_BOUTIQUE_CART;


@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
public class OrderFlowSplitTest extends OrderFlowTest {

    @Autowired
    private CallInTransaction callInTransaction;
    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private OrderFlowTestFixtures orderFlowTestFixtures;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    private ScheduledSplitOrderTaskRunner scheduledSplitOrderTaskRunner;

    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private ProductRepository productRepository;
    @MockBean
    ConfigParamService configParamServiceMock;

    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;
    @Value("${test-boutique.buyer-id}")
    private Long boutiqueBuyerId;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;

    private final String boutiqueBuyerPassword = "password4boutique";
    private ApiV2Client apiV2Client;
    private User boutiqueUser;
    private List<Long> productIds = new ArrayList<>();

    @PostConstruct
    private void init() {
        requestMoreCtx();
        boutiqueUser = callInTransaction.runInNewTransaction(() -> orderFlowTestFixtures.prepareBoutiqueUser(boutiqueBuyerId, boutiqueBuyerPassword));
        apiV2Client = new ApiV2Client(boutiqueUser.getEmail(), boutiqueBuyerPassword);
        orderFlowTestUtils.init(boutiqueUser.getEmail(), boutiqueBuyerPassword);
        cartTestSupport.setUserId(boutiqueUser.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
    }

    @Test
    @Transactional
    public void _01_split_2_order_with_2_positions() {
        setUpMockConfigParamService();

        productIds = orderFlowTestUtils.createDraftProducts(usualSellerId, 4);
        commitAndStartNewTransaction();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(BoutiqueBankService.BOUTIQUE_SCHEMA)
                .noReceiptsMode(true)
                .productIdsList(productIds)

                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2, 3, 4))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.EXPERTISE_JUST_DONE)

                .build());
        //
        OrderDTO validateOrderDto = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(validateOrderDto.getItems()).hasSize(4);
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(validateOrderDto.getId(), BoutiqueBankService.BOUTIQUE_SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        Assertions.assertThat(orderPayment.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(40_000_00, 2).add(validateOrderDto.getDeliveryCost()));
        Assertions.assertThat(orderPayment.getRefundAmountInBase()).isNull();

        OrderSplitRequest orderSplitRequest = new OrderSplitRequest();
        List<OrderPositionDTO> orderPositionDTOList = validateOrderDto.getItems().subList(0, 2);
        orderSplitRequest.setOrderId(validateOrderDto.getId());
        List<List<Long>> firstPositions = ImmutableList.of(
                ImmutableList.of(orderPositionDTOList.get(0).getId(), orderPositionDTOList.get(1).getId()));
        orderSplitRequest.setOrderPositions(firstPositions);
        ResponseEntity<AdminPanelSplitOrderResponseDTO> firstSplit = orderFlowTestUtils.splitOrderByOrderPositions(orderSplitRequest);

        assertEquals(200, firstSplit.getStatusCode().value());
        OrderDTO validateOrderUntillSplitDto = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);

        assertEquals(2, validateOrderUntillSplitDto.getItems().size());

        OrderSplitRequest orderSecondSplitRequest = new OrderSplitRequest();
        List<OrderPositionDTO> orderSecondPositionDTOList = validateOrderUntillSplitDto.getItems().subList(0, 2);

        OrderSplitRequest secondOrderSplitRequest = new OrderSplitRequest();
        secondOrderSplitRequest.setOrderId(validateOrderDto.getId());
        List<List<Long>> secondPositions = ImmutableList.of(
                ImmutableList.of(orderSecondPositionDTOList.get(0).getId(), orderSecondPositionDTOList.get(1).getId()));
        secondOrderSplitRequest.setOrderPositions(secondPositions);

        ResponseEntity<AdminPanelSplitOrderResponseDTO> secondSplit = orderFlowTestUtils.splitOrderByOrderPositions(secondOrderSplitRequest);
        assertEquals(200, secondSplit.getStatusCode().value());
        commitAndStartNewTransaction();
        Order finalOrder = orderRepository.getOne(validateOrderDto.getId());
        assertEquals(OrderState.COMPLETED, finalOrder.getState());

        List<Order> splittedOrders = orderRepository.findOrdersByParentOrder(finalOrder);
        assertEquals(2, splittedOrders.size());
        List<OrderExtraPropInfo> orderProps = finalOrder.getOrderExtraPropValues().stream()
                .map(it -> it.getOrderExtraProp().getId())
                .map(OrderExtraPropInfo::fromId)
                .collect(Collectors.toList());
        splittedOrders.forEach(order -> {
            Assertions.assertThat(order.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_00, 2));
            assertEquals(DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, order.getDeliveryState());
            assertEquals(DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, order.getDeliveryState());
            List<OrderExtraPropInfo> splitted = order.getOrderExtraPropValues().stream()
                    .map(it -> it.getOrderExtraProp().getId())
                    .map(OrderExtraPropInfo::fromId)
                    .collect(Collectors.toList());
            Assertions.assertThat(splitted).containsOnly(OrderExtraPropInfo.ORDER_EXTRA_PROP_ORDER_TAG_SELLER_CONCIERGE,
                   OrderExtraPropInfo.ORDER_EXTRA_PROP_SALES_REQUEST_ID, OrderExtraPropInfo.ORDER_EXTRA_PROP_ORDER_TAG_BOUTIQUES);
        });
    }

    @Test
    @Transactional
    public void _02_split_4_order_with_1_positions() {
        setUpMockConfigParamService();

        productIds = orderFlowTestUtils.createDraftProducts(usualSellerId, 4);
        commitAndStartNewTransaction();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(BoutiqueBankService.BOUTIQUE_SCHEMA)
                .noReceiptsMode(true)
                .productIdsList(productIds)

                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2, 3, 4))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.EXPERTISE_JUST_DONE)

                .build());
        //
        OrderDTO validateOrderDto = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(validateOrderDto.getItems()).hasSize(4);
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(validateOrderDto.getId(), BoutiqueBankService.BOUTIQUE_SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        Assertions.assertThat(orderPayment.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(40_000_00, 2).add(validateOrderDto.getDeliveryCost()));
        Assertions.assertThat(orderPayment.getRefundAmountInBase()).isNull();

        AdminPanelOrderDetailsDTO startOrderInfo = orderFlowTestUtils.loadAdminOrderSuccessful(apiV2Client, testOrder.getId(), true);
        Assertions.assertThat(startOrderInfo.getChildren()).isNull();
        Assertions.assertThat(startOrderInfo.getProducts().getList()).hasSize(4);

        OrderSplitRequest orderSplitRequest = new OrderSplitRequest();
        List<OrderPositionDTO> orderPositionDTOList = validateOrderDto.getItems().subList(0, 4);
        orderSplitRequest.setOrderId(validateOrderDto.getId());
        List<List<Long>> positions = ImmutableList.of(
                ImmutableList.of(orderPositionDTOList.get(0).getId()),
                ImmutableList.of((orderPositionDTOList.get(1).getId())),
                ImmutableList.of((orderPositionDTOList.get(2).getId())),
                ImmutableList.of((orderPositionDTOList.get(3).getId())));
        orderSplitRequest.setOrderPositions(positions);
        //
        ResponseEntity<AdminPanelSplitOrderResponseDTO> splitRequest = orderFlowTestUtils.splitOrderByOrderPositions(orderSplitRequest);

        assertEquals(200, splitRequest.getStatusCode().value());
        commitAndStartNewTransaction();

        Order finalOrder = orderRepository.getOne(validateOrderDto.getId());
        assertEquals(OrderState.COMPLETED, finalOrder.getState());

        List<Order> splittedOrders = orderRepository.findOrdersByParentOrder(finalOrder);
        splittedOrders.forEach(order -> {
            Assertions.assertThat(order.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            assertEquals(DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, order.getDeliveryState());
        });

        AdminPanelOrderDetailsDTO finalOrderInfo = orderFlowTestUtils.loadAdminOrderSuccessful(apiV2Client, finalOrder.getId(), true);
        Assertions.assertThat(finalOrderInfo.getChildren()).hasSize(4);
        Assertions.assertThat(finalOrderInfo.getProducts().getList()).isEmpty();
    }

    @Test
    @Transactional
    public void _03_split_splitted_order() {
        setUpMockConfigParamService();

        productIds = orderFlowTestUtils.createDraftProducts(usualSellerId, 4);
        commitAndStartNewTransaction();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(BoutiqueBankService.BOUTIQUE_SCHEMA)
                .noReceiptsMode(true)
                .productIdsList(productIds)

                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2, 3, 4))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.EXPERTISE_JUST_DONE)

                .build());
        //
        OrderDTO validateOrderDto = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(validateOrderDto.getItems()).hasSize(4);
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(validateOrderDto.getId(), BoutiqueBankService.BOUTIQUE_SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        Assertions.assertThat(orderPayment.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(40_000_00, 2).add(validateOrderDto.getDeliveryCost()));
        Assertions.assertThat(orderPayment.getRefundAmountInBase()).isNull();
        //
        AdminPanelOrderDetailsDTO startOrderInfo = orderFlowTestUtils.loadAdminOrderSuccessful(apiV2Client, testOrder.getId(), true);
        Assertions.assertThat(startOrderInfo.getChildren()).isNull();
        Assertions.assertThat(startOrderInfo.getProducts().getList()).hasSize(4);

        OrderSplitRequest orderSplitRequest = new OrderSplitRequest();
        List<OrderPositionDTO> orderPositionDTOList = validateOrderDto.getItems().subList(0, 4);
        orderSplitRequest.setOrderId(validateOrderDto.getId());
        List<List<Long>> positions = ImmutableList.of(ImmutableList.of(orderPositionDTOList.get(0).getId(), orderPositionDTOList.get(1).getId(), orderPositionDTOList.get(2).getId()));

        orderSplitRequest.setOrderPositions(positions);
        //
        ResponseEntity<AdminPanelSplitOrderResponseDTO> splitRequest = orderFlowTestUtils.splitOrderByOrderPositions(orderSplitRequest);

        assertEquals(200, splitRequest.getStatusCode().value());
        commitAndStartNewTransaction();
        List<Order> firstSplittedOrder = orderRepository.findOrdersByParentOrder(orderRepository.getOne(testOrder.getId()));
        assertEquals(1, firstSplittedOrder.size());
        assertEquals(3, firstSplittedOrder.get(0).getOrderPositions().size());
        assertEquals(testOrder.getId(), firstSplittedOrder.get(0).getParentOrder().getId());


        OrderDTO firstOrderDto = orderFlowTestUtils.loadOrderSuccessfull(firstSplittedOrder.get(0).getId(), true);
        OrderSplitRequest secondOrderSplitRequest = new OrderSplitRequest();
        List<OrderPositionDTO> secondOrderPositionDTOList = firstOrderDto.getItems().subList(0, 3);
        secondOrderSplitRequest.setOrderId(firstOrderDto.getId());
        List<List<Long>> secondPositions = ImmutableList.of(
                ImmutableList.of(secondOrderPositionDTOList.get(0).getId()),
                ImmutableList.of((secondOrderPositionDTOList.get(1).getId())),
                ImmutableList.of((secondOrderPositionDTOList.get(2).getId())));

        secondOrderSplitRequest.setOrderPositions(secondPositions);
        //
        ResponseEntity<AdminPanelSplitOrderResponseDTO> secondSplitRequest = orderFlowTestUtils.splitOrderByOrderPositions(secondOrderSplitRequest);

        List<Order> secondSplittedOrders = orderRepository.findOrdersByParentOrder(orderRepository.getOne(firstOrderDto.getId()));
        assertEquals(200, splitRequest.getStatusCode().value());
        commitAndStartNewTransaction();

        assertEquals(3, secondSplittedOrders.size());
        Order finalOrder = orderRepository.getOne(firstOrderDto.getId());

        assertEquals(OrderState.COMPLETED, finalOrder.getState());

        secondSplittedOrders.forEach(order -> {
            Assertions.assertThat(order.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            assertEquals(DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, order.getDeliveryState());
        });
        //
        AdminPanelOrderDetailsDTO splitOrderInfo = orderFlowTestUtils.loadAdminOrderSuccessful(apiV2Client, testOrder.getId(), true);
        Assertions.assertThat(splitOrderInfo.getChildren()).hasSize(1);
        Assertions.assertThat(splitOrderInfo.getProducts().getList()).hasSize(1);
        AdminPanelOrderDetailsDTO childOrderInfo = splitOrderInfo.getChildren().get(0);
        Assertions.assertThat(childOrderInfo.getChildren()).hasSize(3);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_split_order_on_delivery_state() {
        setUpMockConfigParamService();

        productIds = orderFlowTestUtils.createDraftProducts(usualSellerId, 4);
        commitAndStartNewTransaction();
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(BoutiqueBankService.BOUTIQUE_SCHEMA)
                .noReceiptsMode(true)
                .productIdsList(productIds)

                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2, 3, 4))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.SPLIT)

                .build());
        //
        commitAndStartNewTransaction();
        scheduledSplitOrderTaskRunner.splitOrdersTask();
        List<Order> splittedOrders = orderRepository.findOrdersByParentOrder(orderRepository.getOne(testOrder.getId()));
        assertEquals(4 ,splittedOrders.size());

        Order finalOrder = orderRepository.getOne(testOrder.getId());

        assertEquals(OrderState.COMPLETED, finalOrder.getState());

        splittedOrders.forEach(order -> {
            Assertions.assertThat(order.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            assertEquals(DeliveryState.DELIVERED_TO_BUYER, order.getDeliveryState());
            List<OrderExtraPropInfo> splitted = order.getOrderExtraPropValues().stream()
                    .map(it -> it.getOrderExtraProp().getId())
                    .map(OrderExtraPropInfo::fromId)
                    .collect(Collectors.toList());
            Assertions.assertThat(splitted).containsOnly(OrderExtraPropInfo.ORDER_EXTRA_PROP_ORDER_TAG_SELLER_CONCIERGE,
                    OrderExtraPropInfo.ORDER_EXTRA_PROP_SALES_REQUEST_ID, OrderExtraPropInfo.ORDER_EXTRA_PROP_ORDER_TAG_BOUTIQUES);
        });
    }

    @Test
    @Transactional
    public void _05_split_7_2_3_2_fail_expertise_delivery_split() {
        setUpMockConfigParamService();

        productIds = orderFlowTestUtils.createDraftProducts(boutiqueUser.getId(), 7);
        commitAndStartNewTransaction();

        String salesRequestId = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd-HH-mm-ss"));
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(BoutiqueBankService.BOUTIQUE_SCHEMA)
                .noReceiptsMode(true)
                .productIdsList(productIds)
                .salesRequestId(salesRequestId)

                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5, 6, 7))
                .refusePositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.EXPERTISE_START)

                .build());
        //
//        OrderDTO validateOrderDTO = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(testOrder.getItems()).hasSize(7);
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), BoutiqueBankService.BOUTIQUE_SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        Assertions.assertThat(orderPayment.getAmountInBase()).isEqualByComparingTo(BigDecimal.valueOf(70_000_00, 2).add(testOrder.getDeliveryCost()));
        Assertions.assertThat(orderPayment.getRefundAmountInBase()).isNull();
        //
        AdminPanelOrderDetailsDTO startOrderInfo = orderFlowTestUtils.loadAdminOrderSuccessful(apiV2Client, testOrder.getId(), true);
        Assertions.assertThat(startOrderInfo.getChildren()).isNullOrEmpty();
        Assertions.assertThat(startOrderInfo.getProducts().getList()).hasSize(7);

        //Отрезаем 2 позиции в отдельный заказ
        OrderSplitRequest firstOrder2PositionsSplitRequest = new OrderSplitRequest();
        List<OrderPositionDTO> orderPositionDTOList = testOrder.getItems().subList(0, 7);
        firstOrder2PositionsSplitRequest.setOrderId(testOrder.getId());
        List<List<Long>> firstSplittedPositions = ImmutableList.of(
                ImmutableList.of(
                        orderPositionDTOList.get(0).getId(),
                        orderPositionDTOList.get(1).getId())
        );

        firstOrder2PositionsSplitRequest.setOrderPositions(firstSplittedPositions);
        //
        ResponseEntity<AdminPanelSplitOrderResponseDTO> first2splitRequest = orderFlowTestUtils.splitOrderByOrderPositions(firstOrder2PositionsSplitRequest);

        assertEquals(200, first2splitRequest.getStatusCode().value());
        commitAndStartNewTransaction();
        List<Order> firstSplittedOrder = orderRepository.findOrdersByParentOrder(orderRepository.getOne(testOrder.getId()));
        assertEquals(1, firstSplittedOrder.size());
        assertEquals(2, firstSplittedOrder.get(0).getOrderPositions().size());
        assertEquals(testOrder.getId(), firstSplittedOrder.get(0).getParentOrder().getId());

        OrderDTO firstWith2PositionsOrderDto = orderFlowTestUtils.loadOrderSuccessfull(firstSplittedOrder.get(0).getId(), true);
        testOrder = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(testOrder.getItems()).hasSize(5);

        //Отрезаем еще 3 позиции от начального заказа
        OrderSplitRequest secondOrderSplitRequest = new OrderSplitRequest();

        List<OrderPositionDTO> second3OrderPositionDTOList = testOrder.getItems().subList(0, 3);
        secondOrderSplitRequest.setOrderId(testOrder.getId());
        List<List<Long>> second3Positions = ImmutableList.of(
                ImmutableList.of(
                        second3OrderPositionDTOList.get(0).getId(),
                        second3OrderPositionDTOList.get(1).getId(),
                        second3OrderPositionDTOList.get(2).getId())
        );

        secondOrderSplitRequest.setOrderPositions(second3Positions);
        //
        ResponseEntity<AdminPanelSplitOrderResponseDTO> secondSplitRequest = orderFlowTestUtils.splitOrderByOrderPositions(secondOrderSplitRequest);
        Assertions.assertThat(secondSplitRequest.getStatusCode()).isEqualTo(HttpStatus.OK);
        long splitOrderId2ndWith3p = secondSplitRequest.getBody().getOrders().stream().findFirst().orElse(0L);

        List<Order> secondSplittedOrders = orderRepository.findOrdersByParentOrder(orderRepository.getOne(testOrder.getId()));
        commitAndStartNewTransaction();

        assertEquals(2, secondSplittedOrders.size());

        OrderDTO secondWith3PositionsOrderDto = orderFlowTestUtils.loadOrderSuccessfull(splitOrderId2ndWith3p, true);
        testOrder = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(testOrder.getItems()).hasSize(2);

        //Не проходим экспертизу для сплитованного заказа с двумя позициями
        AdminPanelOrderDetailsDTO firstWith2PositionsOrderInfo = orderFlowTestUtils.loadAdminOrderSuccessful(apiV2Client, firstWith2PositionsOrderDto.getId(), true);

        OrderFlowTestUtils.TestConfig expFailConfig = OrderFlowTestUtils.TestConfig.builder()
                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1, 2))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                .build();
        List<Product> products2 = new ArrayList<>();
                firstSplittedOrder.get(0).getOrderPositions().forEach(it -> products2.add(it.getProductItem().getProduct()));
        orderFlowTestUtils.processExpertiseSteps(firstWith2PositionsOrderDto, products2, expFailConfig);

        //Проходим экспертизу для сплитованного заказа с тремя позициями
        OrderFlowTestUtils.TestConfig expSuccessConfig = OrderFlowTestUtils.TestConfig.builder()
                .expertisePassPositions(ImmutableList.of(1, 2, 3))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())
                .build();
        Order splitOrderWith3p = orderRepository.getOne(splitOrderId2ndWith3p);
        List<Product> products3 = new ArrayList<>();
        splitOrderWith3p.getOrderPositions().forEach(it -> products3.add(it.getProductItem().getProduct()));
        orderFlowTestUtils.processExpertiseSteps(secondWith3PositionsOrderDto, products3, expSuccessConfig);

        orderFlowTestUtils.adminPanel_Charge(secondWith3PositionsOrderDto.getId());
        //
        orderFlowTestUtils.processHoldComplete(secondWith3PositionsOrderDto);
        //
        orderFlowTestUtils.sendOurselves(secondWith3PositionsOrderDto.getId(), null);
        //
        orderFlowTestUtils.changeDeliveryState(secondWith3PositionsOrderDto.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(secondWith3PositionsOrderDto.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        commitAndStartNewTransaction();
        //Доставили заказ с тремя позициями, разбиваем
        scheduledSplitOrderTaskRunner.splitOrdersTask();
        commitAndStartNewTransaction();

        List<Order> thirdSplittedOrders = orderRepository.findOrdersByParentOrder(orderRepository.getOne(splitOrderId2ndWith3p));


        assertEquals(3, thirdSplittedOrders.size());

        thirdSplittedOrders.forEach(order -> {
            Assertions.assertThat(order.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            assertEquals(DeliveryState.DELIVERED_TO_BUYER, order.getDeliveryState());
        });
        //
        AdminPanelOrderDetailsDTO thirdSplittedOrderInfo = orderFlowTestUtils.loadAdminOrderSuccessful(apiV2Client, secondWith3PositionsOrderDto.getId(), true);
        Assertions.assertThat(thirdSplittedOrderInfo.getChildren()).hasSize(3);
        Assertions.assertThat(thirdSplittedOrderInfo.getProducts().getList()).isEmpty();
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _06_splitOrder_onProductsInModeration() {
        setUpMockConfigParamService();
        //
        productIds = orderFlowTestUtils.createDraftProducts(usualSellerId, 4);
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .paymentsSchema(BoutiqueBankService.BOUTIQUE_SCHEMA)
                .noReceiptsMode(true)
                .productIdsList(productIds)

                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1, 2, 3, 4))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .stopPosition(OrderFlowTestUtils.TestStopPosition.SPLIT)

                .build());
        rollbackAndStartNewTransaction();
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_TRANSFERRED);
        Assertions.assertThat(order.getOrderExtraPropValues()).as("We have SplitOrderTask extraProp")
                .anyMatch(it -> Objects.equals(it.getOrderExtraProp().getId(), OrderExtraPropInfo.ORDER_EXTRA_PROP_SPLIT_ORDER_TASK.getId()));
        //
        Product productOnModeration = productRepository.getOne(productIds.get(0));  // This is main part of this test
        productOnModeration.setProductState(ProductState.NEED_MODERATION);          // It should pass with this state
        commitAndStartNewTransaction();
        //
        scheduledSplitOrderTaskRunner.splitOrdersTask();
        commitAndStartNewTransaction();
        //
        List<Order> childOrdersLst = orderRepository.findOrdersByParentOrder(orderRepository.getOne(testOrder.getId()));
        Assertions.assertThat(childOrdersLst).as("Order split done 4 all items").hasSize(4);
    }

    private void setUpMockConfigParamService() {
        reset(configParamServiceMock);
        when(configParamServiceMock.getValueAsBoolean(CONFIG_PARAM_SOME_ITEMS_IN_BOUTIQUE_CART, false))
                .thenReturn(true);
    }
}
