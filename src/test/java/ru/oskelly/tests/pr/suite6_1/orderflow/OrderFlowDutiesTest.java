package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.dao.address.CountryRepository;
import su.reddot.domain.dao.delivery.DeliveryCompanyCityRepository;
import su.reddot.domain.dao.delivery.DeliveryCompanyCountryRepository;
import su.reddot.domain.model.address.City;
import su.reddot.domain.model.address.Country;
import su.reddot.domain.model.address.CountryContextNameEnum;
import su.reddot.domain.model.discount.AbsolutePromoCode;
import su.reddot.domain.model.duty.DutyCalculatorConfig;
import su.reddot.domain.model.duty.OrderDuty;
import su.reddot.domain.model.duty.OrderPositionDuty;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.logistic.DeliveryCompany;
import su.reddot.domain.model.logistic.DeliveryCompanyCity;
import su.reddot.domain.model.logistic.DeliveryCompanyCountry;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.address.CityService;
import su.reddot.domain.service.dto.AddressAggregationEndpointRequestDTO;
import su.reddot.domain.service.dto.AddressAggregationRequestDTO;
import su.reddot.domain.service.dto.AddressEndpointAggregationDTO;
import su.reddot.domain.service.dto.AddressEndpointAggregationRequestDTO;
import su.reddot.domain.service.dto.duty.DutyDTO;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.delivery.DeliveryCompanyService;
import su.reddot.infrastructure.logistic.DeliveryOptionsService;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.presentation.api.v2.Api2Response;
import su.reddot.presentation.api.v2.cart.CartRequest;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@TestMethodOrder(MethodOrderer.MethodName.class)
@TestPropertySource(properties = {
        "internationalVersion=true",
        "app.default-country.default-country-id=2"
})
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
public class OrderFlowDutiesTest extends OrderFlowTest {

    @Autowired
    private UserService userService;
    @Autowired
    private CountryRepository countryRepository;
    @Autowired
    private CityService cityService;
    @Autowired
    private DeliveryCompanyCountryRepository deliveryCompanyCountryRepository;
    @Autowired
    private DeliveryCompanyCityRepository deliveryCompanyCityRepository;
    @Autowired
    private DeliveryCompanyService deliveryCompanyService;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    protected CallInTransaction callInTransaction;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.i11ls-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.i11ls-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private static final String DEFAULT_ZIP = "000000";

    private static final List<String> fromCountriesLst = ImmutableList.of("AE", "EE", "ES", "IT", "JP", "PL", "US");

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private Long prepareAdminsUser() {
        User adminsUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        return adminsUser.getId();
    }

    @PostConstruct
    private void init() {
        requestMoreCtx();
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareAdminsUser);
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.prepareUsualSellerData(usualSellerCounterpartyId));
    }

    @BeforeEach
    public void initEach() {
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.enableDutyCalculator(DutyCalculatorConfig.DType.UAE_IMPORT_DUTY, true));
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.enableDutyCalculator(DutyCalculatorConfig.DType.COMMISSION_VAT_DUTY, true));
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.enableCountryContext("SA", CountryContextNameEnum.BUYER_ADDRESS));
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.enableCountryContext("QA", CountryContextNameEnum.BUYER_ADDRESS));
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.enableCountryContext("KW", CountryContextNameEnum.BUYER_ADDRESS));
    }

    @AfterEach
    public void doneEach() {
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.enableDutyCalculator(DutyCalculatorConfig.DType.UAE_IMPORT_DUTY, false));
        callInTransaction.runInNewTransaction(() -> orderFlowTestUtils.enableDutyCalculator(DutyCalculatorConfig.DType.COMMISSION_VAT_DUTY, false));
        callInTransaction.runInNewTransaction(() -> saveSellerPickupCountryId(usualSellerId, null));
        callInTransaction.runInNewTransaction(() -> saveBuyerCountryId(buyerEmail, null));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
        orderFlowTestTcbMock = null;
    }

    private AddressEndpointAggregationDTO saveAddressEndpointAggregation4Buyer(String countryIsoCodeAlpha2) {
        Country country = countryRepository.findByIsoCodeAlpha2(countryIsoCodeAlpha2);
        City city = cityService.findAllCitiesByCountryId(country.getId(), false).get(0);
        //
        DeliveryCompany deliveryCompany = deliveryCompanyService.getAllDeliveryCompanies().stream()
                .filter(it -> Objects.equals(it.getName(), DeliveryOptionsService.OSKELLY_DELIVERY_COMPANY_NAME))
                .findFirst()
                .orElse(null);
        if (deliveryCompanyCountryRepository.countByCountry(country) == 0) {
            DeliveryCompanyCountry deliveryCompanyCountry = new DeliveryCompanyCountry().setDeliveryCompany(deliveryCompany).setCountry(country);
            deliveryCompanyCountryRepository.save(deliveryCompanyCountry);
        }
        if (deliveryCompanyCityRepository.countByCity(city) == 0) {
            DeliveryCompanyCity deliveryCompanyCity = new DeliveryCompanyCity().setDeliveryCompany(deliveryCompany).setCity(city);
            deliveryCompanyCityRepository.save(deliveryCompanyCity);
        }
        commitAndStartNewTransaction();
        //
        AddressAggregationEndpointRequestDTO addressEndpointDTO = new AddressAggregationEndpointRequestDTO();
        addressEndpointDTO.setFirstName(String.format("FirstName from %s", countryIsoCodeAlpha2));
        addressEndpointDTO.setLastName(UUID.randomUUID().toString());
        addressEndpointDTO.setPhone("+78005553505");
        //
        AddressAggregationRequestDTO addressDTO = new AddressAggregationRequestDTO();
        addressDTO.setAddress(String.format("Address line in %s", countryIsoCodeAlpha2));
        addressDTO.setAddress2(UUID.randomUUID().toString());
        addressDTO.setZipCode(DEFAULT_ZIP);
        addressDTO.setCountry(country.getId());
        addressDTO.setCity(city.getId());
        addressEndpointDTO.setAddress(addressDTO);
        //
        AddressEndpointAggregationRequestDTO addressEndpointAggregationDTO = new AddressEndpointAggregationRequestDTO();
        addressEndpointAggregationDTO.setPhysicalAddress(addressEndpointDTO);
        addressEndpointAggregationDTO.setUsePhysicalAddressForBilling(true);
        //
        ResponseEntity<String> saveResult = orderFlowTestUtils.saveAddressEndpointAggregation(addressEndpointAggregationDTO, HttpStatus.Series.SUCCESSFUL);
        //
        Api2Response<AddressEndpointAggregationDTO> apiV2Response = orderFlowTestUtils.fromJson(saveResult.getBody(), new TypeReference<Api2Response<AddressEndpointAggregationDTO>>(){});
        //
        Assertions.assertThat(apiV2Response.getData().getPhysicalAddress().getAddress().getCountryData().getIsoCodeAlpha2()).isEqualTo(countryIsoCodeAlpha2);
        //
        return apiV2Response.getData();
    }

    private void saveSellerPickupCountryId(long userId, String countryIsoCodeAlpha2) {
        Country country = Objects.isNull(countryIsoCodeAlpha2)
                ? null
                : countryRepository.findByIsoCodeAlpha2(countryIsoCodeAlpha2);
        User user = userService.getOne(userId);
        user.setPickupCountry(country);
        userService.save(user);
    }

    private void saveBuyerCountryId(String userMail, String countryIsoCodeAlpha2) {
        Country country = Objects.isNull(countryIsoCodeAlpha2)
                ? null
                : countryRepository.findByIsoCodeAlpha2(countryIsoCodeAlpha2);
        User user = userService.getUserByEmail(userMail);
        user.setCountry(country);
        userService.save(user);
    }

    private void _01_orderFlowDuties_validateCart(GroupedCart groupedCart, BigDecimal dutiesAmount) {
        Assertions.assertThat(groupedCart.getGroups()).allSatisfy(it -> {
            Assertions.assertThat(it.getDutiesAmount()).isEqualByComparingTo(dutiesAmount);
            BigDecimal dutiesTotalSum = CollectionUtils.emptyIfNull(it.getDuties()).stream()
                    .map(DutyDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            Assertions.assertThat(dutiesTotalSum).isEqualByComparingTo(dutiesAmount);
        });
    }

    private OrderDTO _01_orderFlowDuties_fromXXToYY_happy(String sourceIsoCode2, String targetIsoCode2,
                                                          long sellerPayoutAmount, BigDecimal dutiesAmount) {
        AddressEndpointAggregationDTO addressEndpointAggregationDTO = saveAddressEndpointAggregation4Buyer(targetIsoCode2);
        rollbackAndStartNewTransaction();
        //
        saveBuyerCountryId(buyerEmail, targetIsoCode2);
        commitAndStartNewTransaction();
        //
        saveSellerPickupCountryId(usualSellerId, sourceIsoCode2);
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)
                .targetDeliveryAepAggregationId(addressEndpointAggregationDTO.getId())

                .paymentsSchema(TcbBankService.SCHEMA)
                .itemsCount(1)

                .validateCart(it -> _01_orderFlowDuties_validateCart(it, dutiesAmount))

                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(ImmutableList.of(1))
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .sellerPayoutAmount(sellerPayoutAmount)
                .noAgentReportCompare(true)

                .build());
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_orderFlowDuties_fromXxToSa_happy() {
        fromCountriesLst.forEach(this::_01_orderFlowDuties_fromXxToSa_happy);
    }

    public void _01_orderFlowDuties_fromXxToSa_happy(String sourceIsoCode2) {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = _01_orderFlowDuties_fromXXToYY_happy(sourceIsoCode2, "SA",
                7_500_00L, BigDecimal.valueOf(3_225_00, 2));
        rollbackAndStartNewTransaction();
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
        Assertions.assertThat(order.getDuties()).hasSize(3)
                .allSatisfy(it -> {
                    Assertions.assertThat(it.getDutyCalculatorConfig().getPickupCountry().getIsoCodeAlpha2()).isEqualTo(sourceIsoCode2);
                    Assertions.assertThat(it.getDutyCalculatorConfig().getDeliveryCountry().getIsoCodeAlpha2()).isEqualTo("SA");
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(0);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(15_00, 2))
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(1_500_00, 2))
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE_WITH_VAT);
                    Assertions.assertThat(it.getSequence()).isEqualTo(1);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(15_00, 2))
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(1_725_00, 2))
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.DELIVERY_COMPANY_FEE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(2);
                    Assertions.assertThat(it.getPercent()).isNull();
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.ZERO)
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                });
        Assertions.assertThat(order.getOrderPositions()).allSatisfy(it ->
                Assertions.assertThat(it.getDuties()).hasSize(1)
                        .allSatisfy(itDuty -> Assertions.assertThat(itDuty.getType()).isEqualTo(OrderPositionDuty.Type.COMMISSION_OSKELLY))
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_orderFlowDuties_fromXxToAe_happy() {
        fromCountriesLst.stream().filter(it -> !Objects.equals("AE", it))
                .forEach(this::_01_orderFlowDuties_fromXxToAe_happy);
    }

    public void _01_orderFlowDuties_fromXxToAe_happy(String sourceIsoCode2) {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = _01_orderFlowDuties_fromXXToYY_happy(sourceIsoCode2, "AE", 7_500_00L, BigDecimal.valueOf(1_032_50, 2));
        rollbackAndStartNewTransaction();
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
        Assertions.assertThat(order.getDuties()).hasSize(3)
                .allSatisfy(it -> {
                    Assertions.assertThat(it.getDutyCalculatorConfig().getPickupCountry().getIsoCodeAlpha2()).isEqualTo(sourceIsoCode2);
                    Assertions.assertThat(it.getDutyCalculatorConfig().getDeliveryCountry().getIsoCodeAlpha2()).isEqualTo("AE");
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(0);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(5_00, 2))
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(500_00, 2))
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE_WITH_VAT);
                    Assertions.assertThat(it.getSequence()).isEqualTo(1);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(5_00, 2))
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(525_00, 2))
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.DELIVERY_COMPANY_FEE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(2);
                    Assertions.assertThat(it.getPercent()).isNull();
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_50, 2))
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                });
        Assertions.assertThat(order.getOrderPositions()).allSatisfy(it ->
                Assertions.assertThat(it.getDuties()).hasSize(1)
                        .allSatisfy(itDuty -> Assertions.assertThat(itDuty.getType()).isEqualTo(OrderPositionDuty.Type.COMMISSION_OSKELLY))
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_orderFlowDuties_fromAeToAe_happy() {
        OrderDTO testOrder = _01_orderFlowDuties_fromXXToYY_happy("AE","AE", 7_375_00L, BigDecimal.ZERO);
        rollbackAndStartNewTransaction();
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
        Assertions.assertThat(order.getDuties()).isEmpty();
        Assertions.assertThat(order.getOrderPositions()).allSatisfy(it ->
                Assertions.assertThat(it.getDuties()).hasSize(2)
                        .anySatisfy(itDuty -> {
                            Assertions.assertThat(itDuty.getType()).isEqualTo(OrderPositionDuty.Type.COMMISSION_OSKELLY);
                            Assertions.assertThat(itDuty.getSequence()).isEqualTo(0);
                            Assertions.assertThat(itDuty.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(25, 2))
                                    .isEqualByComparingTo(itDuty.getHoldPercent())
                                    .isEqualByComparingTo(itDuty.getEffectivePercent());
                            Assertions.assertThat(itDuty.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2))
                                    .isEqualByComparingTo(itDuty.getHoldAmount())
                                    .isEqualByComparingTo(itDuty.getEffectiveAmount());
                        })
                        .anySatisfy(itDuty -> {
                            Assertions.assertThat(itDuty.getType()).isEqualTo(OrderPositionDuty.Type.COMMISSION_OSKELLY_VAT);
                            Assertions.assertThat(itDuty.getSequence()).isEqualTo(1);
                            Assertions.assertThat(itDuty.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(5, 2))
                                    .isEqualByComparingTo(itDuty.getHoldPercent())
                                    .isEqualByComparingTo(itDuty.getEffectivePercent());
                            Assertions.assertThat(itDuty.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(125_00, 2))
                                    .isEqualByComparingTo(itDuty.getHoldAmount())
                                    .isEqualByComparingTo(itDuty.getEffectiveAmount());
                        })
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_orderFlowDuties_fromXxToKw_happy() {
        fromCountriesLst.forEach(this::_01_orderFlowDuties_fromXxToKw_happy);
    }

    public void _01_orderFlowDuties_fromXxToKw_happy(String sourceIsoCode2) {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = _01_orderFlowDuties_fromXXToYY_happy(sourceIsoCode2,"KW",
                7_500_00L, BigDecimal.valueOf(500_00, 2));
        rollbackAndStartNewTransaction();
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
        Assertions.assertThat(order.getDuties()).hasSize(3)
                .allSatisfy(it -> {
                    Assertions.assertThat(it.getDutyCalculatorConfig().getPickupCountry().getIsoCodeAlpha2()).isEqualTo(sourceIsoCode2);
                    Assertions.assertThat(it.getDutyCalculatorConfig().getDeliveryCountry().getIsoCodeAlpha2()).isEqualTo("KW");
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(0);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(5_00, 2))
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(500_00, 2))
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE_WITH_VAT);
                    Assertions.assertThat(it.getSequence()).isEqualTo(1);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.ZERO)
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.ZERO)
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.DELIVERY_COMPANY_FEE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(2);
                    Assertions.assertThat(it.getPercent()).isNull();
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.ZERO)
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                });
        Assertions.assertThat(order.getOrderPositions()).allSatisfy(it ->
                Assertions.assertThat(it.getDuties()).hasSize(1)
                        .allSatisfy(itDuty -> Assertions.assertThat(itDuty.getType()).isEqualTo(OrderPositionDuty.Type.COMMISSION_OSKELLY))
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _01_orderFlowDuties_fromXxToQa_happy() {
        fromCountriesLst.forEach(this::_01_orderFlowDuties_fromXxToQa_happy);
    }

    public void _01_orderFlowDuties_fromXxToQa_happy(String sourceIsoCode2) {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = _01_orderFlowDuties_fromXXToYY_happy(sourceIsoCode2,"QA",
                7_500_00L, BigDecimal.valueOf(500_00, 2));
        rollbackAndStartNewTransaction();
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
        Assertions.assertThat(order.getDuties()).hasSize(3)
                .allSatisfy(it -> {
                    Assertions.assertThat(it.getDutyCalculatorConfig().getPickupCountry().getIsoCodeAlpha2()).isEqualTo(sourceIsoCode2);
                    Assertions.assertThat(it.getDutyCalculatorConfig().getDeliveryCountry().getIsoCodeAlpha2()).isEqualTo("QA");
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(0);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(5_00, 2))
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(500_00, 2))
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE_WITH_VAT);
                    Assertions.assertThat(it.getSequence()).isEqualTo(1);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.ZERO)
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.ZERO)
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.DELIVERY_COMPANY_FEE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(2);
                    Assertions.assertThat(it.getPercent()).isNull();
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.ZERO)
                            .isEqualByComparingTo(it.getConfirmedAmount())
                            .isEqualByComparingTo(it.getEffectiveAmount());
                });
        Assertions.assertThat(order.getOrderPositions()).allSatisfy(it ->
                Assertions.assertThat(it.getDuties()).hasSize(1)
                        .allSatisfy(itDuty -> Assertions.assertThat(itDuty.getType()).isEqualTo(OrderPositionDuty.Type.COMMISSION_OSKELLY))
        );
    }

    private OrderDTO _02_orderFlowDuties_fromAeToXX_combo(String sourceIsoCode2, String targetIsoCode2) {
        AddressEndpointAggregationDTO addressEndpointAggregationDTO = saveAddressEndpointAggregation4Buyer(targetIsoCode2);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        commitAndStartNewTransaction();
        //
        saveBuyerCountryId(buyerEmail, targetIsoCode2);
        commitAndStartNewTransaction();
        //
        saveSellerPickupCountryId(usualSellerId, sourceIsoCode2);
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .pickupDeliveryAepId(pickupId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .targetDeliveryAepId(deliveryId)
                .targetDeliveryAepAggregationId(addressEndpointAggregationDTO.getId())

                .paymentsSchema(TcbBankService.SCHEMA)
                .itemsCount(5)

                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(ImmutableList.of(5))

                .expertisePassPositions(ImmutableList.of(4))
                .expertiseFailPositions(ImmutableList.of(3))
                .defectsByPositions(Lists.newArrayList(null, 1234L, null, null, null))
                .cleaningsByPositions(Lists.newArrayList(4321L, null, null, null, null))

                .sellerPayoutAmount(46_945_00L)
                .noAgentReportCompare(true)

                .build());
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_orderFlowDuties_fromXxToSa_combo() {
        fromCountriesLst.forEach(this::_02_orderFlowDuties_fromXxToSa_combo);
    }

    private void _02_orderFlowDuties_fromXxToSa_combo(String sourceIsoCode2) {
        OrderDTO testOrder = _02_orderFlowDuties_fromAeToXX_combo(sourceIsoCode2,"SA");
        rollbackAndStartNewTransaction();
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
        Assertions.assertThat(order.getDuties()).hasSize(3)
                .allSatisfy(it -> {
                    Assertions.assertThat(it.getDutyCalculatorConfig().getPickupCountry().getIsoCodeAlpha2()).isEqualTo(sourceIsoCode2);
                    Assertions.assertThat(it.getDutyCalculatorConfig().getDeliveryCountry().getIsoCodeAlpha2()).isEqualTo("SA");
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(0);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(15_00, 2))
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(22_500_00, 2));
                    Assertions.assertThat(it.getConfirmedAmount()).isEqualByComparingTo(BigDecimal.valueOf(15_000_00, 2));
                    Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_314_90, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE_WITH_VAT);
                    Assertions.assertThat(it.getSequence()).isEqualTo(1);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(15_00, 2))
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(25_875_00, 2));
                    Assertions.assertThat(it.getConfirmedAmount()).isEqualByComparingTo(BigDecimal.valueOf(17_250_00, 2));
                    Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(11_862_1350, 4));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.DELIVERY_COMPANY_FEE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(2);
                    Assertions.assertThat(it.getPercent()).isNull();
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                    Assertions.assertThat(it.getConfirmedAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                    Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                });
        Assertions.assertThat(order.getOrderPositions()).allSatisfy(it ->
                Assertions.assertThat(it.getDuties()).hasSize(1)
                        .allSatisfy(itDuty -> Assertions.assertThat(itDuty.getType()).isEqualTo(OrderPositionDuty.Type.COMMISSION_OSKELLY))
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_orderFlowDuties_fromXxToKw_combo() {
        fromCountriesLst.forEach(this::_02_orderFlowDuties_fromXxToKw_combo);
    }

    private void _02_orderFlowDuties_fromXxToKw_combo(String sourceIsoCode2) {
        OrderDTO testOrder = _02_orderFlowDuties_fromAeToXX_combo(sourceIsoCode2,"KW");
        rollbackAndStartNewTransaction();
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
        Assertions.assertThat(order.getDuties()).hasSize(3)
                .allSatisfy(it -> {
                    Assertions.assertThat(it.getDutyCalculatorConfig().getPickupCountry().getIsoCodeAlpha2()).isEqualTo(sourceIsoCode2);
                    Assertions.assertThat(it.getDutyCalculatorConfig().getDeliveryCountry().getIsoCodeAlpha2()).isEqualTo("KW");
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(0);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(5_00, 2))
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                    Assertions.assertThat(it.getConfirmedAmount()).isEqualByComparingTo(BigDecimal.valueOf(5_000_00, 2));
                    Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(3_438_30, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE_WITH_VAT);
                    Assertions.assertThat(it.getSequence()).isEqualTo(1);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.ZERO)
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                    Assertions.assertThat(it.getConfirmedAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                    Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.DELIVERY_COMPANY_FEE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(2);
                    Assertions.assertThat(it.getPercent()).isNull();
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                    Assertions.assertThat(it.getConfirmedAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                    Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                });
        Assertions.assertThat(order.getOrderPositions()).allSatisfy(it ->
                Assertions.assertThat(it.getDuties()).hasSize(1)
                        .allSatisfy(itDuty -> Assertions.assertThat(itDuty.getType()).isEqualTo(OrderPositionDuty.Type.COMMISSION_OSKELLY))
        );
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _02_orderFlowDuties_fromXxToQa_combo() {
        fromCountriesLst.forEach(this::_02_orderFlowDuties_fromXxToQa_combo);
    }

    private void _02_orderFlowDuties_fromXxToQa_combo(String sourceIsoCode2) {
        OrderDTO testOrder = _02_orderFlowDuties_fromAeToXX_combo(sourceIsoCode2,"QA");
        rollbackAndStartNewTransaction();
        //
        Order order = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
        Assertions.assertThat(order.getDuties()).hasSize(3)
                .allSatisfy(it -> {
                    Assertions.assertThat(it.getDutyCalculatorConfig().getPickupCountry().getIsoCodeAlpha2()).isEqualTo(sourceIsoCode2);
                    Assertions.assertThat(it.getDutyCalculatorConfig().getDeliveryCountry().getIsoCodeAlpha2()).isEqualTo("QA");
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(0);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.valueOf(5_00, 2))
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
                    Assertions.assertThat(it.getConfirmedAmount()).isEqualByComparingTo(BigDecimal.valueOf(5_000_00, 2));
                    Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.valueOf(3_438_30, 2));
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.VAT_ON_PRICE_WITH_VAT);
                    Assertions.assertThat(it.getSequence()).isEqualTo(1);
                    Assertions.assertThat(it.getPercent()).isEqualByComparingTo(BigDecimal.ZERO)
                            .isEqualByComparingTo(it.getConfirmedPercent())
                            .isEqualByComparingTo(it.getEffectivePercent());
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                    Assertions.assertThat(it.getConfirmedAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                    Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                })
                .anySatisfy(it -> {
                    Assertions.assertThat(it.getType()).isEqualTo(OrderDuty.Type.DELIVERY_COMPANY_FEE);
                    Assertions.assertThat(it.getSequence()).isEqualTo(2);
                    Assertions.assertThat(it.getPercent()).isNull();
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                    Assertions.assertThat(it.getConfirmedAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                    Assertions.assertThat(it.getEffectiveAmount()).isEqualByComparingTo(BigDecimal.ZERO);
                });
        Assertions.assertThat(order.getOrderPositions()).allSatisfy(it ->
                Assertions.assertThat(it.getDuties()).hasSize(1)
                        .allSatisfy(itDuty -> Assertions.assertThat(itDuty.getType()).isEqualTo(OrderPositionDuty.Type.COMMISSION_OSKELLY))
        );
    }

    private GroupedCart _03_orderFlowDuties_validateCart(CartRequest cartRequest, BigDecimal dutyAmount, BigDecimal ordersAmount) {
        GroupedCart cart00 = cartTestSupport.getCart(cartRequest, false);
        Assertions.assertThat(cart00.getGroups()).hasSize(1)
                .allSatisfy(it -> Assertions.assertThat(it.getDutiesAmount()).isEqualByComparingTo(dutyAmount))
                .allSatisfy(it -> Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(ordersAmount));
        Assertions.assertThat(cart00.getGroups().get(0).getDuties()).hasSize(1)
                .allSatisfy(it -> {
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(dutyAmount);
                    Assertions.assertThat(it.getDescription()).isEqualTo("Import Customs and Duties");
                });
        return cart00;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _03_orderFlowDuties_cartFromITtoAE_thenSA_thenQA() {
        AddressEndpointAggregationDTO aepaAE = saveAddressEndpointAggregation4Buyer("AE");
        AddressEndpointAggregationDTO aepaSA = saveAddressEndpointAggregation4Buyer("SA");
        AddressEndpointAggregationDTO aepaQA = saveAddressEndpointAggregation4Buyer("QA");
        commitAndStartNewTransaction();
        //
        saveBuyerCountryId(buyerEmail, "AE");
        commitAndStartNewTransaction();
        //
        saveSellerPickupCountryId(usualSellerId, "IT");
        commitAndStartNewTransaction();
        //
        String promoCodes = UUID.randomUUID().toString();
        orderFlowTestUtils.makeTestPromocodes(AbsolutePromoCode.class, promoCodes, BigDecimal.valueOf(1_00_00, 2));
        commitAndStartNewTransaction();
        //
        List<Long> itemList = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                        .sellerId(usualSellerId).maxItems(1).build())
                .stream()
                .map(Product::getId)
                .collect(Collectors.toList());
        commitAndStartNewTransaction();
        //
        GroupedCart cartInfo = orderFlowTestUtils.fillCartWithCurrencyCode(itemList, null);
        Assertions.assertThat(cartInfo.getGroups()).hasSize(1);
        //
        long sellerId = cartInfo.getGroups().get(0).getSeller().getId();
        cartInfo.getGroup(sellerId);
        //
        cartTestSupport.setCartAddressEndpoint();
        // ===> AE
        orderFlowTestUtils.changeTargetAddressEndpointAggregationCart(aepaAE.getId());
        GroupedCart cartAECartMode = _03_orderFlowDuties_validateCart(CartRequest.builder().build(),
                BigDecimal.valueOf(1_032_50, 2), BigDecimal.valueOf(11_532_50, 2)
        );
        Assertions.assertThat(cartAECartMode.getGroups()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getDiscount()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpoint()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpointAggregation()).isNull();
        });
        GroupedCart cartAECheckout = _03_orderFlowDuties_validateCart(CartRequest.builder().checkoutMode(true).build(),
                BigDecimal.valueOf(1_032_50, 2), BigDecimal.valueOf(11_532_50, 2)
        );
        Assertions.assertThat(cartAECheckout.getGroups()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getDiscount()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpoint()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpointAggregation()).satisfies(aepa -> {
                Assertions.assertThat(aepa.getId()).isEqualTo(aepaAE.getId());
            });
        });
        GroupedCart cartAEWithCode = _03_orderFlowDuties_validateCart(CartRequest.builder().sellerId(sellerId).promoCode(promoCodes).build(),
                BigDecimal.valueOf(1_022_25, 2), BigDecimal.valueOf(10_500_00, 2)
        );
        Assertions.assertThat(cartAEWithCode.getGroups()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getDiscount()).isNotNull();
            Assertions.assertThat(it.getDeliveryAddressEndpoint()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpointAggregation()).isNull();
        });
        OrderDTO validateAECode = cartTestSupport.checkPromoCode(sellerId, promoCodes);
        Assertions.assertThat(cartAEWithCode.getGroups().get(0))
                .usingRecursiveComparison()
                .ignoringFields("deliveryAddressEndpointAggregation", "deliveryAddressEndpoint")
                .isEqualTo(validateAECode);
        // ===> SA
        orderFlowTestUtils.changeTargetAddressEndpointAggregationCart(aepaSA.getId());
        GroupedCart cartSACartMode = _03_orderFlowDuties_validateCart(CartRequest.builder().build(),
                BigDecimal.valueOf(1_032_50, 2), BigDecimal.valueOf(11_532_50, 2)
        );
        Assertions.assertThat(cartSACartMode.getGroups()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getDiscount()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpoint()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpointAggregation()).isNull();
        });
        GroupedCart cartSACheckout = _03_orderFlowDuties_validateCart(CartRequest.builder().checkoutMode(true).build(),
                BigDecimal.valueOf(3_225_00, 2), BigDecimal.valueOf(13_725_00, 2)
        );
        Assertions.assertThat(cartSACheckout.getGroups()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getDiscount()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpoint()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpointAggregation()).satisfies(aepa -> {
                Assertions.assertThat(aepa.getId()).isEqualTo(aepaSA.getId());
            });
        });
        GroupedCart cartSAWithCode = _03_orderFlowDuties_validateCart(CartRequest.builder().sellerId(sellerId).promoCode(promoCodes).build(),
                BigDecimal.valueOf(3_192_75, 2), BigDecimal.valueOf(10_500_00, 2)
        );
        Assertions.assertThat(cartSAWithCode.getGroups()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getDiscount()).isNotNull();
            Assertions.assertThat(it.getDeliveryAddressEndpoint()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpointAggregation()).isNull();
        });
        OrderDTO validateSACode = cartTestSupport.checkPromoCode(sellerId, promoCodes);
        Assertions.assertThat(cartSAWithCode.getGroups().get(0))
                .usingRecursiveComparison()
                .ignoringFields("deliveryAddressEndpointAggregation", "deliveryAddressEndpoint")
                .isEqualTo(validateSACode);
        // ===> QA
        orderFlowTestUtils.changeTargetAddressEndpointAggregationCart(aepaQA.getId());
        GroupedCart cartQACartMode = _03_orderFlowDuties_validateCart(CartRequest.builder().build(),
                BigDecimal.valueOf(1_032_50, 2), BigDecimal.valueOf(11_532_50, 2)
        );
        Assertions.assertThat(cartQACartMode.getGroups()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getDiscount()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpoint()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpointAggregation()).isNull();
        });
        GroupedCart cartQACheckout = _03_orderFlowDuties_validateCart(CartRequest.builder().checkoutMode(true).build(),
                BigDecimal.valueOf(500_00, 2), BigDecimal.valueOf(11_000_00, 2)
        );
        Assertions.assertThat(cartQACheckout.getGroups()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getDiscount()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpoint()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpointAggregation()).satisfies(aepa -> {
                Assertions.assertThat(aepa.getId()).isEqualTo(aepaQA.getId());
            });
        });
        GroupedCart cartQAWithCode = _03_orderFlowDuties_validateCart(CartRequest.builder().sellerId(sellerId).promoCode(promoCodes).build(),
                BigDecimal.valueOf(495_00, 2), BigDecimal.valueOf(10_500_00, 2)
        );
        Assertions.assertThat(cartQAWithCode.getGroups()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getDiscount()).isNotNull();
            Assertions.assertThat(it.getDeliveryAddressEndpoint()).isNull();
            Assertions.assertThat(it.getDeliveryAddressEndpointAggregation()).isNull();
        });
        OrderDTO validateQACode = cartTestSupport.checkPromoCode(sellerId, promoCodes);
        Assertions.assertThat(cartQAWithCode.getGroups().get(0))
                .usingRecursiveComparison()
                .ignoringFields("deliveryAddressEndpointAggregation", "deliveryAddressEndpoint")
                .isEqualTo(validateQACode);
    }

    private GroupedCart _04_orderFlowDuties_validateCart(CartRequest cartRequest, BigDecimal dutyAmount, BigDecimal ordersAmount) {
        GroupedCart cart00 = cartTestSupport.getCart(cartRequest, false);
        Assertions.assertThat(cart00.getGroups()).hasSize(1)
                .allSatisfy(it -> Assertions.assertThat(it.getDutiesAmount()).isEqualByComparingTo(dutyAmount))
                .allSatisfy(it -> Assertions.assertThat(it.getFinalAmount()).isEqualByComparingTo(ordersAmount));
        Assertions.assertThat(cart00.getGroups().get(0).getDuties()).hasSize(1)
                .allSatisfy(it -> {
                    Assertions.assertThat(it.getAmount()).isEqualByComparingTo(dutyAmount);
                    Assertions.assertThat(it.getDescription()).isEqualTo("Import Customs and Duties");
                });
        return cart00;
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_orderFlowDuties_nullUserCartDutiesSellerIT_toAE_toSA_toQA() {
        String promoCodes = UUID.randomUUID().toString();
        orderFlowTestUtils.makeTestPromocodes(AbsolutePromoCode.class, promoCodes, BigDecimal.valueOf(1_00_00, 2));
        commitAndStartNewTransaction();
        //
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        Map<Long, Long> productsWithSize = products.stream()
                .collect(Collectors.toMap(Product::getId, it -> it.getAvailableProductItems().get(0).getSize().getId()));
        Long sellerId = products.stream().findFirst().map(Product::getSeller).map(User::getId).orElse(null);
        commitAndStartNewTransaction();
        //
        saveSellerPickupCountryId(usualSellerId, "IT");
        commitAndStartNewTransaction();
        //
        cartTestSupport.resetAuthInfo();
        cartTestSupport.cleanCart(false);
        productsWithSize.forEach((productsId, sizeId) -> cartTestSupport.addToCartSuccessful(productsId, sizeId, 1, false));
        // ===> 00 (-> AE)
        _04_orderFlowDuties_validateCart(
                CartRequest.builder().build(),
                BigDecimal.valueOf(1_032_50, 2), BigDecimal.valueOf(11_532_50, 2)
        );
        // ===> AE
        Long countryIdAE = countryRepository.findByIsoCodeAlpha2("AE").getId();
        GroupedCart cartAE = _04_orderFlowDuties_validateCart(
                CartRequest.builder().countryId(countryIdAE).build(),
                BigDecimal.valueOf(1_032_50, 2), BigDecimal.valueOf(11_532_50, 2)
        );
        Assertions.assertThat(cartAE.getGroups().get(0).getDiscount()).isNull();
        GroupedCart cartAEWithCode = _04_orderFlowDuties_validateCart(
                CartRequest.builder().countryId(countryIdAE).sellerId(sellerId).promoCode(promoCodes).build(),
                BigDecimal.valueOf(1_022_25, 2), BigDecimal.valueOf(10_500_00, 2)
        );
        Assertions.assertThat(cartAEWithCode.getGroups().get(0).getDiscount()).isNotNull();
        // ===> SA
        Long countryIdSA = countryRepository.findByIsoCodeAlpha2("SA").getId();
        GroupedCart cartSA = _04_orderFlowDuties_validateCart(
                CartRequest.builder().countryId(countryIdSA).build(),
                BigDecimal.valueOf(3_225_00, 2), BigDecimal.valueOf(13_725_00, 2));
        Assertions.assertThat(cartSA.getGroups().get(0).getDiscount()).isNull();
        GroupedCart cartSAWithCode = _04_orderFlowDuties_validateCart(
                CartRequest.builder().countryId(countryIdSA).sellerId(sellerId).promoCode(promoCodes).build(),
                BigDecimal.valueOf(3_192_75, 2), BigDecimal.valueOf(10_500_00, 2)
        );
        Assertions.assertThat(cartSAWithCode.getGroups().get(0).getDiscount()).isNotNull();
        // ===> QA
        Long countryIdQA = countryRepository.findByIsoCodeAlpha2("QA").getId();
        GroupedCart cartQA = _04_orderFlowDuties_validateCart(
                CartRequest.builder().countryId(countryIdQA).build(),
                BigDecimal.valueOf(500_00, 2), BigDecimal.valueOf(11_000_00, 2));
        Assertions.assertThat(cartQA.getGroups().get(0).getDiscount()).isNull();
        GroupedCart cartQAWithCode = _04_orderFlowDuties_validateCart(
                CartRequest.builder().countryId(countryIdQA).sellerId(sellerId).promoCode(promoCodes).build(),
                BigDecimal.valueOf(495_00, 2), BigDecimal.valueOf(10_500_00, 2)
        );
        Assertions.assertThat(cartQAWithCode.getGroups().get(0).getDiscount()).isNotNull();
    }
    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_orderFlowDuties_setAddressEndpointAggregations() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        Map<Long, Long> productsWithSize = products.stream()
                .collect(Collectors.toMap(Product::getId, it -> it.getAvailableProductItems().get(0).getSize().getId()));
        commitAndStartNewTransaction();
        //
        saveSellerPickupCountryId(usualSellerId, "IT");
        commitAndStartNewTransaction();
        //
        AddressEndpointAggregationDTO aepaSA = saveAddressEndpointAggregation4Buyer("SA");
        commitAndStartNewTransaction();
        AddressEndpointAggregationDTO aepaQA = saveAddressEndpointAggregation4Buyer("QA");
        commitAndStartNewTransaction();
        //
        GroupedCart cartNull = cartTestSupport.cleanCart(true);
        Assertions.assertThat(cartNull).isNull();
        //
        productsWithSize.forEach((productsId, sizeId) -> cartTestSupport.addToCartSuccessful(productsId, sizeId, 1, false));
        //
        GroupedCart cartWithCartModeNullAddr = cartTestSupport.getCart(CartRequest.builder().build(), true);
        Assertions.assertThat(cartWithCartModeNullAddr.getGroups()).hasSize(1)
                .allSatisfy(it -> Assertions.assertThat(it.getDeliveryAddressEndpointAggregation()).isNull());
        //
        GroupedCart cartWithCheckoutModeAddr = cartTestSupport.getCart(CartRequest.builder().checkoutMode(true).build(), true);
        Assertions.assertThat(cartWithCheckoutModeAddr.getGroups()).hasSize(1)
                .allSatisfy(it -> Assertions.assertThat(it.getDeliveryAddressEndpointAggregation()).isNotNull())
                .allSatisfy(it -> Assertions.assertThat(it.getDeliveryAddressEndpointAggregation().getId()).isEqualTo(aepaQA.getId()));
    }

}
