package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.google.common.collect.Lists;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.component.HoldRequest;
import su.reddot.domain.model.banktransaction.BankOperation;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPayment;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.Best2payAndTcbBankService;
import su.reddot.infrastructure.bank.PaymentSystemConfigService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.impl.tcb.response.GetOrderStateResponse;
import su.reddot.infrastructure.bank.payments.noon.NoonBankService;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class OrderFlowAuthorizeExpireTest extends OrderFlowTest {

    @Autowired
    private UserService userService;
    @Autowired
    private PaymentSystemConfigService paymentSystemConfigService;

    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;
    @Autowired
    protected CallInTransaction callInTransaction;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;


    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;

    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private final String SCHEMA_NAMES_UNEXISTING = UUID.randomUUID().toString();
    private final String PAYMENT_TYPE_UNEXISTING = UUID.randomUUID().toString();

    private final Duration duration05DaysMinus2Hours = Duration.ofDays(5).minusHours(2);
    private final Duration duration27DaysMinus2Hours = Duration.ofDays(27).minusHours(2);

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private static Long cardCpPaymentTypeMirActual;
    private static Long cardCpPaymentTypeMirExpire;
    private static Long cardCpPaymentTypeNullValue;
    private static Long cardCpPaymentTypeMirNoDate;
    private static Long cardCpPaymentTypeVisa;
    private static Long cardCpPaymentTypeMaster;


    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private List<Long> prepareBuyerCounterparties() {
        User buyer = userService.getUserByEmail(buyerEmail);
        cardCpPaymentTypeNullValue = orderFlowTestUtils.findOrCreateCardCounterpartyWithCardId(buyer, OrderFlowTestTcbMock.CLIENT_ID_PAYMENT_TYPE_NULL_VALUE.toString());
        cardCpPaymentTypeMirActual = orderFlowTestUtils.findOrCreateCardCounterpartyWithCardId(buyer, OrderFlowTestTcbMock.CLIENT_ID_PAYMENT_TYPE_MIR_ACTUAL.toString());
        cardCpPaymentTypeMirExpire = orderFlowTestUtils.findOrCreateCardCounterpartyWithCardId(buyer, OrderFlowTestTcbMock.CLIENT_ID_PAYMENT_TYPE_MIR_EXPIRE.toString());
        cardCpPaymentTypeMirNoDate = orderFlowTestUtils.findOrCreateCardCounterpartyWithCardId(buyer, OrderFlowTestTcbMock.CLIENT_ID_PAYMENT_TYPE_MIR_NODATE.toString());
        cardCpPaymentTypeVisa = orderFlowTestUtils.findOrCreateCardCounterpartyWithCardId(buyer, OrderFlowTestTcbMock.CLIENT_ID_PAYMENT_TYPE_VISA_OKAY.toString());
        cardCpPaymentTypeMaster = orderFlowTestUtils.findOrCreateCardCounterpartyWithCardId(buyer, OrderFlowTestTcbMock.CLIENT_ID_PAYMENT_TYPE_MASTER_OKAY.toString());
        return Lists.newArrayList(cardCpPaymentTypeNullValue, cardCpPaymentTypeMirActual, cardCpPaymentTypeMirExpire, cardCpPaymentTypeVisa, cardCpPaymentTypeMaster);
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        ApiV2Client apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        callInTransaction.runInNewTransaction(this::prepareBuyerCounterparties);
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
        orderFlowTestTcbMock = null;
    }

    private void _01_paymentSystemConfigService_validateAuthExpireTime(String schema, String paymentType, Duration expectedExpireDuration) {
        LocalDateTime authorizationDateTime = LocalDateTime.now(ZoneOffset.UTC);
        LocalDateTime authorizationExpireDateTime = paymentSystemConfigService.getAuthorizeExpireTime(schema, paymentType, authorizationDateTime);
        if (Objects.isNull(expectedExpireDuration)) {
            Assertions.assertThat(authorizationExpireDateTime).isNull();
            return;
        }
        Duration actualDuration = Duration.between(authorizationDateTime, authorizationExpireDateTime);
        Assertions.assertThat(actualDuration).isPositive();
        Assertions.assertThat(actualDuration).isEqualTo(expectedExpireDuration);
    }

    private void _01_paymentSystemConfigService_validateNullExpireTime(String schema, String paymentType) {
        LocalDateTime authorizationExpireDateTime = paymentSystemConfigService.getAuthorizeExpireTime(schema, paymentType, null);
        Assertions.assertThat(authorizationExpireDateTime).isNull();
    }

    @Test
    public void _01_paymentSystemConfigServiceParamsTest() {
        _01_paymentSystemConfigService_validateAuthExpireTime(TcbBankService.SCHEMA, "VISA", duration05DaysMinus2Hours);
        _01_paymentSystemConfigService_validateAuthExpireTime(TcbBankService.SCHEMA, "MIR", duration27DaysMinus2Hours);
        _01_paymentSystemConfigService_validateAuthExpireTime(TcbBankService.SCHEMA, "MASTER", duration27DaysMinus2Hours);
        _01_paymentSystemConfigService_validateAuthExpireTime(TcbBankService.SCHEMA, null, duration05DaysMinus2Hours);
        _01_paymentSystemConfigService_validateAuthExpireTime(TcbBankService.SCHEMA, PAYMENT_TYPE_UNEXISTING, duration05DaysMinus2Hours);
        //
        _01_paymentSystemConfigService_validateAuthExpireTime(NoonBankService.NOON_SCHEMA, null, null);
        _01_paymentSystemConfigService_validateAuthExpireTime(NoonBankService.NOON_SCHEMA, "VISA", null);
        _01_paymentSystemConfigService_validateAuthExpireTime(NoonBankService.NOON_SCHEMA, PAYMENT_TYPE_UNEXISTING, null);
        //
        _01_paymentSystemConfigService_validateAuthExpireTime(Best2payAndTcbBankService.SCHEMA, null, null);
        _01_paymentSystemConfigService_validateAuthExpireTime(Best2payAndTcbBankService.SCHEMA, "VISA", null);
        _01_paymentSystemConfigService_validateAuthExpireTime(Best2payAndTcbBankService.SCHEMA, PAYMENT_TYPE_UNEXISTING, null);
        //
        _01_paymentSystemConfigService_validateAuthExpireTime(SCHEMA_NAMES_UNEXISTING, null, null);
        _01_paymentSystemConfigService_validateAuthExpireTime(SCHEMA_NAMES_UNEXISTING, "VISA", null);
        _01_paymentSystemConfigService_validateAuthExpireTime(SCHEMA_NAMES_UNEXISTING, PAYMENT_TYPE_UNEXISTING, null);
        //
        _01_paymentSystemConfigService_validateNullExpireTime(SCHEMA_NAMES_UNEXISTING, null);
        _01_paymentSystemConfigService_validateNullExpireTime(SCHEMA_NAMES_UNEXISTING, "VISA");
        _01_paymentSystemConfigService_validateNullExpireTime(SCHEMA_NAMES_UNEXISTING, PAYMENT_TYPE_UNEXISTING);
    }

    private long _02_XX_paymentExpireTest_tcb_payWithSpecifiedClientTcb(Long paymentsCounterpartyId, String paymentType) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(usualSellerId)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        User seller = products.get(0).getSeller();
        //
        cartTestSupport.setCartAddressEndpoint();
        //
        OrderService.InitOrderResult orderHoldInit = cartTestSupport.holdCartWithParams(seller.getId(),
                HoldRequest.builder()
                        .paymentSystem(TcbBankService.SCHEMA)
                        .paymentBuyerCounterpartyId(paymentsCounterpartyId)
                        .build());
        //
        orderFlowTestUtils.callOrderHoldCallback(orderHoldInit.getOrderId(), orderHoldInit.getBank_url().replace("https://", "http://"));
        //
        OrderDTO testOrder = orderFlowTestUtils.loadOrderSuccessfull(orderHoldInit.getOrderId(), true);
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        Assertions.assertThat(orderPayment.getPaymentType()).isEqualTo(paymentType);
        //
        testOrder.getItems().forEach(it ->
                orderFlowTestUtils.rejectOrApprovePosition(it.getId(), true)
        );
        //
        orderFlowTestUtils.changeAddressEndpoint(testOrder.getId(), pickupId, deliveryId);
        //
        orderFlowTestUtils.takeOurselves(testOrder.getId(), null);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        testOrder.getItems().forEach(it ->
                orderFlowTestUtils.adminsApi1expertise(it.getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_PASSED_OK, null)
        );
        //
        return testOrder.getId();
    }

    private void _02_XX_paymentExpireTest_tcb_validatePaymentExpireDatetime(long orderId, String paymentType, Duration expectDuration) {
        ResponseEntity<String> chargeResponse = orderFlowTestUtils.adminPanel_Charge(orderId);
        Assertions.assertThat(chargeResponse.getStatusCode().is2xxSuccessful()).isTrue();
        rollbackAndStartNewTransaction();
        //
        BankOperation authOperation = orderFlowTestUtils.validateBankOperation(orderId, OperationType.HOLD, 10_500_00);
        GetOrderStateResponse authResponse = TcbBankService.readRawTcbResponse(authOperation.getRawResponse());
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(orderId, TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_INPROGRESS);
        Assertions.assertThat(orderPayment.getPaymentType()).isEqualTo(paymentType);
        Assertions.assertThat(orderPayment.getPaymentSystemPaymentId()).isEqualTo(String.valueOf(orderId));
        Assertions.assertThat(orderPayment.getPaymentSystemTransactionId()).isEqualTo(String.valueOf(authResponse.getOrderInfo().getOrderId()));
        Assertions.assertThat(orderPayment.getPaymentSystemOrderId()).isEqualTo(authResponse.getOrderInfo().getExtId());
        Assertions.assertThat(orderPayment.getAuthorizeExpireTime()).isEqualTo(authOperation.getExpireTime());
        //
        Order order = orderFlowTestUtils.validateOrderState(orderId, OrderState.HOLD_COMPLETED);
        Assertions.assertThat(order.getTransactionId()).isEqualTo(String.valueOf(authResponse.getOrderInfo().getOrderId()));
        Assertions.assertThat(order.getAcquirerOrderId()).isEqualTo(authResponse.getOrderInfo().getExtId());
        //
        Duration actualDuration = Duration.between(authOperation.getPaymentSystemTime(), authOperation.getExpireTime());
        Assertions.assertThat(actualDuration.toMinutes()).isEqualTo(expectDuration.toMinutes());
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_01_PaymentExpireTest_Tcb_payWithMIRCaptureOkay() {
        long orderId = _02_XX_paymentExpireTest_tcb_payWithSpecifiedClientTcb(cardCpPaymentTypeMirActual, "MIR");
        _02_XX_paymentExpireTest_tcb_validatePaymentExpireDatetime(orderId, "MIR", Duration.ofDays(27).minusHours(2));
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_02_PaymentExpireTest_Tcb_payWithVisaCaptureOkay() {
        long orderId = _02_XX_paymentExpireTest_tcb_payWithSpecifiedClientTcb(cardCpPaymentTypeVisa, "VISA");
        _02_XX_paymentExpireTest_tcb_validatePaymentExpireDatetime(orderId, "VISA", Duration.ofDays(5).minusHours(2));
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_03_PaymentExpireTest_Tcb_payWithMasterCaptureOkay() {
        long orderId = _02_XX_paymentExpireTest_tcb_payWithSpecifiedClientTcb(cardCpPaymentTypeMaster, "MASTER");
        _02_XX_paymentExpireTest_tcb_validatePaymentExpireDatetime(orderId, "MASTER", Duration.ofDays(27).minusHours(2));
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_04_PaymentExpireTest_Tcb_payWithNullCaptureOkay() {
        long orderId = _02_XX_paymentExpireTest_tcb_payWithSpecifiedClientTcb(cardCpPaymentTypeNullValue, null);
        _02_XX_paymentExpireTest_tcb_validatePaymentExpireDatetime(orderId, null, Duration.ofDays(5).minusHours(2));
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _03_01_PaymentExpireTest_Tcb_payWithMIRExpireCaptureFail() {
        long orderId = _02_XX_paymentExpireTest_tcb_payWithSpecifiedClientTcb(cardCpPaymentTypeMirExpire, "MIR");
        ResponseEntity<String> chargeResponse = orderFlowTestUtils.adminPanel_Charge(orderId);
        Assertions.assertThat(chargeResponse.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownException = orderFlowTestUtils.readExceptionFromText(chargeResponse.getBody());
        Assertions.assertThat(thrownException.getMessage()).matches("Заказ .*: не удается выполнить списание ДС, время удержания ДС истекло \\(.*\\)");
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _03_02_PaymentExpireTest_Tcb_payWithMIRExpireReverseFullOkay() {
        long orderId = _02_XX_paymentExpireTest_tcb_payWithSpecifiedClientTcb(cardCpPaymentTypeMirExpire, "MIR");
        ResponseEntity<String> refundResponse = orderFlowTestUtils.adminPanel_refund(orderId, false);
        Assertions.assertThat(refundResponse.getStatusCode().is2xxSuccessful()).isTrue();
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderId, OrderState.REFUND);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _03_03_PaymentExpireTest_Tcb_payWithMIRNullDateCaptureFail() {
        long orderId = _02_XX_paymentExpireTest_tcb_payWithSpecifiedClientTcb(cardCpPaymentTypeMirNoDate, "MIR");
        ResponseEntity<String> chargeResponse = orderFlowTestUtils.adminPanel_Charge(orderId);
        Assertions.assertThat(chargeResponse.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownException = orderFlowTestUtils.readExceptionFromText(chargeResponse.getBody());
        Assertions.assertThat(thrownException.getMessage()).matches("Заказ .*: в операции авторизации \\(HOLD\\) не указано время удержания ДС");
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _03_04_PaymentExpireTest_Tcb_payWithMIRNullDateReverseFullOkay() {
        long orderId = _02_XX_paymentExpireTest_tcb_payWithSpecifiedClientTcb(cardCpPaymentTypeMirNoDate, "MIR");
        ResponseEntity<String> refundResponse = orderFlowTestUtils.adminPanel_refund(orderId, false);
        Assertions.assertThat(refundResponse.getStatusCode().is2xxSuccessful()).isTrue();
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(orderId, OrderState.REFUND);
    }

}
