package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.api.Assertions;
import org.springframework.context.event.EventListener;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import ru.oskelly.tests.SpringTestWithDb;
import ru.oskelly.tests.TestUtils;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.CartControllerV2Test;
import su.reddot.component.CartTestSupport;
import su.reddot.component.HoldRequest;
import su.reddot.component.TestApiConfiguration;
import su.reddot.domain.dao.AuthorityRepository;
import su.reddot.domain.dao.BrandRepository;
import su.reddot.domain.dao.PayoutRequestRepository;
import su.reddot.domain.dao.discount.PromoCodeRepository;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.dao.product.ProductItemRepository;
import su.reddot.domain.dao.product.ProductRepository;
import su.reddot.domain.model.address.CountryContextNameEnum;
import su.reddot.domain.model.agentreport.AgentReport;
import su.reddot.domain.model.agentreport.AgentReportPayments;
import su.reddot.domain.model.banktransaction.BankOperation;
import su.reddot.domain.model.banktransaction.BankPayment;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.banktransaction.order.OrderBankOperation;
import su.reddot.domain.model.counterparty.CardCounterparty;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.counterparty.CounterpartyType;
import su.reddot.domain.model.currency.CurrencyRate;
import su.reddot.domain.model.discount.AbsolutePromoCode;
import su.reddot.domain.model.discount.FractionalPromoCode;
import su.reddot.domain.model.discount.PromoCode;
import su.reddot.domain.model.duty.DutyCalculatorConfig;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequest;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestType;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderExtraPropInfo;
import su.reddot.domain.model.order.OrderPayment;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderPositionCalcMode;
import su.reddot.domain.model.order.OrderPositionState;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.payoutrequest.PayoutRequestAgentName;
import su.reddot.domain.model.payoutrequest.PayoutRequestStatus;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.ProductItem;
import su.reddot.domain.model.product.ProductState;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.size.SizeType;
import su.reddot.domain.model.user.User;
import su.reddot.domain.model.user.UserAuthorityBinding;
import su.reddot.domain.model.userbalance.UserBalanceChange;
import su.reddot.domain.model.userbalance.UserBalanceChangeMode;
import su.reddot.domain.service.agentreport.ScheduledAgentReportTaskRunner;
import su.reddot.domain.service.bonuses.BonusesService;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.currency.CurrencyRateService;
import su.reddot.domain.service.currency.CurrencyService;
import su.reddot.domain.service.dto.AddressEndpointAggregationRequestDTO;
import su.reddot.domain.service.dto.BankOperationDTO;
import su.reddot.domain.service.dto.BankPaymentDTO;
import su.reddot.domain.service.dto.CurrencyDTO;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.ProductsSalesRequest;
import su.reddot.domain.service.dto.order.DocumentLinkDTO;
import su.reddot.domain.service.dto.order.ExpertiseDTO;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.dto.order.OrderSourceDTO;
import su.reddot.domain.service.dto.order.PaymentDTO;
import su.reddot.domain.service.dto.order.adminpanel.AdminPanelOrderDetailsDTO;
import su.reddot.domain.service.dto.order.adminpanel.AdminPanelSplitOrderResponseDTO;
import su.reddot.domain.service.dto.order.adminpanel.OrderStateDTO;
import su.reddot.domain.service.dto.userbalance.UserBalanceChangeParams;
import su.reddot.domain.service.fiscalreceiptrequest.FiscalReceiptRequestService;
import su.reddot.domain.service.notification.OrderHoldEvent;
import su.reddot.domain.service.order.ChangeOrderEvent;
import su.reddot.domain.service.order.OrderDocumentGenerator;
import su.reddot.domain.service.order.OrderPaymentService;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.order.documents.AgentReportRuDocumentGenerator;
import su.reddot.domain.service.order.impl.OrderExtraPropsService;
import su.reddot.domain.service.product.item.ProductItemService;
import su.reddot.domain.service.productpublication.ProductPublicationService;
import su.reddot.domain.service.size.SizeService;
import su.reddot.domain.service.user.UserService;
import su.reddot.domain.service.userbalance.UserBalanceService;
import su.reddot.infrastructure.bank.BoutiqueBankService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.jobs.AgentPaymentJobs;
import su.reddot.infrastructure.bank.jobs.ScheduledBankRunner;
import su.reddot.infrastructure.bank.payments.noon.NoonBankService;
import su.reddot.infrastructure.bank.payments.oskelly.PaymentsServiceBankService;
import su.reddot.infrastructure.cashregister.Checkable;
import su.reddot.infrastructure.cashregister.impl.starrys.type.BuyerCheckRequest;
import su.reddot.infrastructure.cashregister.impl.starrys.type.Line;
import su.reddot.infrastructure.configparam.ConfigParamService;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.test.TestApiPortHandler;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.infrastructure.util.Utils;
import su.reddot.presentation.adminpanel.AdminOrdersController;
import su.reddot.presentation.api.v2.Api2Response;
import su.reddot.presentation.api.v2.cart.CartRequest;
import su.reddot.presentation.api.v2.order.OrderSetOrderLegalEntityRequest;
import su.reddot.presentation.api.v2.order.OrderSetOrderSourceInfoRequest;
import su.reddot.presentation.api.v2.order.OrderSplitRequest;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.Clock;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrderFlowTestUtils {

    private final Clock clock;

    private final ConfigParamService configParamService;

    private final CartTestSupport cartTestSupport;
    private final JdbcTemplate jdbcTemplate;

    private final UserService userService;
    private final ProductRepository productRepository;
    private final ProductItemRepository productItemRepository;
    private final AuthorityRepository authorityRepository;
    private final PromoCodeRepository promoCodeRepository;
    private final FiscalReceiptRequestService fiscalReceiptRequestService;
    private final UserBalanceService userBalanceService;
    private final ProductPublicationService productPublicationService;
    private final CurrencyService currencyService;
    private final CounterpartyService counterpartyService;
    private final BrandRepository brandRepository;
    private final ProductItemService productItemService;
    private final SizeService sizeService;
    private final CurrencyRateService currencyRateService;
    private final OrderPaymentService orderPaymentService;
    private final PaymentsServiceBankService paymentsServiceBankService;
    private final OrderExtraPropsService orderExtraPropsService;
    private final PayoutRequestRepository payoutRequestRepository;

    private final OrderService orderService;
    private final OrderRepository orderRepository;

    private final TestApiConfiguration testApiConfiguration;

    private final AgentPaymentJobs agentPaymentJobs;
    private final ScheduledBankRunner scheduledBankRunner;
    private final ScheduledAgentReportTaskRunner scheduledAgentReportTaskRunner;

    private final TestApiPortHandler testApiPortHandler;

    private ApiV2Client apiV2Client;

    @Getter
    private OskellyDeliveryInfo defaultOskellyDeliveryInfoS2O;
    @Getter
    private OskellyDeliveryInfo defaultOskellyDeliveryInfoO2B;

    private final ObjectMapper objectMapper;
    private final CallInTransaction callInTransaction;
    private final BonusesService bonusesService;

    private final AtomicLong orderEventLock = new AtomicLong();

    private final Map<String, String> paymentsSchemeToPayoutBankName = ImmutableMap.<String, String>builder()
            .put(TcbBankService.SCHEMA, TcbBankService.TCB_BANK_NAME)
            .put(BoutiqueBankService.BOUTIQUE_SCHEMA, BoutiqueBankService.BOUTIQUE_BANK_NAME)
            .put(NoonBankService.NOON_SCHEMA, NoonBankService.NOON_BANK_NAME)
            .build();

    public void init(String userMail, String password) {
        apiV2Client = new ApiV2Client(userMail, password, objectMapper);
        defaultOskellyDeliveryInfoS2O = OskellyDeliveryInfo.builder()
                .courierName("S2O-Courier").courierPhone("**********").courierDate(LocalDate.now(clock))
                .build();
        defaultOskellyDeliveryInfoO2B = OskellyDeliveryInfo.builder()
                .courierName("O2B-Courier").courierPhone("**********").courierDate(LocalDate.now(clock))
                .build();
    }

    @EventListener
    public void onOrderChanged(@NonNull ChangeOrderEvent event) {
        synchronized (orderEventLock) {
            if (orderEventLock.get() == event.getId()) {
                orderEventLock.notifyAll();
            }
        }
    }

    @EventListener
    public void onOrderHold(@NonNull OrderHoldEvent event) {
        synchronized (orderEventLock) {
            if (orderEventLock.get() == event.getOrderId()) {
                orderEventLock.notifyAll();
            }
        }
    }

    @SneakyThrows
    private void waitOrderChanges(long orderId, long timeout) {
        synchronized (orderEventLock) {
            orderEventLock.set(orderId);
            orderEventLock.wait(timeout);
        }
    }

    public void setTcbOrdersCount(int tcbOrdersCount) {
        String paramValue = String.valueOf(tcbOrdersCount);
        configParamService.setValueAsString(ConfigParamService.CONFIG_PARAM_ALLOW_TCB_ORDERS_USUAL_COUNT, paramValue);
        configParamService.setValueAsString(ConfigParamService.CONFIG_PARAM_ALLOW_TCB_ORDERS_AGENT_COUNT, paramValue);
        String ordersHeld = "100";
        configParamService.setValueAsString(ConfigParamService.CONFIG_PARAM_HOLD_ORDER_ORDERS_COUNTER, ordersHeld);
    }

    public void allowCardPayoutForUser(Long userId) {
        String paramValue = configParamService.getValueAsString(ConfigParamService.CONFIG_PARAM_PAYOUTS_CARD_PAYOUTS_USERS);
        Collection<String> userList = Arrays.stream(StringUtils.split(paramValue, ",")).collect(Collectors.toCollection(HashSet::new));
        if (userList.contains(userId.toString())) {
            return;
        }
        userList.add(userId.toString());
        configParamService.setValueAsString(ConfigParamService.CONFIG_PARAM_PAYOUTS_CARD_PAYOUTS_USERS, StringUtils.join(userList, ","));
    }

    public void setAllowPaymentSystemChoose(Collection<String> paymentSystemsList) {
        configParamService.setValueAsString(ConfigParamService.CONFIG_PARAM_ALLOW_CHOOSE_ORDER_PAYMENT_SYSTEM, String.join(",", paymentSystemsList));
    }

    public void switchMarkingCode4AllSellers(boolean isMarkingCodeEnabled) {
        configParamService.setValueAsString(ConfigParamService.CONFIG_PARAM_ENABLE_MARKING_CODE_USER_LIST, isMarkingCodeEnabled ? "ALL" : "");
    }

    public List<Product> getProductsForOrdersWithSeller(Long sellerId) {
        return getProductsForOrdersWithSeller(FindProduct4Test.builder()
                .sellerId(sellerId)
                .build()
        );
    }

    public List<Product> getProductsForOrdersWithSeller(FindProduct4Test findProduct4Test) {
        log.info("getProductsForOrdersWithSeller: init ({})", findProduct4Test);
        List<Product> resultList = getProductsForOrdersWithSellerEx(findProduct4Test);
        log.info("getProductsForOrdersWithSeller: done");
        return resultList;
    }

    private List<Product> getProductsForOrdersWithSellerEx(FindProduct4Test findProduct4Test) {
        User sellerUser = userService.getOne(findProduct4Test.getSellerId());

        List<Product> result = new ArrayList<>();
        List<Product> products = productRepository.findProductsBySellerIdAndProductState(sellerUser.getId(), ProductState.PUBLISHED).stream()
                .filter(product -> product.getProductItems().size() == 1).collect(Collectors.toList());
        for (Product product : products) {
            boolean isInBoutique = product.getProductItems().stream()
                    .anyMatch(pi -> !orderRepository.findActiveBoutiqueOrderIdsByProductItem(pi.getId()).isEmpty());
            boolean skipConcierge = product.isSelectedForConcierge() && !findProduct4Test.isForConcierge();
            boolean isSomeCurrency = Objects.nonNull(product.getCurrentPriceCurrencyId());
            if (isInBoutique || skipConcierge || isSomeCurrency) {
                continue;
            }
            BigDecimal productPrice = new BigDecimal((result.size() + 1) * 10000); // 10.000 - 20.000 - 30.000 - ...
            product.setSalesChannel(findProduct4Test.isForBoutique() ? SalesChannel.BOUTIQUE_AND_WEBSITE : SalesChannel.WEBSITE);
            product.setCurrentPrice(productPrice);
            productRepository.saveAndFlush(product);
            product.getProductItems().forEach(pi -> {
                pi.setHidden(false);
                pi.setCount(10000);
                pi.setDeleteTime(null);
                pi.setCurrentPrice(productPrice);
                pi.setCurrentPriceWithoutCommission(product.getCurrentPriceWithoutCommission());
                pi.setCustomCommission(product.isCustomCommission());
                pi.setCurrentPriceCurrencyId(product.getCurrentPriceCurrencyId());
                productItemRepository.saveAndFlush(pi);
            });
            result.add(product);
            int maxItems = findProduct4Test.getMaxItems() == 0 ? 32 : findProduct4Test.getMaxItems();
            if (result.size() >= maxItems) {
                break;
            }
        }
        return result;
    }

    public List<Product> getProductsForOrdersWithSeller(Long sellerId, String currencyCode, long itemsCount) {
        List<Product> allSellerProducts = getProductsForOrdersWithSeller(sellerId);
        //
        CurrencyDTO currency = currencyService.getCurrencyDTOByCodeCached(currencyCode);
        //
        List<Product> sellerProductsWithThisCurrency = allSellerProducts.stream()
                .filter(p -> Objects.equals(p.getCurrentPriceCurrencyId(), currency.getId()))
                .collect(Collectors.toList());
        if (sellerProductsWithThisCurrency.size() >= itemsCount) {
            return sellerProductsWithThisCurrency;
        }
        //
        List<Product> sellerProductsWithNullCurrency = allSellerProducts.stream()
                .filter(p -> Objects.isNull(p.getCurrentPriceCurrencyId()))
                .collect(Collectors.toList());
        //
        if ((sellerProductsWithNullCurrency.size() + sellerProductsWithThisCurrency.size()) < itemsCount) {
            throw new IllegalStateException(String.format("Unable to find %d products from seller %d with currencyCode %s",
                    itemsCount, sellerId, currencyCode));
        }
        //
        for (Product p : sellerProductsWithNullCurrency) {
            setProductPrice(p, currencyCode, BigDecimal.valueOf(10_000_00, 2));
            sellerProductsWithThisCurrency.add(p);
            if (sellerProductsWithThisCurrency.size() >= itemsCount) {
                return sellerProductsWithThisCurrency;
            }
        }
        throw new IllegalStateException(String.format("Unable to find %d products from seller %d with currencyCode %s",
                itemsCount, sellerId, currencyCode));
    }


    public void setProductPrice(Product product, String currencyCode, BigDecimal currencyValue) {
        productPublicationService.setPriceInCurrency(product,
                currencyService.getCurrencyDTOByCodeCached(currencyCode).getId(),
                currencyValue,
                product.getProductState());
    }

    public GroupedCart fillCart(List<Product> productList) {
        return fillCartWithCurrencyCode(
                productList.stream().map(Product::getId).collect(Collectors.toList()),
                null);
    }

    public GroupedCart fillCartWithCurrencyCode(List<Long> productIdsList, String currencyCode) {
        List<Product> productList = productIdsList.stream()
                .map(productRepository::getOne)
                .collect(Collectors.toList());
        List<CartControllerV2Test.CartAddRequest> cartItems = productList.stream()
                .map(p -> new CartControllerV2Test.CartAddRequest()
                        .setProductId(p.getId())
                        .setSizeId(p.getAvailableProductItems().get(0).getSize().getId())
                        .setCurrencyCode(currencyCode)
                        .setCount(1)).collect(Collectors.toList());
        return fillCartWithList(cartItems);
    }

    public GroupedCart getCart(CartRequest cartRequest) {
        return cartTestSupport.getCart(cartRequest, true);
    }

    public GroupedCart fillCartWithList(List<CartControllerV2Test.CartAddRequest> cartItems) {
        GroupedCart cartInfo = null;
        cartTestSupport.cleanCart(true);
        for (int i = 0; i < cartItems.size(); i++) {
            cartInfo = cartTestSupport.addToCartWithParamsSuccessful(cartItems.get(i), true);
            assertEquals(i + 1, cartInfo.getSize());
        }
        return cartInfo;
    }

    public void fillCartWithFakeCurrencyCode(List<Product> productList, String currencyCode) {
        cartTestSupport.cleanCart(true);
        for (Product product : productList) {
            CartControllerV2Test.CartAddRequest addRequest = new CartControllerV2Test.CartAddRequest()
                    .setProductId(product.getId())
                    .setSizeId(product.getAvailableProductItems().get(0).getSize().getId())
                    .setCurrencyCode(currencyCode)
                    .setCount(1);
            cartTestSupport.addToCartWithParamsUnsuccessful4xx(addRequest, true);
        }
    }

    private String getOrderUrl(long orderId) {
        return testApiConfiguration.getServerUrl() + "/api/v2/orders/" + orderId;
    }

    public String createOrderWithSaleRequestUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v2/cart/createOrderWithSaleRequest";
    }

    private String getOrderClientDocumentsUrl(long orderId) {
        return testApiConfiguration.getServerUrl() + "/api/v2/orders/" + orderId + "/documents";
    }

    private String getOrderAdminsDocumentsUrl(long orderId) {
        return testApiConfiguration.getServerUrl() + "/api/v2/admin/orders/" + orderId + "/docs";
    }

    private String getApiV2AdminOrdersIdsUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v2/admin/orders/ids";
    }

    private String getApiV2AdminBankOperationsUrl(long orderId) {
        return testApiConfiguration.getServerUrl() + "/api/v2/admin/orders/" + orderId + "/bankoperations";
    }

    private String getOrderPositionConfirmUrl(long orderId) {
        return testApiConfiguration.getServerUrl() + "/api/v2/orders/" + orderId + "/confirmPosition";
    }

    private String getAdminOrderUrl(long orderId) {
        return testApiConfiguration.getServerUrl() + "/api/v2/admin/orders/" + orderId;
    }

    private String getAdminV1OrderInfoUrl(long orderId) {
        return testApiConfiguration.getServerUrl() + "/adminpanel/orders/info?orderId=" + orderId;
    }

    private String getSplitOrderByOrderPostionsUrl() {
        return testApiConfiguration.getServerUrl() + "/adminpanel/orders/split-order-by-order-positions";
    }

    private String getProductUrl(long productId) {
        return testApiConfiguration.getServerUrl() + "/api/v2/catalog/products/" + productId;
    }

    private String getCartDeliveryAddressEndpointUrl() {
        return testApiConfiguration.getServerUrl() + "/api/v2/cart/deliveryAddressEndpoint";
    }

    private String getAgentReportPdfUrl(long agentReportId) {
        return testApiConfiguration.getServerUrl() + "/api/v2/agentreports/pdf/" + agentReportId;
    }

    private String getOrderDocumentUrl(long ordersId, String documentname, String format) {
        return testApiConfiguration.getServerUrl() + "api/v2/orders/" + ordersId + "/documents/" + documentname + "." + format;
    }

    public OrderDTO loadOrderSuccessfullWithCustomClient(ApiV2Client apiV2Client, long orderId, boolean withAuthoriseParams) {
        ResponseEntity<Api2Response<OrderDTO>> responseEntity = apiV2Client.request(getOrderUrl(orderId), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<OrderDTO>>() {}, withAuthoriseParams);
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        assertNotNull(responseEntity.getBody());
        assertNotNull(responseEntity.getBody().getData());
        return responseEntity.getBody().getData();
    }

    public OrderDTO createOrderFromApplicationSuccessfullWithCustomClient(ApiV2Client apiV2Client, ProductsSalesRequest productsSalesRequest, boolean withAuthoriseParams) {
        ResponseEntity<Api2Response<OrderDTO>> responseEntity = apiV2Client.request(createOrderWithSaleRequestUrl(), null, HttpMethod.POST, MediaType.APPLICATION_JSON, productsSalesRequest, new ParameterizedTypeReference<Api2Response<OrderDTO>>() {}, withAuthoriseParams);
        return responseEntity.getBody().getData();
    }

    public OrderDTO loadOrderSuccessfull(long orderId, boolean withAuthoriseParams) {
        return loadOrderSuccessfullWithCustomClient(apiV2Client, orderId, withAuthoriseParams);
    }

    public AdminPanelOrderDetailsDTO loadAdminOrderSuccessful(ApiV2Client apiV2Client, long orderId, boolean withChildren) {
        Map<String, String> getParams = new HashMap<>();
        getParams.put("withChildren", String.valueOf(withChildren));
        //
        ResponseEntity<Api2Response<AdminPanelOrderDetailsDTO>> responseEntity = Optional.ofNullable(apiV2Client).orElse(apiV2Client).request(getAdminOrderUrl(orderId),
                getParams,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<Api2Response<AdminPanelOrderDetailsDTO>>() {},
                true);
        //
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        assertNotNull(responseEntity.getBody());
        assertNotNull(responseEntity.getBody().getData());
        //
        return responseEntity.getBody().getData();
    }

    public ResponseEntity<AdminPanelSplitOrderResponseDTO> splitOrderByOrderPositions(OrderSplitRequest orderSplitRequest) {
        ResponseEntity<AdminPanelSplitOrderResponseDTO> responseEntity = apiV2Client.request(getSplitOrderByOrderPostionsUrl(),
                null,
                HttpMethod.POST,
                MediaType.APPLICATION_JSON,
                orderSplitRequest,
                AdminPanelSplitOrderResponseDTO.class,
                true);
        return responseEntity;
    }

    public void checkAdminV1OrderInfo(long orderId) {
        String response = apiV2Client.request(getAdminV1OrderInfoUrl(orderId),
                null,
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<String>() {},
                true).getBody();
        assertNotNull(response);
        assertFalse(response.contains("Администрирование | Ошибка"));
    }

    public OrderService.InitOrderResult holdOrderWithPromoCodePS(User seller, String promoCode, String paymentSystem) {
        OrderService.InitOrderResult holdResult = cartTestSupport.holdWithSetAddressEndpointPCnCC(CartTestSupport.HOLD_V2_ENDPOINT, seller.getId(),
                promoCode, paymentSystem);
        Long orderId = holdResult.getOrderId();
        loadOrderSuccessfull(orderId, true);
        return holdResult;
    }

    public Order validateOrderState(long orderId, OrderState state) {
        Order order = orderService.getOrder(orderId);
        Assertions.assertThat(order.getState()).isEqualTo(state);
        return order;
    }

    public Order validateOrderDeliveryState(long orderId, DeliveryState deliveryState) {
        Order order = orderService.getOrder(orderId);
        Assertions.assertThat(order.getDeliveryState()).isEqualTo(deliveryState);
        return order;
    }

    public List<Long> getOrdersPayoutUpdateDoneList(SpringTestWithDb testCase, boolean cleanValue) {
        List<Long> result = configParamService.getValueAsListOfLong(ConfigParamService.CONFIG_PARAM_ORDERS_PAYOUT_UPDATE_DONE_LIST);
        if (!cleanValue) {
            return result;
        }
        configParamService.setValueAsString(ConfigParamService.CONFIG_PARAM_ORDERS_PAYOUT_UPDATE_DONE_LIST, "0");
        testCase.commitAndStartNewTransaction();
        //
        return result;
    }

    public void validateAgentReportText(long orderId, String detailsRegEx) {
        AgentReport agentReport = orderService.getOrder(orderId).getAgentReport();
        Assertions.assertThat(agentReport.getPaymentDetails()).matches(detailsRegEx);
    }

    public AgentReport validateAgentReportAmnt(long orderId, String currencyCode, BigDecimal baseAmount) {
        AgentReport agentReport = orderService.getOrder(orderId).getAgentReport();
        Assertions.assertThat(agentReport.getOriginalCurrencyAmount()).isEqualByComparingTo(baseAmount);
        Assertions.assertThat(agentReport.getOriginalCurrency().getIsoCode()).isEqualTo(currencyCode);
        return agentReport;
    }

    private BankOperation validateUsualOperation(Order order, BankOperationDTO bankOperationDTO) {
        BigDecimal dtoOpsAmount = bankOperationDTO.getOperationType() == OperationType.SELLER_PAYOUT
                ? bankOperationDTO.getAmount().setScale(2, RoundingMode.HALF_UP) // TODO: GetRidOfIt
                : bankOperationDTO.getAmount();
        Optional<OrderBankOperation> bankOperation = order.getBankOperations().stream()
                .filter(it -> it.getOperationType() == bankOperationDTO.getOperationType())
                .filter(it -> BigDecimal.valueOf(it.getApiSentAmount(), 2).compareTo(dtoOpsAmount) == 0)
                .findAny();
        Assertions.assertThat(bankOperation).isPresent();
        Assertions.assertThat(bankOperation.get().getCurrency()).isNotNull();
        return bankOperation.orElse(null);
    }

    private BankOperation validatePayoutOperation(Order order, BankOperationDTO bankOperationDTO) {
        long payoutCurrencyId = bankOperationDTO.getCurrency().getId();
        long systemCurrencyId = currencyService.getBaseCurrency().getId();
        if (payoutCurrencyId == systemCurrencyId){
            return validateUsualOperation(order, bankOperationDTO);
        }
        if (!orderPaymentService.getPaymentService(order.getPaymentVersion()).isPayoutInBaseCurrencyOnly()) {
            return validateUsualOperation(order, bankOperationDTO);
        }
        Optional<OrderBankOperation> bankOperation = order.getBankOperations().stream()
                .filter(it -> it.getOperationType() == bankOperationDTO.getOperationType())
                .filter(it -> BigDecimal.valueOf(it.getApiSentAmount(), 2).compareTo(bankOperationDTO.getAmountInBase()) == 0)
                .findAny();
        Assertions.assertThat(bankOperation).isPresent();
        Assertions.assertThat(bankOperation.get().getCurrency()).isNotNull();
        Assertions.assertThat(bankOperation.get().getCurrency().getId()).isEqualTo(systemCurrencyId);
        return bankOperation.orElse(null);
    }

    public BankOperation validateBankOperation(long orderId, OperationType operationType, BigDecimal validateAmount) {
        Order order = orderService.getOrder(orderId);
        //
        List<BankOperationDTO> remoteOpList = getApiV2AdminBankOperations(orderId).getData();
        List<BankOperationDTO> opWithAmount = remoteOpList.stream()
                .filter(it -> it.getOperationType() == operationType)
                .collect(Collectors.toList());
        Optional<BankOperationDTO> remoteOperation = opWithAmount.size() == 1
                ? opWithAmount.stream().findAny()
                : opWithAmount.stream().filter(it -> it.getAmount().compareTo(validateAmount) == 0).findAny();
        Assertions.assertThat(remoteOperation).hasValueSatisfying(it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(validateAmount);
            Assertions.assertThat(it.getCurrency()).isNotNull();
        });
        //
        BankOperation localOperation = (operationType == OperationType.SELLER_PAYOUT)
                ? validatePayoutOperation(order, remoteOperation.orElse(null))
                : validateUsualOperation(order, remoteOperation.orElse(null));
        //
        return localOperation;
    }

    public BankOperation validateBankOperation(long orderId, OperationType operationType, long amountInCents) {
        return validateBankOperation(orderId, operationType, BigDecimal.valueOf(amountInCents, 2));
    }

    private boolean isPaymentService(String paymentVersion) {
        return paymentsServiceBankService.getSupportedPaymentVersions().contains(paymentVersion);
    }

    public void validateBankOperationTypeList(long orderId, List<OperationType> operationTypeList) {
        Order order = orderService.getOrder(orderId);
        //
        List<OperationType> remoteTypeList = getApiV2AdminBankOperations(orderId).getData().stream()
                .map(BankOperationDTO::getOperationType)
                .collect(Collectors.toList());
        //
        Assertions.assertThat(operationTypeList).isEqualTo(remoteTypeList);
        if (isPaymentService(order.getPaymentVersion())) {
            return;
        }
        List<OperationType> actualTypeList = order.getBankOperations().stream()
                .sorted(Comparator.comparing(OrderBankOperation::getId))
                .map(BankOperation::getOperationType)
                .collect(Collectors.toList());
        Assertions.assertThat(operationTypeList).isEqualTo(actualTypeList);
    }

    public OrderPayment validateOrderPayment(long orderId, String paymentVersion, OrderPaymentState paymentState) {
        Order order = orderService.getOrder(orderId);
        //
        Assertions.assertThat(order.getOrderPayments()).hasSize(1);
        //
        Optional<OrderPayment> orderPayment = order.getOrderPayments().stream()
                .filter(op -> op.getPaymentVersion().equals(paymentVersion))
                .collect(Collectors.reducing((a, b) -> null));
        Assertions.assertThat(orderPayment).isPresent();
        //
        Assertions.assertThat(orderPayment.get().getState()).isEqualTo(paymentState);
        //
        return orderPayment.get();
    }

    public Api2Response<String> getRawApi2Response(String rawData) {
        try {
            return objectMapper.readValue(rawData, new TypeReference<Api2Response<String>>() {});
        } catch (IOException e) {
            return null;
        }
    }

    public ResponseEntity<String> convertHoldToPrepayment(long orderId, HttpStatus.Series expectCode) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/convert-hold-to-prepayment/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().series()).isEqualTo(expectCode);
        return responseEntity;
    }

    public void enableUserAuthority(long userId, AuthorityName authorityName, boolean state) { // TODO: move this to userAuthorityService? Or to userService
        User user = userService.getOne(userId);
        boolean hasRight = user.getUserAuthorityBindings().stream().anyMatch(ab -> authorityName.name().equals(ab.getAuthority().getName().name()));
        if (hasRight && state == true) {
            return;
        }
        UserAuthorityBinding authorityBinding = new UserAuthorityBinding();
        authorityBinding.setUser(user);
        authorityBinding.setAuthority(authorityRepository.findByName(authorityName));
        if (hasRight && state == false) {
            UserAuthorityBinding userAuthorityBinding = user.getUserAuthorityBindings()
                    .stream().filter(ab -> ab.getAuthority() == authorityBinding.getAuthority())
                    .collect(Collectors.toList()).get(0);
            user.getUserAuthorityBindings().remove(userAuthorityBinding);
        } else {
            user.getUserAuthorityBindings().add(authorityBinding);
        }
        userService.save(user);
    }

    public void enableLoyalityProgram(User user, boolean isEnable) {
        user.setIsLoyaltyProgramAccepted(isEnable);
        userService.save(user);
    }

    public User saveUser(long userId, Consumer<User> fillInfo) {
        User editUser = userService.getOne(userId);
        fillInfo.accept(editUser);
        userService.save(editUser);
        //
        return editUser;
    }

    public void enableCountryContext(String countryIsoCode, CountryContextNameEnum contextName) {
        StringBuilder sqlQuery = new StringBuilder();
        sqlQuery.append("insert into country_context_relation(country_id, country_context_id)")
                .append("values (")
                .append(String.format("(select id from country where iso_code_alpha2 = '%s'),", countryIsoCode))
                .append(String.format("(select id from country_context where \"name\" = '%s')", contextName.name()))
                .append(") on conflict do nothing;");
        jdbcTemplate.execute(sqlQuery.toString());
    }

    public void enableDutyCalculator(DutyCalculatorConfig.DType calculatorType, boolean enable) {
        jdbcTemplate.update("UPDATE duty_calculator_config SET is_active = ? WHERE dtype = ?", enable, calculatorType.name());
    }

    @SneakyThrows
    public ResponseEntity<String> callOrderHoldCallback(long orderId, String callbackPath) {
        String urlReplace = testApiPortHandler.replacePortInUrl(callbackPath);
        //
        CompletableFuture<ResponseEntity<String>> asyncResult = CompletableFuture.supplyAsync(() ->
                apiV2Client.request(urlReplace, null, HttpMethod.GET, null, new ParameterizedTypeReference<String>() {}, false)
        );
        //
        waitOrderChanges(orderId, 5000);
        //
        return asyncResult.get();
    }


    private OrderPaymentState getPaymentState(long ordersId) {
        Order order = orderService.getOrder(ordersId);
        OrderPayment orderPayment = orderPaymentService.findOrderPaymentOrFail(order);
        return orderPayment.getState();
    }

    @SneakyThrows
    private void processPsPayment(long ordersId, OrderPaymentState waitsState) {
        long waitsMs = 2 * 60 * 1000;
        long sleepMs = 5000;
        //
        Order order = orderService.getOrder(ordersId);
        OrderPayment orderPayment = orderPaymentService.findOrderPaymentOrFail(order);
        for (int i = 0; i < waitsMs / sleepMs; i++) {
            OrderPaymentState paymentState = callInTransaction.runInNewTransaction(() -> getPaymentState(ordersId));
            if (paymentState == waitsState) {
                return;
            }
            paymentsServiceBankService.validateActiveOperations(orderPayment.getPaymentId());
            Thread.sleep(sleepMs);
        }
        paymentsServiceBankService.validatePaymentServicePayment(orderPayment.getPaymentId(), false);
    }

    public void rejectOrApprovePosition(long orderPositionId, boolean approve) {
        String callPath = testApiConfiguration.getServerUrl() + (approve ? "/adminpanel/orders/position/approve" : "/adminpanel/orders/position/decline");
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                TestUtils.getOneParamAsMap("positionId", orderPositionId),
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderPositionId));
    }

    public ResponseEntity<String> approvePosition(long orderPositionId) {
        String callPath = testApiConfiguration.getServerUrl() + ("/adminpanel/orders/position/approve");
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                TestUtils.getOneParamAsMap("positionId", orderPositionId),
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        return responseEntity;
    }

    public ResponseEntity<String> adminPanel_setOrderPositionMarkingCode(long orderPositionId, String markingCode, HttpStatus.Series returnSeries) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/position/datamatrix";
        Map<String, String> getParams = new HashMap<>();
        getParams.put("positionId", String.valueOf(orderPositionId));
        getParams.put("datamatrix", markingCode);
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                getParams,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, false);
        Assertions.assertThat(responseEntity.getStatusCode().series()).isEqualTo(returnSeries);
        if (!returnSeries.equals(HttpStatus.Series.SUCCESSFUL)) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderPositionId));
        return responseEntity;
    }

    public ResponseEntity<String> saveAddressEndpointAggregation(AddressEndpointAggregationRequestDTO addressEndpointAggregationRequestDTO, HttpStatus.Series expectSeries) {
        String callPath = testApiConfiguration.getServerUrl() + "/api/v2/account/addresses";
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath, null, HttpMethod.POST,
                addressEndpointAggregationRequestDTO, new ParameterizedTypeReference<String>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().series()).isEqualTo(expectSeries);
        return responseEntity;
    }

    public ResponseEntity<String> changeSellerCounterparty(long orderId, long counterpartyId, HttpStatus.Series expectCode) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/change-seller-counterparty/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                TestUtils.getOneParamAsMap("sellerCounterpartyId", counterpartyId),
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().series()).isEqualTo(expectCode);
        if (expectCode == HttpStatus.Series.SUCCESSFUL) {
            Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        }
        return responseEntity;
    }

    public void changeTargetAddressEndpointAggregationCart(long addressEndpointAggregationId) {
        String callPath = testApiConfiguration.getServerUrl() + "/api/v2/cart/deliveryAddressEndpointAggregation";
        //
        Map<String, String> putParams = new HashMap<>();
        putParams.put("deliveryAddressEndpointAggregationId", String.valueOf(addressEndpointAggregationId));
        //
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath, putParams, HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        //
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
    }

    public void changeTargetAddressEndpointAggregation(long orderId, Long addressEndpointAggregationId) {
        String callPath = testApiConfiguration.getServerUrl() + "/api/v2/orders/" + orderId + "/deliveryAddressEndpointAggregation/" + addressEndpointAggregationId;
        //
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath, null, HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        //
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
    }

    public void changeAddressEndpoint(long orderId, Long pickUpId, Long deliveryId) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/change-address-endpoint/" + orderId;
        //
        Map<String, String> putParams = new HashMap<>();
        putParams.put("pickupAddressEndpointId", String.valueOf(pickUpId));
        if (deliveryId != null) {
            putParams.put("deliveryAddressEndpointId", String.valueOf(deliveryId));
        }
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                putParams,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        //
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
    }

    public ResponseEntity<String> takeOurselves(long orderId, OskellyDeliveryInfo oskellyDeliveryInfo) {
        Map<String, String> getParams = new HashMap<>();
        OskellyDeliveryInfo deliveryInfo = Objects.isNull(oskellyDeliveryInfo) ? defaultOskellyDeliveryInfoS2O : oskellyDeliveryInfo;
        getParams.put("courierName", deliveryInfo.getCourierName());
        getParams.put("courierPhone", deliveryInfo.getCourierPhone());
        getParams.put("courierDate", String.valueOf(deliveryInfo.getCourierDate().atStartOfDay().toEpochSecond(ZoneOffset.UTC)));

        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/send-confirmed-order/ourselves/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                getParams,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        boolean callWillFail = Optional.ofNullable(oskellyDeliveryInfo).map(OskellyDeliveryInfo::isCallWillFail).orElse(false);
        if (callWillFail) {
            return responseEntity;
        }
        //
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        //
        return responseEntity;
    }

    public ResponseEntity<String> takeOurselves(long orderId, OskellyDeliveryInfo oskellyDeliveryInfo, String email, String password) {
        ApiV2Client apiV2Client = new ApiV2Client(email, password);
        User user = userService.getUserByEmail(email);
        cartTestSupport.setUserId(user.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();

        Map<String, String> getParams = new HashMap<>();
        cartTestSupport.cleanCart(true);
        OskellyDeliveryInfo deliveryInfo = Objects.isNull(oskellyDeliveryInfo) ? defaultOskellyDeliveryInfoS2O : oskellyDeliveryInfo;
        getParams.put("courierName", deliveryInfo.getCourierName());
        getParams.put("courierPhone", deliveryInfo.getCourierPhone());
        getParams.put("courierDate", String.valueOf(deliveryInfo.getCourierDate().atStartOfDay().toEpochSecond(ZoneOffset.UTC)));

        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/send-confirmed-order/ourselves/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                getParams,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        boolean callWillFail = Optional.ofNullable(oskellyDeliveryInfo).map(OskellyDeliveryInfo::isCallWillFail).orElse(false);
        if (callWillFail) {
            return responseEntity;
        }
        //
        return responseEntity;
    }

    public ResponseEntity<String> changeDeliveryState(long orderId, DeliveryState deliveryState, boolean isWait200okay) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/delivery/changestate/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                TestUtils.getOneParamAsMap("state", deliveryState.name()),
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        if (!isWait200okay) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        return responseEntity;
    }

    public ResponseEntity<String> expertiseRawResponse(long orderPositionId, ExpertiseAction expertiseAction, Long decreaseAmount) {
        String callPath = testApiConfiguration.getServerUrl();
        Map<String, String> getParams = new HashMap<>();
        switch (expertiseAction) {
            case EXPERTISE_NO_ACTION:
                return null;
            case EXPERTISE_PASSED_OK:
                callPath = callPath +  "/adminpanel/orders/set-expertise-passed";
                getParams.put("positionId", String.valueOf(orderPositionId));
                break;
            case EXPERTISE_REJECT_IT:
                callPath = callPath +  "/adminpanel/orders/set-expertise-reject";
                getParams.put("positionId", String.valueOf(orderPositionId));
                break;
            case EXPERTISE_DEFECT_IT:
                callPath = callPath +  "/adminpanel/orders/set-defect-price";
                getParams.put("positionId", String.valueOf(orderPositionId));
                getParams.put("discount", String.valueOf(decreaseAmount));
                break;
            case EXPERTISE_CLEANS_IT:
                callPath = callPath +  "/adminpanel/orders/set-cleaning";
                getParams.put("positionId", String.valueOf(orderPositionId));
                getParams.put("price", String.valueOf(decreaseAmount));
                break;
            default:
                throw new IllegalArgumentException(String.format("Unknown expertise action %s", expertiseAction.name()));
        }
        return apiV2Client.request(callPath, getParams, HttpMethod.PUT, null, new ParameterizedTypeReference<String>() {}, true);
    }

    public ResponseEntity<String> adminsApi1expertise(long orderPositionId, boolean waitOkay, ExpertiseAction expertiseAction, Long decreaseAmount) {
        if (expertiseAction == ExpertiseAction.EXPERTISE_NO_ACTION) {
            return null;
        }
        ResponseEntity<String> responseEntity = expertiseRawResponse(orderPositionId, expertiseAction, decreaseAmount);
        if (!waitOkay) {
            return responseEntity;
        }
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        return responseEntity;
    }

    public ResponseEntity<String> adminsApi1expertiseIssues(long orderPositionId, Long defectsAmount, Long serviceAmount, String expertiseText) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/set-expertise-issues";
        //
        Map<String, String> getParams = new HashMap<>();
        getParams.put("positionId", String.valueOf(orderPositionId));
        Optional.ofNullable(defectsAmount).ifPresent(it -> getParams.put("defectsAmount", String.valueOf(it)));
        Optional.ofNullable(serviceAmount).ifPresent(it -> getParams.put("serviceAmount", String.valueOf(it)));
        Optional.ofNullable(expertiseText).ifPresent(it -> getParams.put("expertiseText", expertiseText));
        //
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath, getParams, HttpMethod.PUT, null, new ParameterizedTypeReference<String>() {}, true);
        //
        Assertions.assertThat(responseEntity.getStatusCode().series()).isEqualTo(HttpStatus.Series.SUCCESSFUL);
        //
        return responseEntity;
    }

    public ResponseEntity<String> adminsApi1ApproveMarkingCode(long orderPositionId, boolean setValue) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/position/approve-marking-code";
        MultiValueMap<String, String> putParams = new LinkedMultiValueMap<>();
        putParams.add("positionId", String.valueOf(orderPositionId));
        putParams.add("setValue", String.valueOf(setValue));
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath, null, HttpMethod.PUT, putParams, new ParameterizedTypeReference<String>() {}, true);
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        return responseEntity;
    }

    public ResponseEntity<String> adminsApi1SetPositionMarkingCode(long orderPositionId, String datamatrix, boolean waitOkay) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/position/datamatrix";
        MultiValueMap<String, String> putParams = new LinkedMultiValueMap<>();
        putParams.add("positionId", String.valueOf(orderPositionId));
        putParams.add("datamatrix", datamatrix);
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath, null, HttpMethod.PUT, putParams, new ParameterizedTypeReference<String>() {}, true);
        if (!waitOkay) {
            return responseEntity;
        }
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        return responseEntity;
    }

    public ResponseEntity<String> adminsApi1SetOrderSourceInfo(long orderId, long orderSourceInfoId, boolean isOkayCall) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/set-order-source-infos";
        OrderSetOrderSourceInfoRequest orderSetOrderSourceInfoRequest = new OrderSetOrderSourceInfoRequest()
                .setOrderIds(ImmutableList.of(orderId))
                .setOrderSourceInfoId(orderSourceInfoId);
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.PATCH,
                orderSetOrderSourceInfoRequest,
                new ParameterizedTypeReference<String>() {},
                true);
        if (!isOkayCall) {
            return responseEntity;
        }
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        return responseEntity;
    }

    public ResponseEntity<String> adminsApi1SetOrderLegalEntity(List<Long> orderIds, long legalEntityId, boolean isOkayCall) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/set-order-legal-entity";
        OrderSetOrderLegalEntityRequest orderSetOrderLegalEntityRequest = new OrderSetOrderLegalEntityRequest()
                .setOrderIds(orderIds)
                .setLegalEntityId(legalEntityId);
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.POST,
                orderSetOrderLegalEntityRequest,
                new ParameterizedTypeReference<String>() {},
                true);
        if (!isOkayCall) {
            return responseEntity;
        }
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        return responseEntity;
    }

    public static final String FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE = "Заказ .*: не удается изменить параметры выплаты \\(реквизиты\\), выплата в процессе или завершена";
    public static final String FAIL_TEXT_ORDER_STATUS_PAY_WAIT = "Заказ .*: некорректное состояние заказа MONEY_PAYMENT_WAIT";
    public static final String FAIL_TEXT_ORDER_STATUS_COMPLETE = "Заказ .*: некорректное состояние заказа COMPLETED";

    public void changeSellerCounterpartyFailPayoutActiveOrDone(Long ordersId, long sellerCounterPartyId, String textMask) {
        ResponseEntity<String> failResp = changeSellerCounterparty(ordersId, sellerCounterPartyId, HttpStatus.Series.CLIENT_ERROR);
        Exception failInfo = readExceptionFromText(failResp.getBody());
        Assertions.assertThat(failInfo.getMessage()).matches(textMask);
    }

    public BigDecimal getCurrencyRate(String currencyCode, String currencyToCode) {
        return currencyRateService.getAllRates().stream()
                .filter(it -> Objects.equals(it.getCurrency().getIsoCode(), currencyCode) &&
                        Objects.equals(it.getCurrencyTo().getIsoCode(), currencyToCode))
                .findFirst()
                .map(it -> it.getRateValue())
                .orElseThrow(NoSuchElementException::new);
    }

    public void setCurrencyRate(String currencyCode, String currencyToCode, BigDecimal targetSetValue) {
        CurrencyRate currencyRate = currencyRateService.getAllRates().stream()
                .filter(it -> Objects.equals(it.getCurrency().getIsoCode(), currencyCode) &&
                        Objects.equals(it.getCurrencyTo().getIsoCode(), currencyToCode))
                .findFirst()
                .orElseThrow(NoSuchElementException::new);
        int rateState = targetSetValue.compareTo(currencyRate.getRateValue());
        if (rateState == 0) {
            return;
        }
        BigDecimal changeStep = currencyRate.getRateValue().min(targetSetValue) // Get smallest value of
                .divide(BigDecimal.TEN, 8, RoundingMode.HALF_UP)      // Get 10% steps
                .multiply(BigDecimal.valueOf(rateState));                       // Set direction: up / down
        for (BigDecimal trgRateVal = currencyRate.getRateValue(); targetSetValue.compareTo(trgRateVal) == rateState; trgRateVal = trgRateVal.add(changeStep)) {
            currencyRateService.updateRateValueByCurrencyId(currencyRate.getCurrency().getId(), currencyRate.getCurrencyTo().getId(),
                    trgRateVal, null);  // SneakUp:)
        }
        currencyRateService.updateRateValueByCurrencyId(currencyRate.getCurrency().getId(), currencyRate.getCurrencyTo().getId(),
                targetSetValue, null);  // SetValue!
    }

    public enum ExpertiseAction {
        EXPERTISE_PASSED_OK,
        EXPERTISE_NO_ACTION,
        EXPERTISE_REJECT_IT,
        EXPERTISE_DEFECT_IT,
        EXPERTISE_CLEANS_IT
    }

    public ResponseEntity<String> sendOurselves(long orderId, OskellyDeliveryInfo oskellyDeliveryInfo) {
        Map<String, String> getParams = new HashMap<>();
        OskellyDeliveryInfo deliveryInfo = Objects.isNull(oskellyDeliveryInfo) ? defaultOskellyDeliveryInfoO2B : oskellyDeliveryInfo;
        getParams.put("courierName", deliveryInfo.getCourierName());
        getParams.put("courierPhone", deliveryInfo.getCourierPhone());
        getParams.put("courierDate", String.valueOf(deliveryInfo.getCourierDate().atStartOfDay().toEpochSecond(ZoneOffset.UTC)));

        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/send-order-after-expertise/ourselves/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                getParams,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        return responseEntity;
    }

    public ResponseEntity<String> adminPanel_sendWithDeliveryCompany(long orderId, HttpStatus.Series series) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/send-confirmed-order/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        return responseEntity;
    }

    public ResponseEntity<String> adminPanel_paymentToConcierge(long orderId, boolean is200Okay) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/payment/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.POST,
                null, new ParameterizedTypeReference<String>() {}, true);
        if (!is200Okay) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        return responseEntity;
    }

    public ResponseEntity<String> adminPanel_switchDebtOnPayout(long orderId, boolean value) {
        Map<String, String> getParams = new HashMap<>();
        getParams.put("switchTo", BooleanUtils.toStringTrueFalse(value));
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/switch-debt-on-payout/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                getParams,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        return responseEntity;
    }

    public void processOrderPayment(OrderDTO orderDTO, OrderPaymentState orderPaymentState) {
        if (Optional.ofNullable(orderDTO.getPayment()).map(PaymentDTO::getPaymentVersion).map(this::isPaymentService).orElse(false)) {
            processPsPayment(orderDTO.getId(), orderPaymentState);
            return;
        };
        switch (orderPaymentState) {
            case CAPTURE_DONE:
                processBankOperation(orderDTO.getId(), OperationType.HOLD_COMPLETE);
                break;
            case REFUND_DONE:
                processBankOperation(orderDTO.getId(), OperationType.REFUND);
                break;
        }
    }

    public void processHoldComplete(OrderDTO orderDTO) {    // TODO: All orders must have orderPayment!
        if (Optional.ofNullable(orderDTO.getPayment()).map(PaymentDTO::getPaymentVersion).map(this::isPaymentService).orElse(false)) {
            processPsPayment(orderDTO.getId(), OrderPaymentState.CAPTURE_DONE);
        } else {
            processBankOperation(orderDTO.getId(), OperationType.HOLD_COMPLETE);
        }
    }

    public void processBankOperation(long orderId, OperationType operationType) {
        Order order = orderService.getOrder(orderId);
        List<OrderBankOperation> bankOperationList = order.getBankOperations().stream()
                .filter(op -> op.getOperationType() == operationType)
                .collect(Collectors.toList());
        BankOperation bankOperation = bankOperationList.stream()
                .filter(it -> it.getState() == TransactionState.INPROGRESS)
                .findFirst()
                .orElse(bankOperationList.stream().findFirst().orElse(null));
        Assertions.assertThat(bankOperation).isNotNull();
        Assertions.assertThat(bankOperation.getState()).isNotEqualTo(TransactionState.DONE);
        orderService.getBankAccountService(order).checkAndProcessBankOperation(bankOperation.getUuid());
        waitOrderChanges(orderId, 2000);
    }

    private void executePrepayStep(SpringTestWithDb testCase, OrderDTO testOrder) {
        ResponseEntity<String> responseOnConvertToPrepayment = convertHoldToPrepayment(testOrder.getId(), HttpStatus.Series.SUCCESSFUL);
        Assertions.assertThat(responseOnConvertToPrepayment.getStatusCode().series()).isEqualTo(HttpStatus.Series.SUCCESSFUL);
        testCase.rollbackAndStartNewTransaction();
        //
        validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_INPROGRESS);
        validateFiscalReceiptsTypeList(testOrder.getId(), ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE));
        //
        processHoldComplete(testOrder);
        testCase.rollbackAndStartNewTransaction();
        //
        validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
    }

    public void validateFiscalReceiptsTypeList(long orderId, List<FiscalReceiptRequestType> fiscalReceiptRequestTypes) {
        Order order = orderService.getOrder(orderId);
        Assertions.assertThat(order.getBuyerCheckStartTime()).isNull();
        Assertions.assertThat(order.getBuyerCheckEndTime()).isNull();
        Assertions.assertThat(order.getBuyerCheck()).isNull();
        //
        List<FiscalReceiptRequestType> actualTypesList = fiscalReceiptRequestService.getRequestsForOrder(orderId).stream()
                .sorted(Comparator.comparing(FiscalReceiptRequest::getId))
                .map(FiscalReceiptRequest::getReceiptType)
                .collect(Collectors.toList());
        Assertions.assertThat(actualTypesList).isEqualTo(fiscalReceiptRequestTypes);
    }

    public static final long ITEM_KIND_ADVANCE = Checkable.Line.LINE_ATTRIBUTE_ADVANCE;
    public static final long ITEM_KIND_COMMODITY = Checkable.Line.LINE_ATTRIBUTE_COMMODITY;
    public static final long ITEM_KIND_SERVICE = Checkable.Line.LINE_ATTRIBUTE_SERVICE;

    public static final long ITEM_PAY_KIND_ADVANCE = Checkable.Line.LINE_PAYMENT_ADVANCE;
    public static final long ITEM_PAY_KIND_FULLPAY = Checkable.Line.LINE_PAYMENT_FULL_PAYMENT;

    @SneakyThrows
    public BuyerCheckRequest validateOrderFiscalReceipt(long orderId,
                                    FiscalReceiptRequestType requestType,
                                    Long documentType,
                                    List<Long> positionsAmount,
                                    List<Long> positionsItemKind,
                                    List<Long> positionsPaymentKind) {
        List<FiscalReceiptRequest> receiptsList = fiscalReceiptRequestService.getRequestsForOrder(orderId).stream()
                .filter(fr -> fr.getReceiptType() == requestType)
                .collect(Collectors.toList());
        Assertions.assertThat(receiptsList).hasSize(1);
        FiscalReceiptRequest receiptRequest = receiptsList.get(0);
        Assertions.assertThat(receiptRequest).isNotNull();
        //
        BuyerCheckRequest receiptRequestData = objectMapper.readValue(receiptRequest.getReceiptRequest(), BuyerCheckRequest.class);
        //
        Assertions.assertThat(receiptRequestData.getDocumentType()).isEqualTo(documentType.shortValue());
        //
        List<Long> actualPositionsAmount = receiptRequestData.getLines().stream()
                .map(Line::getPrice)
                .collect(Collectors.toList());
        Assertions.assertThat(positionsAmount).containsExactlyElementsOf(actualPositionsAmount);
        //
        List<Long> actualPositionsItemKind = receiptRequestData.getLines().stream()
                .map(it -> Long.valueOf(it.getLineAttribute()))
                .collect(Collectors.toList());
        Assertions.assertThat(positionsItemKind).containsExactlyElementsOf(actualPositionsItemKind);
        //
        List<Long> actualPositionsPaymentKind = receiptRequestData.getLines().stream()
                .map(it -> Long.valueOf(it.getPayAttribute()))
                .collect(Collectors.toList());
        Assertions.assertThat(positionsPaymentKind).containsExactlyElementsOf(actualPositionsPaymentKind);
        //
        return receiptRequestData;
    }

    public BuyerCheckRequest validateOrderFiscalReceipt(OrderDTO orderDTO, FiscalReceiptRequestType requestType) {
        if (requestType == FiscalReceiptRequestType.DELIVERY_ADVANCE) {
            return validateOrderFiscalReceipt(orderDTO.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                    Lists.newArrayList(orderDTO.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                    Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE));
        }
        if (requestType == FiscalReceiptRequestType.DELIVERY_PAYMENT) {
            return validateOrderFiscalReceipt(orderDTO.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                    Lists.newArrayList(orderDTO.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_SERVICE),
                    Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY));
        }
        if (requestType == FiscalReceiptRequestType.DELIVERY_REFUND) {
            return validateOrderFiscalReceipt(orderDTO.getId(), FiscalReceiptRequestType.DELIVERY_REFUND, 2L,
                    Lists.newArrayList(orderDTO.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                    Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE));
        }
        throw new IllegalArgumentException("Unable to validate receipts: no code");
    }

    public ResponseEntity<String> sendAgentReport(long orderId, boolean is200Okay) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/send-agentreport/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        if (!is200Okay) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        return responseEntity;
    }

    public ResponseEntity<String> setHasDispute(long orderId, boolean hasDisput, boolean is200Okay) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/dispute/" + orderId + "?hasDispute=" + hasDisput;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        if (!is200Okay) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        return responseEntity;
    }

    public void excludeFromAgentReport(long orderPositionId, boolean doExclude) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/exclude-from-agentreport?doExclude=" + doExclude;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                TestUtils.getOneParamAsMap("positionId", orderPositionId),
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderPositionId));
    }

    public void refundOnReturn(long orderId) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/refund/" + orderId + "?onReturn=true";
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
    }

    public void refundOnReturnAuto(long orderId) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/return/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
    }

    public ResponseEntity<String> adminPanel_refund(long orderId, boolean isWait200Okay) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/refund/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        if (!isWait200Okay) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        return responseEntity;
    }

    public ResponseEntity<String> adminPanel_setInStorePickUp(long orderId, boolean value, boolean isWait200Okay) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/in-store-pickup/" + orderId;
        Map<String, String> postParams = new HashMap<>();
        postParams.put("inStorePickupState", Boolean.toString(value));
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                postParams,
                HttpMethod.POST,
                null, new ParameterizedTypeReference<String>() {}, true);
        if (!isWait200Okay) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        return responseEntity;
    }

    public ResponseEntity<String> adminPanel_refundAmount(long orderId, BigDecimal refundAmount, String comment, boolean isWait200Okay) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/refund-amount/" + orderId;
        //
        AdminOrdersController.OrderRefundAmountReq orderRefundAmountReq = AdminOrdersController.OrderRefundAmountReq.builder()
                .refundAmount(refundAmount)
                .comment(comment)
                .build();
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.POST,
                orderRefundAmountReq, new ParameterizedTypeReference<String>() {}, true);
        if (!isWait200Okay) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        return responseEntity;
    }


    public ResponseEntity<String> returnCompletedOrSoldOrder(long orderId, Boolean debtOnReturn, boolean wait200okay, String onecId) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/return-completed/" + orderId;
        Map<String, String> getParams = new HashMap<>();
        Optional.ofNullable(debtOnReturn).ifPresent(value -> getParams.put("debtBalance", value.toString()));
        getParams.put("onecId", onecId);
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                getParams,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        if (!wait200okay) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        return responseEntity;
    }

    public ResponseEntity<String> sellOrderInBoutique(long orderId, boolean is200Okay, String onecId) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/sell-order-in-boutique/" + orderId;
        Map<String, String> getParams = new HashMap<>();
        getParams.put("isOrderProcessingNotificationRequired", "false");
        getParams.put("onecId", onecId);

        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                getParams,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        if (!is200Okay) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        return responseEntity;
    }

    public ResponseEntity<String> confirmAgentReport(long orderId, boolean is200Okay) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/confirm-agentreport/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<String>() {}, true);
        if (!is200Okay) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        Assertions.assertThat(responseEntity.getBody()).isEqualTo(String.valueOf(orderId));
        waitOrderChanges(orderId, 2000);
        return responseEntity;
    }

    public ResponseEntity<String> adminPanel_Charge(long orderId) {
        String callPath = testApiConfiguration.getServerUrl() + "/adminpanel/orders/charge/" + orderId;
        ResponseEntity<String> responseEntity = apiV2Client.request(callPath,
                null,
                HttpMethod.POST,
                null, new ParameterizedTypeReference<String>() {}, true);
        return responseEntity;
    }

    public List<OrderDTO> setBoutiqueItemSold(long productItemId, String receiptGuid, String receiptNumber, LocalDateTime receiptDate, long amount, long sum, String onecId) {
        final String receiptDateStr = DateTimeFormatter.ISO_DATE_TIME.format(receiptDate);
        final String url = String.format(
                "%s/api/v2/boutique/sold?productItemId=%s&receiptGuid=%s&receiptNumber=%s&receiptDate=%s&amount=%s&sum=%s&onecId=%s",
                testApiConfiguration.getServerUrl(), productItemId, receiptGuid, receiptNumber, receiptDateStr, amount, sum, onecId);
        final ResponseEntity<Api2Response<List<OrderDTO>>> responseEntity = apiV2Client.request(url,
                null,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<Api2Response<List<OrderDTO>>>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        return responseEntity.getBody().getData();
    }

    public List<OrderDTO> returnBoutiqueItem(long productItemId, String receiptGuid, String receiptNumber, long amount, String saleReceiptGuid, String onecId) {
        final String url = String.format(
                "%s/api/v2/boutique/return?productItemId=%s&receiptGuid=%s&receiptNumber=%s&amount=%s&saleReceiptGuid=%s&onecId=%s",
                testApiConfiguration.getServerUrl(), productItemId, receiptGuid, receiptNumber, amount, saleReceiptGuid, onecId);
        final ResponseEntity<Api2Response<List<OrderDTO>>> responseEntity = apiV2Client.request(url,
                null,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<Api2Response<List<OrderDTO>>>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        return responseEntity.getBody().getData();
    }

    public List<OrderDTO> returnBoutiqueItemToSeller(long productItemId, String returnGuid, long amount, String onecId, String legalEntityUid) {
        final String url = String.format(
                "%s/api/v2/boutique/return-to-seller?productItemId=%s&returnGuid=%s&amount=%s&onecId=%s&legalEntityUid=%s",
                testApiConfiguration.getServerUrl(), productItemId, returnGuid, amount, onecId, legalEntityUid);
        final ResponseEntity<Api2Response<List<OrderDTO>>> responseEntity = apiV2Client.request(url,
                null,
                HttpMethod.PUT,
                null, new ParameterizedTypeReference<Api2Response<List<OrderDTO>>>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        return responseEntity.getBody().getData();
    }

    public void prepareSellerPayout(long orderId, boolean isExpectingOrder) {
        List<Long> orderIdsList = scheduledAgentReportTaskRunner.runPreparePayment();
        Assertions.assertThat(orderIdsList.contains(orderId)).isEqualTo(isExpectingOrder);
    }

    public String getBaseCurrencyIsoCode() {
        return currencyService.getBaseCurrency().getIsoCode();
    }

    public BankPayment validateSellerPayoutBP(long orderId, long payoutAmount, String currencyCode, String paymentVersion, TransactionState transactionState) {
        Order order = orderService.getOrder(orderId);
        Assertions.assertThat(order.getAgentReport().getBankPayments()).hasSize(1);
        BankPayment bankPayment = order.getAgentReport().getBankPayments().get(0);
        Assertions.assertThat(order.getPaymentVersion()).isEqualTo(paymentVersion);
        String validateCurrencyCode = Objects.isNull(currencyCode)
                ? getBaseCurrencyIsoCode()
                : currencyCode;
        Assertions.assertThat(bankPayment.getOriginalCurrencyCode()).isEqualTo(validateCurrencyCode);
        Assertions.assertThat(bankPayment.getOriginalCurrencyAmount().movePointRight(2).longValue()).isEqualTo(payoutAmount);
        if (Objects.isNull(currencyCode)) {
            Assertions.assertThat(bankPayment.getAmount().movePointRight(2).longValue()).isEqualTo(payoutAmount);
        }
        Assertions.assertThat(bankPayment.getState()).isEqualTo(transactionState);
        if (bankPayment.getState() != TransactionState.PREPARED) {
            Assertions.assertThat(bankPayment.getBank()).isEqualTo(paymentsSchemeToPayoutBankName.get(order.getPaymentVersion()));
        }
        return bankPayment;
    }

    public BankOperation validateSellerPayoutBO(long orderId, long payoutAmount, String currencyCode, String paymentVersion, TransactionState transactionState) {
        if (transactionState != TransactionState.DONE) {
            return null;
        }
        return validateBankOperation(orderId, OperationType.SELLER_PAYOUT, payoutAmount);
    }

    public void validateSellerPayoutOpNet(long orderId) {
        Order order = orderService.getOrder(orderId);
        // Kinda dirty since we can payout on any stage in concierge, but there are no tests for "anytime payout" 4 now
        BigDecimal payoutByPositions = order.getOrderPositions().stream()
                .filter(it -> order.isConciergeOrder() ? it.isConfirmed().orElse(false) : it.hasExpertisePassed())
                .map(it -> order.isConciergeOrder() ? it.getSellerPayoutAmountRaw() : it.getSellerPayoutAmountNet())
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Assertions.assertThat(payoutByPositions).isEqualByComparingTo(order.getAgentReport().getPaymentAmount());
    }

    public void validateSellerPayout(long orderId, long payoutAmount, String currencyCode, String paymentVersion, TransactionState transactionState) {
        validateSellerPayoutBP(orderId, payoutAmount, currencyCode, paymentVersion, transactionState);
        validateSellerPayoutBO(orderId, payoutAmount, currencyCode, paymentVersion, transactionState);
        validateSellerPayoutOpNet(orderId);
    }

    public void validateAgentReportPayout(long orderId, int payoutsCount, long payoutAmount, String currencyCode, String bankName) {
        Order order = orderService.getOrder(orderId);
        Assertions.assertThat(order.getAgentReport().getAgentReportPayments()).hasSize(payoutsCount);
        Optional<AgentReportPayments> arPayment = order.getAgentReport().getAgentReportPayments().stream()
                .filter(arp -> arp.getAgentReportPaymentsId().getBank().equals(bankName))
                .findFirst();
        Assertions.assertThat(arPayment).isPresent();
        Assertions.assertThat(arPayment.get().getAgentReportPaymentsId().getBank()).isEqualTo(bankName);
        Assertions.assertThat(arPayment.get().getAmount().movePointRight(2).longValue()).isEqualTo(payoutAmount);
    }

    public Map<Long, Integer> getProductCountByProductItem(long productId) {
        Product product = productRepository.getOne(productId);
        return product.getProductItems().stream().collect(Collectors.toMap(ProductItem::getId, ProductItem::getCount));
    }

    public Api2Response<ProductDTO> getProduct(long productId) {
        ResponseEntity<Api2Response<ProductDTO>> responseEntity = apiV2Client.request(getProductUrl(productId), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<ProductDTO>>() {}, true);
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        assertNotNull(responseEntity.getBody());
        assertNotNull(responseEntity.getBody().getData());
        return responseEntity.getBody();
    }

    public Product loadProduct(long productId) {
        return productRepository.getOne(productId);
    }

    public ResponseEntity<String> setCartAddressEndpoint(long addressEndpointId) {
        ResponseEntity<String> response = apiV2Client.request(
                getCartDeliveryAddressEndpointUrl(),
                null,
                HttpMethod.PUT,
                TestUtils.getOneParamAsMultiValueMap("deliveryAddressEndpointId", addressEndpointId),
                String.class,
                true);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        return response;
    }

    public OrderService.InitOrderResult holdOrderSuccessfull(Long orderId, HoldRequest holdRequest, boolean withAuthoriseParams) {
        Map<String, String> params = new HashMap<>();
        if (Objects.nonNull(holdRequest)) {
            Optional.ofNullable(holdRequest.getPaymentBuyerCounterpartyId()).ifPresent(it -> params.put("paymentBuyerCounterpartyId", it.toString()));
            Optional.ofNullable(holdRequest.getCurrencyCode()).ifPresent(it -> params.put("currencyCode", it));
            Optional.ofNullable(holdRequest.getType()).ifPresent(it -> params.put("type", it));
        }
        ResponseEntity<Api2Response<OrderService.InitOrderResult>> responseEntity = apiV2Client.request(getOrderUrl(orderId) + "/hold", params, HttpMethod.POST, null, new ParameterizedTypeReference<Api2Response<OrderService.InitOrderResult>>() {}, withAuthoriseParams);
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        assertNotNull(responseEntity.getBody());
        assertNotNull(responseEntity.getBody().getData());
        return responseEntity.getBody().getData();
    }

    public BigDecimal adjustUserBalanceToValue(long userId, BigDecimal value) {
        BigDecimal currentBalance = userBalanceService.getBalance(userId);
        if (Objects.isNull(value)) {
            return currentBalance;
        }
        if (currentBalance.compareTo(value) == 0) {
            return currentBalance;
        }
        String adjustOpId = "adjust-user-balance-" + UUID.randomUUID();
        BigDecimal adjustAmount = currentBalance.subtract(value);
        if (adjustAmount.longValue() > 0) {
            userBalanceService.debit(UserBalanceChangeParams.builder()
                    .userId(userId)
                    .amount(adjustAmount.abs())
                    .operationId(adjustOpId)
                    .comment(adjustOpId)
                    .userBalanceChangeMode(UserBalanceChangeMode.AUTO)
                    .build());
        } else {
            userBalanceService.credit(UserBalanceChangeParams.builder()
                    .userId(userId)
                    .amount(adjustAmount.abs())
                    .operationId(adjustOpId)
                    .comment(adjustOpId)
                    .userBalanceChangeMode(UserBalanceChangeMode.AUTO)
                    .build());
        }
        BigDecimal modifiedBalance = userBalanceService.getBalance(userId);
        Assertions.assertThat(modifiedBalance).isEqualByComparingTo(value);
        return modifiedBalance;
    }

    public UserBalanceChange getUserBalanceChangeByObjectId(long userId, String objectId) {
        List<UserBalanceChange> userBalanceHistory = userBalanceService.getUserBalanceHistory(userId);
        Optional<UserBalanceChange> balanceChange = userBalanceHistory.stream()
                .filter(changeItem -> Objects.equals(changeItem.getObjectId(), objectId))
                .findAny();
        Assertions.assertThat(balanceChange).isPresent();
        return balanceChange.get();
    }

    public void validateBalanceChange(long userId, BigDecimal startBalance, BigDecimal balanceChange) {
        BigDecimal actualChange = startBalance.subtract(userBalanceService.getBalance(userId));
        Assertions.assertThat(actualChange).isEqualByComparingTo(balanceChange);
    }

    public List<BankPaymentDTO> transferMoneyToSellers(long validateOrderIds) {
        List<BankPaymentDTO> result = scheduledBankRunner.transferMoneyToSellers();
        Assertions.assertThat(result.stream().anyMatch(bp -> bp.getOrderId().equals(validateOrderIds))).isTrue();
        waitOrderChanges(validateOrderIds, 2000);
        return result;
    }

    public List<BankPaymentDTO> validateMoneyToSellers(long validateOrderIds) {
        List<BankPaymentDTO> result = scheduledBankRunner.checkTransferMoneyToSeller();
        Assertions.assertThat(result.stream().anyMatch(bp -> bp.getOrderId().equals(validateOrderIds))).isTrue();
        waitOrderChanges(validateOrderIds, 2000);
        return result;
    }

    public void transferAgentPaymentsMoneyToSellers() {
        agentPaymentJobs.transferMoneyToSeller();
    }

    public ResponseEntity<String> loadAgentReport(long orderId) {
        Order order = orderService.getOrder(orderId);
        Assertions.assertThat(order.getAgentReport()).isNotNull();
        ResponseEntity<String> response = apiV2Client.request(getAgentReportPdfUrl(order.getAgentReport().getId()), null, HttpMethod.GET, null, String.class, true);
        assertTrue(response.getStatusCode().is2xxSuccessful());
        return response;
    }

    public ResponseEntity<String> getOrderDocument(long orderId, String documentName, String documentFormat) {
        ResponseEntity<String> response = apiV2Client.request(getOrderDocumentUrl(orderId, documentName, documentFormat),
                null, HttpMethod.GET, null, String.class, true);
        Assertions.assertThat(response.getStatusCode().is2xxSuccessful()).isTrue();
        return response;
    }

    public Api2Response<List<DocumentLinkDTO>> getClientOrderDocuments(ApiV2Client customApiV2Client, long orderId) {
        ApiV2Client callClient = Objects.isNull(customApiV2Client) ? apiV2Client : customApiV2Client;
        ResponseEntity<Api2Response<List<DocumentLinkDTO>>> responseEntity = callClient.request(getOrderClientDocumentsUrl(orderId), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<DocumentLinkDTO>>>() {}, true);
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        return responseEntity.getBody();
    }

    public Api2Response<List<DocumentLinkDTO>> getAdminsOrderDocuments(ApiV2Client customApiV2Client, long orderId) {
        ApiV2Client callClient = Objects.isNull(customApiV2Client) ? apiV2Client : customApiV2Client;
        ResponseEntity<Api2Response<List<DocumentLinkDTO>>> responseEntity = callClient.request(getOrderAdminsDocumentsUrl(orderId), null, HttpMethod.GET, null, new ParameterizedTypeReference<Api2Response<List<DocumentLinkDTO>>>() {}, true);
        assertTrue(responseEntity.getStatusCode().is2xxSuccessful());
        return responseEntity.getBody();
    }

    public Api2Response<List<Long>> getApiV2AdminOrdersIds(OrderSourceDTO orderSource, List<OrderStateDTO> orderStates, String searchCodes) {
        Map<String, String> getParams = new HashMap<>();
        Optional.ofNullable(searchCodes).ifPresent(it -> getParams.put("searchByScanCode", searchCodes));
        Optional.ofNullable(orderSource).ifPresent(it -> getParams.put("orderSource", String.valueOf(orderSource)));
        Optional.ofNullable(orderStates).ifPresent(it -> getParams.put("orderStates", orderStates.stream().map(Enum::name).collect(Collectors.joining(","))));
        ResponseEntity<Api2Response<List<Long>>> responseEntity = apiV2Client.request(getApiV2AdminOrdersIdsUrl(),
                getParams,
                HttpMethod.GET,
                null, new ParameterizedTypeReference<Api2Response<List<Long>>>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().series()).isEqualTo(HttpStatus.Series.SUCCESSFUL);
        return responseEntity.getBody();
    }

    public Api2Response<List<BankOperationDTO>> getApiV2AdminBankOperations(long orderId) {
        ResponseEntity<Api2Response<List<BankOperationDTO>>> responseEntity = apiV2Client.request(getApiV2AdminBankOperationsUrl(orderId),
                null,
                HttpMethod.GET,
                null, new ParameterizedTypeReference<Api2Response<List<BankOperationDTO>>>() {}, true);
        Assertions.assertThat(responseEntity.getStatusCode().series()).isEqualTo(HttpStatus.Series.SUCCESSFUL);
        return responseEntity.getBody();
    }

    public static BankOperationDTO findBankOperation(List<BankOperationDTO> bankOperations, OperationType operationType) {
        List<BankOperationDTO> operationsWithType = bankOperations.stream()
                .filter(it -> it.getOperationType() == operationType)
                .collect(Collectors.toList());
        if (operationsWithType.size() != 1) {
            throw new IllegalArgumentException("Unable to find exactly one op in list");
        }
        return operationsWithType.get(0);
    }

    public ResponseEntity<String> sellerApiConfirmPosition(ApiV2Client customApiV2Client, boolean waitOkay, long orderId, long positionId, boolean isConfirmAction, String markingCode) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("orderPositionId", String.valueOf(positionId));
        params.add("doConfirmSale", String.valueOf(isConfirmAction));
        Optional.ofNullable(markingCode).ifPresent(it -> params.add("datamatrix", it));
        ResponseEntity<String> responseEntity = customApiV2Client.request(getOrderPositionConfirmUrl(orderId), null, HttpMethod.PATCH, params, String.class, true);
        if (!waitOkay) {
            return responseEntity;
        }
        Assertions.assertThat(responseEntity.getStatusCode().is2xxSuccessful()).isTrue();
        return responseEntity;
    }

    public Long findOrCreateCardCounterpartyWithCardId(User user, String cardId) {
        Optional<Long> existingId = user.getCounterparties().stream()
                .filter(cp -> cp.getType() == CounterpartyType.CARD && ((CardCounterparty)cp).getCardRefId().equals(cardId))
                .map(Counterparty::getId)
                .findFirst();
        if (existingId.isPresent()) {
            return existingId.get();
        }
        CardCounterparty cardCp = new CardCounterparty();
        cardCp.setUser(user);
        cardCp.setCardRefId(cardId);
        Counterparty counterpartyInfo = counterpartyService.save(cardCp);
        return counterpartyInfo.getId();
    }

    public OrderPositionDTO getOpDto(OrderDTO orderDTO, long productId) {
        return orderDTO.getItems().stream().filter(it -> it.getProductId() == productId).findFirst().orElse(null);
    }

    @SneakyThrows
    public Exception readExceptionFromText(String exceptionData) {
        return objectMapper.readValue(exceptionData, Exception.class);
    }

    @SneakyThrows
    public String intoJson(Object object) {
        return objectMapper.writeValueAsString(object);
    }

    @SneakyThrows
    public <T> T fromJson(String value, TypeReference valueTypeRef) {
        return objectMapper.readValue(value, valueTypeRef);
    }

    private void callAndValidateAdminsApi1expertiseService(long orderId, long orderPositionId, Long serviceAmount) {
        adminsApi1expertise(orderPositionId, true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_CLEANS_IT, serviceAmount);
        OrderDTO orderDTO = loadOrderSuccessfull(orderId, true);
        OrderPositionDTO orderPosition = orderDTO.getItems().stream().filter(it -> Objects.equals(it.getId(), orderPositionId)).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unable to find orderPosition"));
        Assertions.assertThat(orderPosition.getState()).isEqualTo(OrderPositionState.VERIFICATION_NEED_CLEANING);
        //
        ExpertiseDTO expertise = orderPosition.getExpertises().stream().findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unable to find expertise info"));
        Assertions.assertThat(expertise.getIsApproved()).isNotEqualTo(Boolean.TRUE);
        Assertions.assertThat(expertise.getDefectDiscount()).isNull();
        Assertions.assertThat(expertise.getDefectDiscountPrice()).isNull();
        Assertions.assertThat(expertise.getCleaningPrice()).isEqualByComparingTo(BigDecimal.valueOf(serviceAmount, 0));
    }

    private void callAndValidateAdminsApi1expertiseDefects(long orderId, long orderPositionId, Long defectsAmount) {
        adminsApi1expertise(orderPositionId, true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_DEFECT_IT, defectsAmount);
        OrderDTO orderDTO = loadOrderSuccessfull(orderId, true);
        OrderPositionDTO orderPosition = orderDTO.getItems().stream().filter(it -> Objects.equals(it.getId(), orderPositionId)).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unable to find orderPosition"));
        Assertions.assertThat(orderPosition.getState()).isEqualTo(OrderPositionState.VERIFICATION_BAD_STATE);
        //
        ExpertiseDTO expertise = orderPosition.getExpertises().stream().findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unable to find expertise info"));
        Assertions.assertThat(expertise.getIsApproved()).isNotEqualTo(Boolean.TRUE);
        Assertions.assertThat(expertise.getDefectDiscount()).isNull();
        Assertions.assertThat(expertise.getDefectDiscountPrice()).isEqualByComparingTo(BigDecimal.valueOf(defectsAmount, 0));
        Assertions.assertThat(expertise.getCleaningPrice()).isNull();
    }

    private void callAndValidateAdminsApi1expertiseIssues(long orderId, long orderPositionId, Long defectsAmount, Long serviceAmount) {
        adminsApi1expertiseIssues(orderPositionId, defectsAmount, serviceAmount, "");
        OrderDTO orderDTO = loadOrderSuccessfull(orderId, true);
        OrderPositionDTO orderPosition = orderDTO.getItems().stream().filter(it -> Objects.equals(it.getId(), orderPositionId)).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unable to find orderPosition"));
        Assertions.assertThat(orderPosition.getState()).isEqualTo(OrderPositionState.VERIFICATION_BAD_STATE);
        //
        ExpertiseDTO expertise = orderPosition.getExpertises().stream().findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unable to find expertise info"));
        Assertions.assertThat(expertise.getIsApproved()).isNotEqualTo(Boolean.TRUE);
        Assertions.assertThat(expertise.getDefectDiscount()).isNull();
        Assertions.assertThat(expertise.getDefectDiscountPrice()).isEqualByComparingTo(BigDecimal.valueOf(defectsAmount, 0));
        Assertions.assertThat(expertise.getCleaningPrice()).isEqualByComparingTo(BigDecimal.valueOf(serviceAmount));
    }

    private void callAndValidateAdminsApi1expertisePassOkay(long orderId, long orderPositionId) {
        adminsApi1expertise(orderPositionId, true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_PASSED_OK, null);
        OrderDTO orderDTO = loadOrderSuccessfull(orderId, true);
        OrderPositionDTO orderPosition = orderDTO.getItems().stream().filter(it -> Objects.equals(it.getId(), orderPositionId)).findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unable to find orderPosition"));
        ExpertiseDTO expertise = orderPosition.getExpertises().stream().findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Unable to find expertise info"));
        Assertions.assertThat(expertise.getIsApproved()).isTrue();
        Assertions.assertThat(expertise.getDefectDiscount()).isNull();
        Assertions.assertThat(expertise.getDefectDiscountPrice()).isNull();
        Assertions.assertThat(expertise.getCleaningPrice()).isNull();
    }

    public int processExpertiseSteps(OrderDTO orderInfo, List<Product> products, OrderFlowTestUtils.TestConfig testConfig) {
        AtomicInteger expertisePositionsCount = new AtomicInteger(0);
        if (!ImmutableList.of(0, testConfig.itemsCount).contains(testConfig.getDefectsByPositions().size())) {
            Assertions.fail("Defects list size must be one of [0, %s]", testConfig.itemsCount);
        }
        if (!ImmutableList.of(0, testConfig.itemsCount).contains(testConfig.getCleaningsByPositions().size())) {
            Assertions.fail("Service list size must be one of [0, %s]", testConfig.itemsCount);
        }
        IntStream.range(0, testConfig.itemsCount).forEach(idx -> {
            OrderPositionDTO orderPosition = getOpDto(orderInfo, products.get(idx).getId());
            Long defectsAmount = testConfig.getDefectsByPositions().isEmpty() ? null : testConfig.getDefectsByPositions().get(idx);
            Long serviceAmount = testConfig.getCleaningsByPositions().isEmpty() ? null : testConfig.getCleaningsByPositions().get(idx);
            if (Objects.nonNull(defectsAmount) && Objects.isNull(serviceAmount)) {
                callAndValidateAdminsApi1expertiseDefects(orderInfo.getId(), orderPosition.getId(), defectsAmount);
                expertisePositionsCount.incrementAndGet();
            }
            if (Objects.nonNull(serviceAmount) && Objects.isNull(defectsAmount)) {
                callAndValidateAdminsApi1expertiseService(orderInfo.getId(), orderPosition.getId(), serviceAmount);
                expertisePositionsCount.incrementAndGet();
            }
            if (Objects.nonNull(serviceAmount) && Objects.nonNull(defectsAmount)) { // TODO: DEVALAN-1528, replace with EXPERTISE_DEFECTS_SERVICE
                if (Objects.isNull(testConfig.comboExpertiseMode)) {
                    Assertions.fail("Unable to combine expertise defects with service");
                }
                if (testConfig.comboExpertiseMode == ComboExpertiseMode.SERVICE_THEN_DEFECTS) {
                    callAndValidateAdminsApi1expertiseService(orderInfo.getId(), orderPosition.getId(), serviceAmount);
                    callAndValidateAdminsApi1expertiseDefects(orderInfo.getId(), orderPosition.getId(), defectsAmount);
                }
                if (testConfig.comboExpertiseMode == ComboExpertiseMode.DEFECTS_THEN_SERVICE) {
                    callAndValidateAdminsApi1expertiseDefects(orderInfo.getId(), orderPosition.getId(), defectsAmount);
                    callAndValidateAdminsApi1expertiseService(orderInfo.getId(), orderPosition.getId(), serviceAmount);
                }
                if (testConfig.comboExpertiseMode == ComboExpertiseMode.DEFECTS_WITH_SERVICE) {
                    callAndValidateAdminsApi1expertiseIssues(orderInfo.getId(), orderPosition.getId(), defectsAmount, serviceAmount);
                }
                expertisePositionsCount.incrementAndGet();
            }
        });
        testConfig.getExpertisePassPositions().forEach(idx -> {
            OrderPositionDTO orderPosition = getOpDto(orderInfo, products.get(idx - 1).getId());
            callAndValidateAdminsApi1expertisePassOkay(orderInfo.getId(), orderPosition.getId());
            expertisePositionsCount.incrementAndGet();
        });
        testConfig.getExpertiseFailPositions().forEach(idx -> {
            OrderPositionDTO orderPosition = getOpDto(orderInfo, products.get(idx - 1).getId());
            adminsApi1expertise(orderPosition.getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_REJECT_IT, null);
        });
        return expertisePositionsCount.get();
    }

    public int confirmRefuseOrderPositions(OrderDTO orderInfo, List<Product> products, OrderFlowTestUtils.TestConfig conciergeTestConfig) {
        AtomicInteger confirmedPositionsCount = new AtomicInteger();
        CollectionUtils.emptyIfNull(conciergeTestConfig.getConfirmPositions()).forEach(it -> {
            OrderPositionDTO orderPosition = getOpDto(orderInfo, products.get(it - 1).getId());
            rejectOrApprovePosition(orderPosition.getId(), true);
            confirmedPositionsCount.getAndIncrement();
        });
        CollectionUtils.emptyIfNull(conciergeTestConfig.getRefusePositions()).forEach(it -> {
            OrderPositionDTO orderPosition = getOpDto(orderInfo, products.get(it - 1).getId());
            rejectOrApprovePosition(orderPosition.getId(), false);
        });
        return confirmedPositionsCount.get();
    }

    private List<Product> getProductsForOrdersWithSellerType(OrderFlowTestUtils.TestConfig testOrderConfig) {
        List<Product> products = getProductsForOrdersWithSeller(FindProduct4Test.builder()
                .sellerId(testOrderConfig.isAgentSeller ? testOrderConfig.getAgentSellerId() : testOrderConfig.getUsualSellerId())
                .maxItems(testOrderConfig.itemsCount)
                .build()
        );
        products.forEach(it -> it.setSelectedConciergeTime(testOrderConfig.isConciergeOrder() ? LocalDateTime.now() : null));
        return products;
    }

    private void prepareAgentReport4Compare(JsonNode jsonNode) {
        if (Objects.isNull(jsonNode)) {
            return;
        }
        ((ObjectNode)jsonNode.get("reportData")).put("orderName", "");
        ((ObjectNode)jsonNode.get("reportData")).put("registrationDate", "");
        ((ObjectNode)jsonNode.get("reportData")).put("agentReportDate", "");
        //
        jsonNode.get("reportData").get("itemList").forEach(it -> {
            ((ObjectNode)it).put("date", "");
            ((ObjectNode)it).put("goodIdentification", "");
        });
    }

    @SneakyThrows
    private void validateSaleReportRu(SpringTestWithDb testCase, long ordersId) {
        ResponseEntity<String> reportGenRsp = getOrderDocument(ordersId, AgentReportRuDocumentGenerator.DOC_NAME_AGENT_REPORT_RU, OrderDocumentGenerator.DOC_FMT_RAW_JSON);
        JsonNode reportGenJson = objectMapper.readTree(reportGenRsp.getBody());
        prepareAgentReport4Compare(reportGenJson);
        //
        String reportResPath = "json/agent-report-ru/" + testCase.getClass().getSimpleName() + "/" + testCase.testName + ".json";
        URL reportResData = getClass().getClassLoader().getResource(reportResPath);
        String reportResText = Objects.isNull(reportResData) ? null : IOUtils.toString(reportResData, StandardCharsets.UTF_8);
        JsonNode reportResJson = Objects.isNull(reportResText) ? null : objectMapper.readTree(reportResText);
        prepareAgentReport4Compare(reportResJson);
        //
        Assertions.assertThat(reportGenJson).isEqualTo(reportResJson);
    }

    private void processTestOrderAuthorizeReverseCaptureRefund(SpringTestWithDb testCase, TestConfig testOrderConfig, OrderDTO testOrder) {
        adminPanel_refund(testOrder.getId(), true);
        testCase.rollbackAndStartNewTransaction();
        //
        if (testOrderConfig.isAnyPrepayModeSet() || testOrderConfig.isConciergeOrder()) {
            processOrderPayment(testOrder, OrderPaymentState.REFUND_DONE);
            testCase.rollbackAndStartNewTransaction();
        }
        //
        ResponseEntity<String> sendOurselvesFail = sendOurselves(testOrder.getId(), null);
        Assertions.assertThat(sendOurselvesFail.getStatusCode().series()).isEqualTo(HttpStatus.Series.CLIENT_ERROR);
        Exception thrownOnSend = readExceptionFromText(sendOurselvesFail.getBody());
        Assertions.assertThat(thrownOnSend.getMessage()).matches("Заказ .*: не удается завершить оплату, заказ в статусе \\[ВОЗВРАТ\\]");
        //
        validateOrderState(testOrder.getId(), OrderState.REFUND);
        OrderPaymentState paymentStateOnRefund = testOrderConfig.isAnyPrepayModeSet() || (testOrderConfig.isConciergeOrder() && !testOrderConfig.isExpectingRefundOnDoConfirm())
                ? OrderPaymentState.REFUND_DONE
                : OrderPaymentState.REVERSE_DONE;
        validateOrderPayment(testOrder.getId(), testOrderConfig.paymentsSchema, paymentStateOnRefund);
        List<OperationType> validateOpsTypesList = testOrderConfig.isConciergeOrder() && !testOrderConfig.isExpectingRefundOnDoConfirm()
                ? ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND)
                : testOrderConfig.isAnyPrepayModeSet()
                    ? ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND)
                    : ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE);
        validateBankOperationTypeList(testOrder.getId(), validateOpsTypesList);
        //
        boolean receiptsExistsOnRefund = testOrderConfig.isAnyPrepayModeSet() || (testOrderConfig.isConciergeOrder() && !testOrderConfig.isExpectingRefundOnDoConfirm());
        List<FiscalReceiptRequestType> validateReceiptTypesList = (!receiptsExistsOnRefund)
                ? Collections.emptyList()
                : testOrderConfig.isCrossBordsMode()
                    ? ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE, FiscalReceiptRequestType.CB_ADVANCE_REFUND)
                    : ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_REFUND);
        validateFiscalReceiptsTypeList(testOrder.getId(), validateReceiptTypesList);
        //
        List<BankPaymentDTO> bankPaymentTransferDtos = scheduledBankRunner.transferMoneyToSellers();
        Utils.sleepMillis(1000);
        testCase.rollbackAndStartNewTransaction();
        validateOrderState(testOrder.getId(), OrderState.REFUND);
        //
        List<BankPaymentDTO> bankPaymentVerifyDtos = scheduledBankRunner.checkTransferMoneyToSeller();
        Utils.sleepMillis(1000);
        testCase.rollbackAndStartNewTransaction();
        validateOrderState(testOrder.getId(), OrderState.REFUND);
    }

    private List<FiscalReceiptRequestType> receiptListAfterCharge(OrderFlowTestUtils.TestConfig testOrderConfig) {
        if (testOrderConfig.noReceiptsMode) {
            return Collections.emptyList();
        }
        if (testOrderConfig.isCrossBordsMode()) {
            if (testOrderConfig.isAnyDefectExists()) {
                return ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE, FiscalReceiptRequestType.CB_ADVANCE_ADJUST);
            }
            return ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE);
        }
        if (testOrderConfig.isAgentSeller) {
            return ImmutableList.of(FiscalReceiptRequestType.AGENT_ADVANCE);
        }
        return ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE);
    }

    private List<FiscalReceiptRequestType> receiptListAfterO2B(OrderFlowTestUtils.TestConfig testOrderConfig) {
        if (testOrderConfig.noReceiptsMode) {
            return Collections.emptyList();
        }
        if (testOrderConfig.isCrossBordsMode()) {
            if (testOrderConfig.isAnyDefectExists()) {
                return ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE, FiscalReceiptRequestType.CB_ADVANCE_ADJUST, FiscalReceiptRequestType.CB_PAYMENT);
            }
            return ImmutableList.of(FiscalReceiptRequestType.CB_ADVANCE, FiscalReceiptRequestType.CB_PAYMENT);
        }
        if (testOrderConfig.isAgentSeller) {
            return ImmutableList.of(FiscalReceiptRequestType.AGENT_ADVANCE, FiscalReceiptRequestType.AGENT_PAYMENT);
        }
        return ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT);
    }

    private void callTestAction(TestStopPosition testStopPosition, OrderFlowTestUtils.TestConfig testOrderConfigBase) {
        Optional.ofNullable(testOrderConfigBase.callActionsMap).map(it -> it.get(testStopPosition)).ifPresent(it -> {
            it.accept(testOrderConfigBase);
        });
    }

    public OrderDTO processTestOrderAuthorizeReverseCapture(SpringTestWithDb testCase, OrderFlowTestUtils.TestConfig testOrderConfigBase) {
        OrderFlowTestUtils.TestConfig testOrderConfig = testOrderConfigBase.toBuilder()
                .paymentsSchema(ObjectUtils.defaultIfNull(testOrderConfigBase.paymentsSchema, TcbBankService.SCHEMA))
                .build();
        String paymentsSchema = testOrderConfig.getPaymentsSchema();
        //
        OrderDTO testOrder;
        List<Long> productIdsList = Objects.isNull(testOrderConfig.productIdsList)
                ? getProductsForOrdersWithSellerType(testOrderConfig).stream().map(Product::getId).collect(Collectors.toList())
                : testOrderConfig.productIdsList;
        testCase.commitAndStartNewTransaction();

        String promoCodes = null;
        if (Objects.nonNull(testOrderConfig.getPromoCodeAmounts())) {
            promoCodes = makeTestPromocodes(AbsolutePromoCode.class, UUID.randomUUID().toString(), testOrderConfig.getPromoCodeAmounts());
        }
        if (Objects.nonNull(testOrderConfig.getPromoCodePercent())) {
            promoCodes = makeTestPromocodes(FractionalPromoCode.class, UUID.randomUUID().toString(), testOrderConfig.getPromoCodePercent());
        }
        testCase.commitAndStartNewTransaction();

        if (Objects.nonNull(testOrderConfig.salesRequestId)) {
            ProductsSalesRequest productsSalesRequest = new ProductsSalesRequest();
            productsSalesRequest.setSalesRequestId(testOrderConfig.salesRequestId);
            productsSalesRequest.setProductIds(productIdsList);
            testOrder = createOrderFromApplicationSuccessfullWithCustomClient(apiV2Client, productsSalesRequest, true);
        } else {
            callTestAction(TestStopPosition.CART_INIT, testOrderConfig);
            //
            GroupedCart cartInfo = fillCartWithCurrencyCode(productIdsList, null);
            Assertions.assertThat(cartInfo.getGroups()).hasSize(1);
            //
            if (Objects.nonNull(testOrderConfig.getWithdrawBonusBonuses())) {
                bonusesService.transferBonusesManual(cartTestSupport.getBuyer().getId(), null, testOrderConfig.getWithdrawBonusBonuses(), "", "", null);
            }

            GroupedCart viewCart = getCart(CartRequest.builder().promoCode(promoCodes).sellerId(testOrderConfig.getSellerId()).build());
            if (Objects.nonNull(testOrderConfig.validateCart)) {
                testOrderConfig.validateCart.accept(viewCart);
            }
            //
            long sellerId = viewCart.getGroups().get(0).getSeller().getId();
            viewCart.getGroup(sellerId);
            //
            cartTestSupport.setCartAddressEndpoint();
            if (Objects.nonNull(testOrderConfig.targetDeliveryAepAggregationId)) {
                changeTargetAddressEndpointAggregationCart(testOrderConfig.targetDeliveryAepAggregationId);
            }
            //
            callTestAction(TestStopPosition.CART_FULL, testOrderConfig);
            HoldRequest holdRequestPS = HoldRequest.builder()
                    .promoCode(promoCodes)
                    .paymentSystem(TcbBankService.SCHEMA)
                    .currencyCode(testOrderConfig.payCurrencyISO)
                    .totalBonusesAmount(testOrderConfig.withdrawBonusBonuses)
                    .build();
            HoldRequest holdRequest = Objects.isNull(testOrderConfig.payOptionsType)
                    ? holdRequestPS
                    : holdRequestPS.toBuilder().paymentSystem(null).type(testOrderConfig.payOptionsType).build();
            OrderService.InitOrderResult testOrder1 = cartTestSupport.holdCartWithParams(sellerId, holdRequest);
            testCase.rollbackAndStartNewTransaction();
            //
            testOrder = loadOrderSuccessfull(testOrder1.getOrderId(), true);
            validateOrderState(testOrder.getId(), OrderState.HOLD_PROCESSING);
            if (testOrderConfig.getStopPosition() == TestStopPosition.HOLD_PROCESSING) {
                return testOrder;
            }
            //
            OrderPayment orderPaymentAuthInit = validateOrderPayment(testOrder.getId(), paymentsSchema, OrderPaymentState.AUTHORIZE_INPROGRESS);
            Assertions.assertThat(orderPaymentAuthInit.getCartInfo()).isNotNull();
            Assertions.assertThat(ObjectUtils.defaultIfNull(testOrderConfig.getOrderPayAuthAmount(), orderPaymentAuthInit.getAmountInBase()))
                    .isEqualByComparingTo(orderPaymentAuthInit.getAmountInBase());
            Assertions.assertThat(ObjectUtils.defaultIfNull(testOrderConfig.getDeliveryCost(), testOrder.getDeliveryCost()))
                    .isEqualByComparingTo(testOrder.getDeliveryCost());
            validateBankOperationTypeList(testOrder.getId(), ImmutableList.of(OperationType.HOLD));
            //
            if (isPaymentService(paymentsSchema)) {
                processPsPayment(testOrder.getId(), OrderPaymentState.AUTHORIZE_DONE);
                testCase.commitAndStartNewTransaction();
            } else {
                callOrderHoldCallback(testOrder1.getOrderId(), testOrder1.getBank_url().replace("https://", "http://"));
                testCase.rollbackAndStartNewTransaction();
            }
            //
            validateOrderState(testOrder.getId(), OrderState.HOLD);
            validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD));
            //
            OrderPayment orderPaymentAuthDone = validateOrderPayment(testOrder.getId(), paymentsSchema, OrderPaymentState.AUTHORIZE_DONE);
            boolean skipExpireValidation = isPaymentService(paymentsSchema) || orderService.getBankAccountService(paymentsSchema) instanceof BoutiqueBankService;
            if (!skipExpireValidation) {
                Assertions.assertThat(orderPaymentAuthDone.getAuthorizeExpireTime()).isNotNull();
            }
            callInTransaction.runInNewTransaction(() -> this.mockSwiftPeopleApprovePayoutRequest(testOrder.getId()));
        }
        //
        Optional.ofNullable(testOrderConfig.getLegalEntityOnecId()).ifPresent((it) -> {
            AdminPanelOrderDetailsDTO adminOrderHoldInfo = loadAdminOrderSuccessful(apiV2Client, testOrder.getId(), false);
            Assertions.assertThat(adminOrderHoldInfo.getOrderInfo().getLegalEntity().getOnecUuid()).isEqualTo(it);
        });
        //
        testOrder.getItems().forEach(op -> validateOrderPositions(testOrder.getId(), op.getProductId(), it -> {
            if (it.getCalcMode() == OrderPositionCalcMode.STICK_PAYOUT) {
                Assertions.assertThat(it.getAmount()).isEqualByComparingTo(it.getSellerPayoutAmountRaw()
                    .add(it.getMarketplaceCommissionAmountRaw())
                    .add(it.getDutiesExtraAmount()));
            } else {
                Assertions.assertThat(it.getAmount()).isGreaterThan(it.getSellerPayoutAmountRaw());
            }
        }));
        //
        if (testOrderConfig.getStopPosition() == TestStopPosition.HOLD) {
            return testOrder;
        }
        //
        callTestAction(TestStopPosition.HOLD, testOrderConfig);
        //
        if (Objects.nonNull(testOrderConfig.sellerCounterpartyId)) {
            changeSellerCounterparty(testOrder.getId(), testOrderConfig.sellerCounterpartyId, HttpStatus.Series.SUCCESSFUL);
        }
        //
        if (testOrderConfig.isPrepayBefOrderConfirm()) {
            executePrepayStep(testCase, testOrder);
        }
        //
        List<Product> confList = productIdsList.stream().map(productRepository::getOne).collect(Collectors.toList());
        int confirmedPositionsLeft = confirmRefuseOrderPositions(testOrder, confList, testOrderConfig);
        if (confirmedPositionsLeft == 0) {
            processTestOrderAuthorizeReverseCaptureRefund(testCase, testOrderConfig, testOrder);
            return testOrder;
        }
        if (testOrderConfig.getStopPosition() == TestStopPosition.ALL_POSITIONS_CONFIRMATION_DONE) {
            return testOrder;
        }
        //
        changeAddressEndpoint(testOrder.getId(), testOrderConfig.getPickupDeliveryAepId(), testOrderConfig.getTargetDeliveryAepId());
        if (Objects.nonNull(testOrderConfig.getTargetDeliveryAepAggregationId())) {
            changeTargetAddressEndpointAggregation(testOrder.getId(),  testOrderConfig.getTargetDeliveryAepAggregationId());
        }
        //
        ResponseEntity<String> deliveryStateChangesFail = changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, false);
        Assertions.assertThat(deliveryStateChangesFail.getStatusCode().is4xxClientError()).isTrue();
        Exception deliveryStateChangesInfo = readExceptionFromText(deliveryStateChangesFail.getBody());
        Assertions.assertThat(deliveryStateChangesInfo.getMessage()).matches("Order .*: unable to change delivery state null to OURSELVES_FROM_SELLER_TO_OFFICE");
        //
        if (testOrderConfig.isConciergeOrder()) {
            adminPanel_paymentToConcierge(testOrder.getId(), true);
            testCase.rollbackAndStartNewTransaction();
            //
            processHoldComplete(testOrder);
            testCase.commitAndStartNewTransaction();
            //
            prepareSellerPayout(testOrder.getId(), true);
            testCase.commitAndStartNewTransaction();
            //
            if (testOrderConfig.getStopPosition() == TestStopPosition.PAYOUT_TRANSFER_MONEY_TO_SELLER) {
                return testOrder;
            }
            //
            List<BankPaymentDTO> bankPaymentTransferDtos = scheduledBankRunner.transferMoneyToSellers();
            Utils.sleepMillis(1000);
            testCase.rollbackAndStartNewTransaction();
            //
            if (testOrderConfig.getStopPosition() == TestStopPosition.PAYOUT_VALIDATE_MONEY_TO_SELLER) {
                return testOrder;
            }
            //
            List<BankPaymentDTO> bankPaymentVerifyDtos = scheduledBankRunner.checkTransferMoneyToSeller();
            Utils.sleepMillis(1000);
            testCase.rollbackAndStartNewTransaction();
            //
            transferAgentPaymentsMoneyToSellers();
            testCase.commitAndStartNewTransaction();
        }
        //
        validateOrderState(testOrder.getId(), OrderState.HOLD);
        if (testOrderConfig.getStopPosition() == TestStopPosition.DELIVERY_S2O_CALL) {
            return testOrder;
        }
        //
        ResponseEntity<String> dlvO2BResp = takeOurselves(testOrder.getId(), null);
        Assertions.assertThat(dlvO2BResp.getStatusCode().is2xxSuccessful()).isTrue();
        //
        changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        ResponseEntity<String> refundAmountFailText = adminPanel_refundAmount(testOrder.getId(), BigDecimal.valueOf(1_00L, 2), "refundAmountFail", false);
        Assertions.assertThat(refundAmountFailText.getStatusCode().is4xxClientError()).isTrue();
        Exception refundAmountFailInfo = readExceptionFromText(refundAmountFailText.getBody());
        Assertions.assertThat(refundAmountFailInfo.getMessage()).matches("Order .*: unable to call refundAmount with states \\[HOLD\\]");
        //
        if (testOrderConfig.getStopPosition() == TestStopPosition.EXPERTISE_START) {
            return testOrder;
        }
        List<Product> expsList = productIdsList.stream().map(productRepository::getOne).collect(Collectors.toList());
        int itemsLeftExpertiseStep = processExpertiseSteps(testOrder, expsList, testOrderConfig);
        testCase.rollbackAndStartNewTransaction();
        //
        if (itemsLeftExpertiseStep == 0) {
            processTestOrderAuthorizeReverseCaptureRefund(testCase, testOrderConfig, testOrder);
            return testOrder;
        }
        //
        if (testOrderConfig.getStopPosition() == TestStopPosition.EXPERTISE_JUST_DONE) {
            return testOrder;
        }
        //
        ResponseEntity<String> chargeResp = adminPanel_Charge(testOrder.getId());
        Assertions.assertThat(chargeResp.getStatusCode().is2xxSuccessful()).isTrue();
        testCase.rollbackAndStartNewTransaction();
        if (!testOrderConfig.isAnyPrepayModeSet() && !testOrderConfig.isConciergeOrder()) {
            validateOrderState(testOrder.getId(), OrderState.HOLD_COMPLETED);
            validateOrderPayment(testOrder.getId(), paymentsSchema, OrderPaymentState.CAPTURE_INPROGRESS);
            //
            processHoldComplete(testOrder);
        }
        testCase.rollbackAndStartNewTransaction();
        validateOrderState(testOrder.getId(), OrderState.MONEY_TRANSFERRED);
        //
        testOrder.getItems().forEach(op -> validateOrderPositions(testOrder.getId(), op.getProductId(), it -> {
            if (it.getCalcMode() == OrderPositionCalcMode.STICK_PAYOUT) {
                Assertions.assertThat(it.getAmount()).isEqualByComparingTo(it.getSellerPayoutAmountNet()
                        .add(it.getMarketplaceCommissionAmountNet())
                        .add(it.getDutiesExtraAmount()));
            } else {
                Assertions.assertThat(it.getAmount()).isGreaterThan(it.getSellerPayoutAmountNet());
            }
        }));
        //
        boolean willRefundExistAfterCharge = testOrderConfig.isExpectingMoneyReturn()
                && (testOrderConfig.isAnyPrepayModeSet() || testOrderConfig.isConciergeOrder());
        if (willRefundExistAfterCharge) {
            processOrderPayment(testOrder, OrderPaymentState.REFUND_DONE);
            testCase.rollbackAndStartNewTransaction();
        }
        OrderPayment orderPaymentCaptures = validateOrderPayment(testOrder.getId(), paymentsSchema,
                willRefundExistAfterCharge ? OrderPaymentState.REFUND_DONE : OrderPaymentState.CAPTURE_DONE);
        //
        Assertions.assertThat(ObjectUtils.defaultIfNull(testOrderConfig.getOrderPayCaptAmount(), orderPaymentCaptures.getCaptureAmountInBase()))
                .isEqualByComparingTo(orderPaymentCaptures.getCaptureAmountInBase());
        //
        validateFiscalReceiptsTypeList(testOrder.getId(), receiptListAfterCharge(testOrderConfig));
        //
        OrderDTO orderAfterCharge = loadOrderSuccessfull(testOrder.getId(), true);
        List<OperationType> validateOperationsList = Lists.newArrayList(OperationType.HOLD);
        if (testOrderConfig.isExpectingMoneyReturn() && !willRefundExistAfterCharge) {
            validateOperationsList.add(OperationType.HOLD_REVERSE);
        }
        validateOperationsList.add(OperationType.HOLD_COMPLETE);
        if (testOrderConfig.isConciergeOrder()) {
            validateOperationsList.add(OperationType.SELLER_PAYOUT);
        }
        if (willRefundExistAfterCharge) {
            validateOperationsList.add(OperationType.REFUND);
        }
        validateBankOperationTypeList(testOrder.getId(), validateOperationsList);
        //
        sendOurselves(testOrder.getId(), null);
        testCase.rollbackAndStartNewTransaction();
        //
        changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        if (testOrderConfig.getStopPosition() == TestStopPosition.SPLIT) {
            return testOrder;
        }
        validateFiscalReceiptsTypeList(testOrder.getId(), receiptListAfterO2B(testOrderConfig));
        if (testOrderConfig.getStopPosition() == TestStopPosition.DELIVERY_O2B_DONE) {
            return testOrder;
        }
        //
        if (testOrderConfig.getStopPosition() == TestStopPosition.SEND_AGENT_REPORT) {
            return testOrder;
        }
        sendAgentReport(testOrder.getId(), true);
        testCase.rollbackAndStartNewTransaction();
        //
        if (testOrderConfig.getStopPosition() == TestStopPosition.CONFIRM_AGENT_REPORT) {
            return testOrder;
        }
        confirmAgentReport(testOrder.getId(), true);
        testCase.rollbackAndStartNewTransaction();
        //
        if (!testOrderConfig.noAgentReportCompare) {
            validateSaleReportRu(testCase, testOrder.getId());
        }
        //
        prepareSellerPayout(testOrder.getId(), true);
        testCase.commitAndStartNewTransaction();
        //
        validateSellerPayout(testOrder.getId(), testOrderConfig.getSellerPayoutAmount(), testOrderConfig.getSellerPayoutCurrencyCode(), paymentsSchema, TransactionState.PREPARED);
        //
        if (testOrderConfig.getStopPosition() == TestStopPosition.PAYOUT_TRANSFER_MONEY_TO_SELLER) {
            return testOrder;
        }
        List<BankPaymentDTO> bankPaymentTransferDtos = scheduledBankRunner.transferMoneyToSellers();
        Utils.sleepMillis(1000);
        testCase.rollbackAndStartNewTransaction();
        validateOrderState(testOrder.getId(), OrderState.MONEY_PAYMENT_WAIT);
        //
        if (testOrderConfig.getStopPosition() == TestStopPosition.PAYOUT_VALIDATE_MONEY_TO_SELLER) {
            return testOrder;
        }
        List<BankPaymentDTO> bankPaymentVerifyDtos = scheduledBankRunner.checkTransferMoneyToSeller();
        Utils.sleepMillis(1000);
        testCase.rollbackAndStartNewTransaction();
        //
        transferAgentPaymentsMoneyToSellers();
        testCase.commitAndStartNewTransaction();
        //
        validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        validateSellerPayout(testOrder.getId(), testOrderConfig.getSellerPayoutAmount(), testOrderConfig.getSellerPayoutCurrencyCode(), paymentsSchema, TransactionState.DONE);
        //
        if (!Objects.isNull(testOrderConfig.getRefundAfterCompleteAmount())) {
            String refundComments = testCase.getClass().getSimpleName() + "-" + UUID.randomUUID().toString();
            //
            adminPanel_refundAmount(testOrder.getId(), BigDecimal.valueOf(testOrderConfig.getRefundAfterCompleteAmount(), 2), refundComments, true);
            testCase.rollbackAndStartNewTransaction();
            //
            BankOperation refundOp = validateBankOperation(testOrder.getId(), OperationType.REFUND, testOrderConfig.getRefundAfterCompleteAmount());
            Assertions.assertThat(refundOp.getComment()).isEqualTo(refundComments + " (о)");
            Assertions.assertThat(refundOp.getApiSentAmount()).isEqualTo(testOrderConfig.getRefundAfterCompleteAmount());
        }
        //
        AdminPanelOrderDetailsDTO adminOrderInfo = loadAdminOrderSuccessful(apiV2Client, testOrder.getId(), false);
        validateAdminOrderInfo(testOrder.getId(), adminOrderInfo, testOrderConfig);
        //
        if (testOrderConfig.stopPosition == TestStopPosition.COMPLETED) {
            return testOrder;
        }
        //
        returnCompletedOrSoldOrder(testOrder.getId(), true, true, null);
        //
        return testOrder;
    }

    private void validateAdminOrderInfo(long ordersId, AdminPanelOrderDetailsDTO adminOrderInfo, OrderFlowTestUtils.TestConfig testOrderConfig) {
        BigDecimal payoutByPositions = adminOrderInfo.getProducts().getList().stream()
                .map(it -> ObjectUtils.firstNonNull(it.getOrderPositionSellerAmountPayoutNet(), BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Order order = orderService.getOrder(ordersId);
        Assertions.assertThat(payoutByPositions).isEqualByComparingTo(order.getAgentReport().getPaymentAmount());
    }

    public long prepareAgentSellerData(long agentSellerCounterpartyId) {
        Counterparty counterparty = counterpartyService.findById(agentSellerCounterpartyId);
        counterparty.setContractTime(LocalDateTime.now());
        counterparty.setContractNumber("CONTRACT-ID");
        counterparty.setVatRateIndex(2);
        counterpartyService.save(counterparty);
        adjustUserBalanceToValue(counterparty.getUser().getId(), BigDecimal.ZERO);
        return counterparty.getId();
    }

    public long prepareUsualSellerData(long usualSellerCounterpartyId) {
        Counterparty counterparty = counterpartyService.findById(usualSellerCounterpartyId);
        counterpartyService.save(counterparty);
        adjustUserBalanceToValue(counterparty.getUser().getId(), BigDecimal.ZERO);
        return counterparty.getId();
    }


    public List<Long> createProducts(long sellerId, int amount, int count) {
        List<Product> products = new ArrayList<>();
        List<Long> productsId = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            User seller = userService.getOne(sellerId);
            Product product = new Product();
            product.setBrand(brandRepository.getOne(321L));
            product.setCategoryId(401L);
            product.setSeller(seller);
            product.setSizeType(SizeType.NO_SIZE);
            product.setProductConditionId(1L);
            product.setCurrentPrice(BigDecimal.valueOf(amount));
            product.setStartPrice(BigDecimal.valueOf(amount));
            product.setSalesChannel(SalesChannel.BOUTIQUE_AND_WEBSITE);
            product.setProductState(ProductState.PUBLISHED);
            product = productRepository.saveAndFlush(product);
            products.add(product);
            productsId.add(product.getId());
        }
        for (Product product : products) {
            ProductItem productItem = new ProductItem();
            productItem.setSize(sizeService.fromId(427));
            productItem.setProduct(product);
            productItem.setCurrentPrice(BigDecimal.valueOf(amount));
            productItemService.save(productItem);
        }
        return productsId;
    }

    public List<Long> createDraftProducts(long sellerId, int count) {
        List<Product> products = new ArrayList<>();
        List<Long> productsId = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            Product draftProduct = new Product();
            User seller = userService.getOne(sellerId);
            Product product = new Product();
            product.setBrand(brandRepository.getOne(321L));
            product.setCategoryId(401L);
            product.setSeller(seller);
            product.setSizeType(SizeType.NO_SIZE);
            product.setProductConditionId(1L);
            product.setCurrentPrice(BigDecimal.valueOf(10000));
            product.setStartPrice(BigDecimal.valueOf(10000));
            product.setCurrentPriceWithoutCommission(BigDecimal.valueOf(7500));
            product.setSalesChannel(SalesChannel.STOCK_AND_BOUTIQUE_AND_WEBSITE);
            product.setProductState(ProductState.DRAFT);
            draftProduct = productRepository.saveAndFlush(product);
            products.add(draftProduct);
            productsId.add(draftProduct.getId());
        }
        for (Product product : products) {
            ProductItem productItem = new ProductItem();
            productItem.setSize(sizeService.fromId(427));
            productItem.setProduct(product);
            productItem.setCurrentPrice(BigDecimal.valueOf(10000));
            productItem.setCurrentPriceWithoutCommission(BigDecimal.valueOf(7500));
            productItemService.save(productItem);
        }
        return productsId;
    }

    @SneakyThrows
    public String makeTestPromocodes(Class<? extends PromoCode> promoCodesKind, String codeName, BigDecimal value) {
        PromoCode promoCode = promoCodesKind.newInstance();
        if (promoCode instanceof AbsolutePromoCode) {
            ((AbsolutePromoCode) promoCode).setValue(value);
        }
        if (promoCode instanceof FractionalPromoCode) {
            ((FractionalPromoCode) promoCode).setValue(value);
        }
        promoCode.setCode(codeName)
                .setBeginPrice(new BigDecimal(1))
                .setCreatedAt(ZonedDateTime.now())
                .setExpiresAt(ZonedDateTime.now().plusYears(100))
                .setStartsAt(ZonedDateTime.now().minusDays(1000))
                .setDeleted(false);
        promoCodeRepository.saveAndFlush(promoCode);
        return promoCode.getCode();
    }

    public void validateOrderPositions(long ordersId, long productsId, Consumer<OrderPosition> validateOp) {
        Order order = orderService.getOrder(ordersId);
        List<OrderPosition> validateList = order.getOrderPositions().stream()
                .filter(it -> (productsId == -1) || (it.getProductItem().getProduct().getId().equals(productsId)))
                .collect(Collectors.toList());
        Assertions.assertThat(validateList).hasSize(1);
        validateOp.accept(validateList.get(0));
    }

    public void validateOrderItems(OrderDTO orderInfo, long productsId, Consumer<OrderPositionDTO> validateOp) {
        List<OrderPositionDTO> validateList = orderInfo.getItems().stream().filter(it -> productsId == it.getProductId()).collect(Collectors.toList());
        Assertions.assertThat(validateList).hasSize(1);
        validateOp.accept(validateList.get(0));
    }

    public List<UserBalanceChange> loadBalanceChanges(String objectId, long userId) {
        return userBalanceService.getUserBalanceHistoryWithObject(userId, objectId);
    }


    public void changeToSimpleUserNoCB(long userid) {
        userService.getUserById(userid).ifPresent(u -> {
            u.setSaleShipmentRoute(null);
            userService.save(u);
        });
    }

    private void mockSwiftPeopleApprovePayoutRequest(Long orderId) {
        boolean isCrossBorder = orderExtraPropsService.hasOrderExtraProp(orderId, OrderExtraPropInfo.ORDER_EXTRA_PROP_ORDER_TAG_CROSSBORDER);
        if (isCrossBorder) {
            payoutRequestRepository.findByOrderIdAndAgentName(orderId, PayoutRequestAgentName.SWIFT_PEOPLE)
                    .ifPresent(pr -> {
                        pr.setStatus(PayoutRequestStatus.APPROVED);
                        payoutRequestRepository.save(pr);
                    });
        }
    }

    @Getter
    @Builder(toBuilder = true)
    public static class OskellyDeliveryInfo {
        private String courierName;
        private String courierPhone;
        private LocalDate courierDate;
        private boolean callWillFail;
    }

    @Getter
    @ToString
    @Builder(toBuilder = true)
    public static class FindProduct4Test {
        private Long sellerId;
        private boolean forBoutique;
        private boolean forConcierge;
        private int maxItems;
    }

    @Getter
    @Builder(toBuilder = true)
    public static class TestConfig {
        private final int itemsCount;
        private final List<Long> productIdsList;
        private final String paymentsSchema;
        private final String salesRequestId;
        private final String payOptionsType;
        private final String payCurrencyISO;
        //
        private final boolean isUsualSeller;
        private final boolean isAgentSeller;
        private final boolean noReceiptsMode;
        private final boolean conciergeOrder;
        private final boolean crossBordsMode;
        private final boolean noAgentReportCompare;
        private final Map<TestStopPosition, Consumer<TestConfig>> callActionsMap;
        //
        private final Long agentSellerId;
        private final Long usualSellerId;
        private final Long sellerCounterpartyId;
        //
        private final Long pickupDeliveryAepId;
        private final Long targetDeliveryAepId;
        private final Long targetDeliveryAepAggregationId;
        //
        private final List<Integer> confirmPositions;
        private final List<Integer> refusePositions;
        //
        private final boolean prepayBefOrderConfirm;
        private final boolean prepayAftOrderConfirm;
        private final boolean prepayAfterExpertises;
        private final boolean payoutAftOrderConfirm;
        //
        private final BigDecimal promoCodeAmounts;
        private final BigDecimal promoCodePercent;
        //
        private final List<Integer> expertisePassPositions;
        private final List<Integer> expertiseFailPositions;
        private final List<Long> defectsByPositions;
        private final List<Long> cleaningsByPositions;

        private final BigDecimal orderPayAuthAmount;
        private final BigDecimal orderPayCaptAmount;
        private final Long sellerPayoutAmount;
        private final String sellerPayoutCurrencyCode;
        private final BigDecimal deliveryCost;
        private final BigDecimal withdrawBonusBonuses;

        private final Long refundAfterCompleteAmount;

        private final String failText;
        private final TestStopPosition stopPosition;
        private final Consumer<GroupedCart> validateCart;
        private final ComboExpertiseMode comboExpertiseMode;
        private final String legalEntityOnecId;

        public boolean isExpectingRefundOnDoConfirm() {
            return confirmPositions.isEmpty();
        }

        public boolean isExpectingRefundOnExpertise() {
            return CollectionUtils.isNotEmpty(expertiseFailPositions) && expertiseFailPositions.containsAll(confirmPositions);
        }

        public boolean isRefusalsMoneyReturnAsReverse() {
            return isAnyRefuseExists() && (!prepayBefOrderConfirm);
        }

        public boolean isRefusalsMoneyReturnAsRefunds() {
            return isAnyRefuseExists() && (prepayBefOrderConfirm);
        }

        public boolean isDefectsMoneyReturnAsRefunds() {
            return (isAnyDefectExists() || isAnyRejectExists()) &&
                    (isAnyPrepayModeSet() || isPayoutAftOrderConfirm());
        }

        public boolean isAnyPrepayModeSet() {
            return prepayBefOrderConfirm || prepayAftOrderConfirm;
        }

        public boolean isExpectingMoneyReturn() {
            if (isAnyRefuseExists() || isAnyRejectExists()) {
                return true;
            }
            if (isAnyDefectExists() && isAnyCleaningExists()) {
                return ImmutableSet.of(ComboExpertiseMode.SERVICE_THEN_DEFECTS, ComboExpertiseMode.DEFECTS_WITH_SERVICE).contains(comboExpertiseMode);
            }
            return isAnyDefectExists();
        }

        public boolean isAnyRefuseExists() {
            return CollectionUtils.isNotEmpty(refusePositions);
        }

        public boolean isAnyDefectExists() {
            return CollectionUtils.isNotEmpty(defectsByPositions);
        }

        public boolean isAnyCleaningExists() {
            return CollectionUtils.isNotEmpty(cleaningsByPositions);
        }

        public boolean isAnyRejectExists() {
            return CollectionUtils.isNotEmpty(expertiseFailPositions);
        }

        public long getSellerId() { return ObjectUtils.firstNonNull(usualSellerId, agentSellerId); }

    }

    public enum TestStopPosition {
        CART_INIT,
        CART_FULL,
        HOLD_PROCESSING,
        HOLD,
        ALL_POSITIONS_CONFIRMATION_DONE,
        DELIVERY_S2O_CALL,
        EXPERTISE_START,
        EXPERTISE_JUST_DONE,
        EXPERTISE_CALC_DONE,
        DELIVERY_O2B_DONE,
        SEND_AGENT_REPORT,
        CONFIRM_AGENT_REPORT,
        PAYOUT_TRANSFER_MONEY_TO_SELLER,
        PAYOUT_VALIDATE_MONEY_TO_SELLER,
        COMPLETED,
        SPLIT
    }

    public enum ComboExpertiseMode {
        DEFECTS_THEN_SERVICE,
        SERVICE_THEN_DEFECTS,
        DEFECTS_WITH_SERVICE
    }

}
