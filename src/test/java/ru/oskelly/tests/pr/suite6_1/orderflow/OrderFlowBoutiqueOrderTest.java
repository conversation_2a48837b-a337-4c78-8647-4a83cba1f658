package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2ParseException;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.CartControllerV2Test;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.component.CartTestSupport2nd;
import su.reddot.component.HoldRequest;
import su.reddot.domain.dao.order.OrderRepository;
import su.reddot.domain.model.addressendpoint.AddressEndpoint;
import su.reddot.domain.model.banktransaction.BankOperation;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderExtraPropInfo;
import su.reddot.domain.model.order.OrderExtraPropValue;
import su.reddot.domain.model.order.OrderPayment;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.order.OrderPosition;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.model.order.OrderSourceInfo;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.product.SalesChannel;
import su.reddot.domain.model.stock.Stock;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.boutique.BoutiqueService;
import su.reddot.domain.service.commission.CommissionService;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.dto.BankPaymentDTO;
import su.reddot.domain.service.dto.ProductDTO;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.dto.order.OrderSourceDTO;
import su.reddot.domain.service.dto.order.ProductLocationDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.order.impl.OrderExtraPropsService;
import su.reddot.domain.service.ordersourceinfo.OrderSourceInfoService;
import su.reddot.domain.service.product.ProductService;
import su.reddot.domain.service.product.item.ProductItemService;
import su.reddot.domain.service.stock.StockService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.BoutiqueBankService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.impl.tcb.TcbBankClient;
import su.reddot.infrastructure.bank.jobs.ScheduledBankRunner;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.infrastructure.util.Utils;
import su.reddot.presentation.api.v2.Api2Response;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Slf4j
@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class OrderFlowBoutiqueOrderTest extends OrderFlowTest {

    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private UserService userService;
    @Autowired
    private CounterpartyService counterpartyService;
    @Autowired
    private ProductService productService;
    @Autowired
    private ProductItemService productItemService;
    @Autowired
    private BoutiqueService boutiqueService;
    @Autowired
    private CommissionService commissionService;
    @Autowired
    private ScheduledBankRunner scheduledBankRunner;

    @Autowired
    private CallInTransaction callInTransaction;
    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private OrderFlowTestFixtures orderFlowTestFixtures;
    @Autowired
    private CartTestSupport cartTestSupportBoutiqueOrder;
    @Autowired
    @Qualifier("cartTestSupport2nd")
    private CartTestSupport2nd cartTestSupportPlatformOrder;


    @Value("${test-boutique.buyer-id}")
    private Long boutiqueBuyerId;
    private final String boutiqueBuyerPassword = "password4boutique";

    @Value("${test.api.user-id}")
    private Long platformBuyerId;
    @Value("${test.api.user-password}")
    private String platformBuyerPassword;


    @Value("${test-prepayments.usual-seller-id}")
    private Long sellerId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;

    private ApiV2Client apiV2ClientPlatform;

    @Autowired
    private OrderSourceInfoService orderSourceInfoService;

    @Autowired
    private StockService stockService;

    private User boutiqueUser;

    private OrderSourceInfo orderSourceInfo;
    private Stock stock;

    private Long prepareUsualSellerData() {
        Counterparty counterparty = counterpartyService.findById(usualSellerCounterpartyId);
        counterparty.setPaymentAccount("*************.......");
        counterpartyService.save(counterparty);
        orderFlowTestUtils.adjustUserBalanceToValue(counterparty.getUser().getId(), BigDecimal.ZERO);
        return counterparty.getId();
    }

    private User preparePlatformUser() {
        return userService.getUserById(platformBuyerId).orElseThrow(IllegalArgumentException::new);
    }

    @PostConstruct
    private void init() {
        boutiqueUser = callInTransaction.runInNewTransaction(() -> orderFlowTestFixtures.prepareBoutiqueUser(boutiqueBuyerId, boutiqueBuyerPassword));
        orderSourceInfo = orderSourceInfoService.findOrderSourceInfoByName(OrderSourceInfo.ORDER_SOURCE_INFO_MOVING_TO_BOUTIQUE).get();

        orderFlowTestUtils.init(boutiqueUser.getEmail(), boutiqueBuyerPassword);
        //
        ApiV2Client apiV2ClientBoutique = new ApiV2Client(boutiqueUser.getEmail(), boutiqueBuyerPassword);
        cartTestSupportBoutiqueOrder.setUserId(boutiqueUser.getId());
        cartTestSupportBoutiqueOrder.setApiV2Client(apiV2ClientBoutique);
        cartTestSupportBoutiqueOrder.getDeliveryAddressEndpoint();
        //
        User platformBuyer = callInTransaction.runInNewTransaction(this::preparePlatformUser);
        apiV2ClientPlatform = new ApiV2Client(platformBuyer.getEmail(), platformBuyerPassword);
        cartTestSupportPlatformOrder.setUserId(platformBuyer.getId());
        cartTestSupportPlatformOrder.setApiV2Client(apiV2ClientPlatform);
        cartTestSupportPlatformOrder.getDeliveryAddressEndpoint();
        //
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        usualSellerCounterpartyId = callInTransaction.runInNewTransaction(this::prepareUsualSellerData);
        //
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
    }

    private void validateInStorePickUpFails(long orderIds) {
        ResponseEntity<String> inStoreFailData = orderFlowTestUtils.adminPanel_setInStorePickUp(orderIds, true, false);
        Exception inStoreFailInfo = orderFlowTestUtils.readExceptionFromText(inStoreFailData.getBody());
        Assertions.assertThat(inStoreFailInfo.getMessage()).matches("Заказ .*: флаг самовывоз не поддерживается в заказах бутика");
    }

    private OrderDTO _00_OrderFlow_Boutique_MoveItemsToBoutique(List<Product> products, boolean isPassingExpertiseStep, long holdAmount, String moveToOrdersSource) {
        Map<Long, Integer> productItemsCountOnStart = orderFlowTestUtils.getProductCountByProductItem(products.get(0).getId());
        //
        orderFlowTestUtils.fillCart(products);
        //
        cartTestSupportBoutiqueOrder.setCartAddressEndpoint();
        //
        OrderService.InitOrderResult initOrder = cartTestSupportBoutiqueOrder.holdCartWithParams(products.get(0).getSeller().getId(), null);
        Assertions.assertThat(initOrder.getPaymentSystem()).isEqualTo(BoutiqueBankService.BOUTIQUE_SCHEMA);
        rollbackAndStartNewTransaction();
        //
        OrderSourceInfo orderSourceInfoValue = orderSourceInfoService.findOrderSourceInfoByName(moveToOrdersSource).get();
        stock = stockService.findStocksByOrderSourceInfo(orderSourceInfoValue).get(0);
        //
        OrderDTO testOrder = orderFlowTestUtils.loadOrderSuccessfull(initOrder.getOrderId(), true);
        Assertions.assertThat(testOrder.getDeliveryCost()).isEqualByComparingTo(BigDecimal.ZERO);
        //
        Order orderInfo = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD_PROCESSING);
        Assertions.assertThat(orderInfo.getOrderSource()).isEqualTo(OrderSource.BOUTIQUE);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, holdAmount + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD));
        //
        orderFlowTestUtils.callOrderHoldCallback(initOrder.getOrderId(), initOrder.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD));
        BankOperation bankOperationHoldDone = orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, holdAmount + testOrder.getDeliveryCost().movePointRight(2).longValue());
        Assertions.assertThat(bankOperationHoldDone.getExpireTime()).isNull();
        //
        validateInStorePickUpFails(testOrder.getId());
        //
        testOrder.getItems().forEach(op -> orderFlowTestUtils.rejectOrApprovePosition(op.getId(), true));
        //
        ResponseEntity<String> soFailResponseOne = orderFlowTestUtils.sellOrderInBoutique(testOrder.getId(), false, stock.getOnecUuid());
        Api2Response<String> soErrorInfoOne = orderFlowTestUtils.getRawApi2Response(soFailResponseOne.getBody());
        Assertions.assertThat(soErrorInfoOne.getMessage()).matches("Заказ .*: не удается оформить продажу в бутике, некорректное состояние доставки заказа null");
        //
        validateInStorePickUpFails(testOrder.getId());
        //
        orderFlowTestUtils.changeSellerCounterparty(testOrder.getId(), usualSellerCounterpartyId, HttpStatus.Series.SUCCESSFUL);
        orderFlowTestUtils.changeAddressEndpoint(testOrder.getId(), pickupId, deliveryId);
        orderFlowTestUtils.takeOurselves(testOrder.getId(), null);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        if (!isPassingExpertiseStep) {
            return testOrder;
        }
        //
        for (OrderPositionDTO orderPosition : testOrder.getItems()) {
            orderFlowTestUtils.adminsApi1expertise(orderPosition.getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_PASSED_OK, null);
        }
        //
        orderFlowTestUtils.adminPanel_Charge(testOrder.getId());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD_COMPLETED);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, holdAmount + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE));
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Collections.emptyList());
        //
        orderFlowTestUtils.processHoldComplete(testOrder);
        rollbackAndStartNewTransaction();
        //
        Order orderInBoutique = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_TRANSFERRED);
        Assertions.assertThat(orderInBoutique.getDeliveryState()).isEqualTo(DeliveryState.DELIVERED_TO_BUYER);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Collections.emptyList());
        //
        Map<Long, Integer> productItemsCountWhenInBoutique = orderFlowTestUtils.getProductCountByProductItem(products.get(0).getId());
        Assertions.assertThat(productItemsCountWhenInBoutique).isEqualTo(productItemsCountOnStart); // Item is still available on platform
        //
        orderFlowTestUtils.adminsApi1SetOrderSourceInfo(testOrder.getId(), orderSourceInfoValue.getId(), true);
        rollbackAndStartNewTransaction();
        //
        OrderDTO orderWithOrderSourceInfo = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(orderWithOrderSourceInfo.getOrderSourceInfo().getId()).isEqualTo(orderSourceInfoValue.getId());
        //
        orderFlowTestUtils.checkAdminV1OrderInfo(testOrder.getId());
        //
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _01_OrderFlow_Boutique_HappyPathOkay() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        long productId = products.get(0).getId();
        Map<Long, Integer> productItemsCountOnStart = orderFlowTestUtils.getProductCountByProductItem(productId);
        Integer totalAmountOnStart = productItemsCountOnStart.values().stream().reduce(0, Integer::sum);
        //
        OrderDTO testOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true,
                10_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        Assertions.assertThat(testOrder.getOrderSource()).isEqualTo(OrderSourceDTO.BOUTIQUE);
        Assertions.assertThat(testOrder.getOrderSourceInfo().getName()).isEqualTo(OrderSourceInfo.ORDER_SOURCE_INFO_MOVING_TO_BOUTIQUE);
        Assertions.assertThat(testOrder.getProductLocation()).isEqualTo(ProductLocationDTO.SELLER);
        Assertions.assertThat(testOrder.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
        Order order = orderRepository.getOne(testOrder.getId());
        Assertions.assertThat(OrderExtraPropsService.isOrderTagExists(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_ORDER_TAG_BOUTIQUES)).isTrue();
        //
        ResponseEntity<String> arFailResponse = orderFlowTestUtils.sendAgentReport(testOrder.getId(), false);
        Api2Response<String> arErrorInfo = orderFlowTestUtils.getRawApi2Response(arFailResponse.getBody());
        Assertions.assertThat(arErrorInfo.getMessage()).matches("Заказ .*: не удается отправить отчет комиссионера, флаг 'продан' не установлен");
        //
        orderFlowTestUtils.sellOrderInBoutique(testOrder.getId(), true, stock.getOnecUuid());
        ResponseEntity<String> soFailResponse = orderFlowTestUtils.sellOrderInBoutique(testOrder.getId(), false, stock.getOnecUuid());
        Api2Response<String> soErrorInfo = orderFlowTestUtils.getRawApi2Response(soFailResponse.getBody());
        Assertions.assertThat(soErrorInfo.getMessage()).matches("Заказ .*: не удается оформить продажу в бутике, заказ был продан в бутике ранее");
        //
        orderFlowTestUtils.sendAgentReport(testOrder.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.validateAgentReportText(testOrder.getId(),
                "Оплата по отчету комиссионера за .* \\(артикул .*\\) комитенту id .*. НДС не облагается.");
        //
        orderFlowTestUtils.confirmAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        Map<Long, Integer> productItemsCountAfterSell = orderFlowTestUtils.getProductCountByProductItem(productId);
        Integer totalAmountAfterSell = productItemsCountAfterSell.values().stream().reduce(0, Integer::sum);
        Assertions.assertThat(totalAmountAfterSell).isEqualTo(totalAmountOnStart - 1);
        //
        orderFlowTestUtils.prepareSellerPayout(testOrder.getId(), true);
        commitAndStartNewTransaction();
        //
        long paymentAmount = 6_500_00;
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), paymentAmount, null, BoutiqueBankService.BOUTIQUE_SCHEMA, TransactionState.PREPARED);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Collections.emptyList());
        //
        List<BankPaymentDTO> bankPaymentTransferDtos = scheduledBankRunner.transferMoneyToSellers();
        Utils.sleepMillis(5000);
        commitAndStartNewTransaction();
        Assertions.assertThat(bankPaymentTransferDtos).isNotEmpty();
        List<BankPaymentDTO> bankPaymentTransferList = bankPaymentTransferDtos.stream().filter(bp -> bp.getOrderId().equals(testOrder.getId())).collect(Collectors.toList());
        Assertions.assertThat(bankPaymentTransferList).hasSize(1);
        BankPaymentDTO bankPaymentTransferDto = bankPaymentTransferList.get(0);
        Assertions.assertThat(bankPaymentTransferDto.getAmountSentViaApi()).isEqualTo(paymentAmount);
        Assertions.assertThat(bankPaymentTransferDto.getOrderState()).isEqualTo(OrderState.MONEY_PAYMENT_WAIT);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_PAYMENT_WAIT);
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), paymentAmount, null, BoutiqueBankService.BOUTIQUE_SCHEMA, TransactionState.INPROGRESS);
        //
        List<BankPaymentDTO> bankPaymentVerifyDtos = scheduledBankRunner.checkTransferMoneyToSeller();
        Utils.sleepMillis(5000);
        commitAndStartNewTransaction();
        Assertions.assertThat(bankPaymentVerifyDtos).isNotEmpty();
        List<BankPaymentDTO> bankPaymentVerifyList = bankPaymentVerifyDtos.stream().filter(bp -> bp.getOrderId().equals(testOrder.getId())).collect(Collectors.toList());
        Assertions.assertThat(bankPaymentVerifyList).hasSize(1);
        BankPaymentDTO bankPaymentVerifyDto = bankPaymentVerifyList.get(0);
        Assertions.assertThat(bankPaymentVerifyDto.getAmountSentViaApi()).isEqualTo(paymentAmount);
        Assertions.assertThat(bankPaymentVerifyDto.getOrderState()).isEqualTo(OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), paymentAmount, null, BoutiqueBankService.BOUTIQUE_SCHEMA, TransactionState.DONE);
        //
        ProductDTO product = orderFlowTestUtils.getProduct(productId).getData();
        Assertions.assertThat(product.getSalesChannel()).isEqualTo(SalesChannel.BOUTIQUE_AND_WEBSITE);
        //
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), BoutiqueBankService.BOUTIQUE_SCHEMA, OrderPaymentState.CAPTURE_DONE);
        Assertions.assertThat(orderPayment.getAuthorizeExpireTime()).isNull();
        //
        Map<String, String> tcbPayoutsInfo = orderFlowTestTcbMock.getOperationInfo(bankPaymentTransferDto.getUuid().toString());
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_TCB_LOGIN)
                .isEqualTo(OrderFlowTestTcbMock.TCB_LOGIN_CONCIERGE);
        //
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ROUTE)
                .isEqualTo(TcbBankClient.API_V1_ROUTE_ACCOUNT_EXTERNAL_CREDIT);
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_AMOUNT)
                .isEqualTo(bankPaymentTransferDto.getAmountSentViaApi().toString());
        Assertions.assertThat(tcbPayoutsInfo.get(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_COMMENTS))
                .matches("Оплата по отчету комиссионера за .* комитенту id 27\\. НДС не облагается\\.");
        //
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_ID)
                .isEqualTo("*************.......");
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_INN)
                .isEqualTo("************");
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_BIC)
                .isEqualTo("*********");
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_NAME)
                .isEqualTo("... Магомед ...");
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _01_01_orderFlow_boutique_happyPathOkay_withPriceChanges() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true, 10_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        rollbackAndStartNewTransaction();
        //
        long salesProductId = testOrder.getItems().stream().findFirst().map(OrderPositionDTO::getProductId).orElse(0L);
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), salesProductId, it -> {
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(3_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(6_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(3_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(6_500_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        rollbackAndStartNewTransaction();
        //
        Product boutiqueProduct = productService.getRawProduct(salesProductId, ProductService.UserType.SYSTEM).orElse(null);
        _XX_OrderFlow_Boutique_SetProductPrice(boutiqueProduct, BigDecimal.valueOf(20_000_00, 2));
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), salesProductId, it -> {
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(10_769_15, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(19_999_85, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(10_769_15, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(19_999_85, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.sellOrderInBoutique(testOrder.getId(), true, stock.getOnecUuid());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.sendAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.confirmAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.prepareSellerPayout(testOrder.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), 19_999_85, null, BoutiqueBankService.BOUTIQUE_SCHEMA, TransactionState.PREPARED);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _02_OrderFlow_Boutique_NoAgentReportWhenNoItemsLeft() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true,
                10_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        commitAndStartNewTransaction();
        //
        products.get(0).getProductItems().forEach(pi -> {
            pi.setCount(0);
            productItemService.save(pi);
        });
        commitAndStartNewTransaction();
        //
        ResponseEntity<String> sellOrderResponse = orderFlowTestUtils.sellOrderInBoutique(testOrder.getId(), false, stock.getOnecUuid());
        Api2Response<String> errorInfo = orderFlowTestUtils.getRawApi2Response(sellOrderResponse.getBody());
        Assertions.assertThat(errorInfo.getMessage()).matches("Заказ .*: товар для продажи отсутствует / закончился .*");
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _03_OrderFlow_Boutique_OnlyOneProductAllowed() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(5)
                .build()
        );
        commitAndStartNewTransaction();
        //
        GroupedCart cartInfo = orderFlowTestUtils.fillCart(products);
        cartInfo.getGroup(products.get(0).getSeller().getId());
        //
        cartTestSupportBoutiqueOrder.setCartAddressEndpoint();
        //
        try {
            cartTestSupportBoutiqueOrder.holdCartWithParams(products.get(0).getSeller().getId(), null);
            Assertions.fail("Unreachable code: must fall with an exception");
        } catch (ApiV2ParseException e) {
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(e.getRawData());
            Assertions.assertThat(errorRsp.getMessage()).matches("Заказ .*: при оформлении заказа в бутик допускается только 1 товар \\(сейчас: 5\\)");
            Assertions.assertThat(errorRsp.getHumanMessage()).matches("Заказ .*: при оформлении заказа в бутик допускается только 1 товар \\(сейчас: 5\\)");
        }
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_01_orderFlow_boutique_sameProductItemOnlyOnce() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products); // We`re able to order them when there are lot of them
        cartTestSupportBoutiqueOrder.setCartAddressEndpoint();
        cartTestSupportBoutiqueOrder.holdCartWithParams(products.get(0).getSeller().getId(), null);
        //
        products.get(0).getProductItems().forEach(pi -> {
            pi.setCount(1);
            productItemService.save(pi);
        });
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products); // But we can`t do that if there`s already orders to BOUTIQUE and no more items left
        cartTestSupportBoutiqueOrder.setCartAddressEndpoint();
        try {
            cartTestSupportBoutiqueOrder.holdCartWithParams(products.get(0).getSeller().getId(), null);
        } catch (ApiV2ParseException e) {
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(e.getRawData());
            Assertions.assertThat(errorRsp.getMessage()).matches("Заказ .*: товар \\d* уже перемещается в бутик в заказах .*");
            Assertions.assertThat(errorRsp.getHumanMessage()).matches("Заказ .*: товар \\d* уже перемещается в бутик в заказах .*");
        }
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _04_02_orderFlow_boutique_mustSpecifySalesChannel() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.fillCart(products);
        cartTestSupportBoutiqueOrder.setCartAddressEndpoint();
        try {
            cartTestSupportBoutiqueOrder.holdCartWithParams(products.get(0).getSeller().getId(), null);
        } catch (ApiV2ParseException e) {
            Api2Response<String> errorRsp = orderFlowTestUtils.getRawApi2Response(e.getRawData());
            Assertions.assertThat(errorRsp.getMessage()).matches("Заказ .*: канал продаж в товаре \\d* для бутика должен быть \"Бутик\", \"Бутик и сайт\" или \"Склад, бутик и сайт\"");
            Assertions.assertThat(errorRsp.getHumanMessage()).matches("Заказ .*: канал продаж в товаре \\d* для бутика должен быть \"Бутик\", \"Бутик и сайт\" или \"Склад, бутик и сайт\"");
        }
    }

    private OrderService.InitOrderResult _XX_OrderFlow_Boutique_CreateOnlineOrder(List<Product> products) {
        CartControllerV2Test.CartAddRequest cartAddRequest = new CartControllerV2Test.CartAddRequest()
                .setProductId(products.get(0).getId())
                .setSizeId(products.get(0).getAvailableProductItems().get(0).getSize().getId())
                .setCount(1);
        cartTestSupportPlatformOrder.cleanCart(true);
        cartTestSupportPlatformOrder.addToCartWithParamsSuccessful(cartAddRequest, true);
        cartTestSupportPlatformOrder.setCartAddressEndpoint();
        return cartTestSupportPlatformOrder.holdCartWithParams(products.get(0).getSeller().getId(),
                HoldRequest.builder().paymentSystem(TcbBankService.SCHEMA).build());
    }

    private void _XX_OrderFlow_Boutique_ConfirmPlatformOrder(long orderId, long boutiqueOrderId, boolean isRefundMode, String boutiqueOrderOrderSource2Set) {
        OrderFlowTestUtils.OskellyDeliveryInfo deliveryInfo = OrderFlowTestUtils.OskellyDeliveryInfo.builder()
                .courierName("S2O-Courier").courierPhone("**********").courierDate(LocalDate.now()).callWillFail(!isRefundMode)
                .build();
        ResponseEntity<String> sendCallData1st = orderFlowTestUtils.takeOurselves(orderId, deliveryInfo);
        if (isRefundMode) {
            Assertions.assertThat(sendCallData1st.getStatusCode().is2xxSuccessful()).isTrue();
            return;
        }

        Exception sendCallFailInfo = orderFlowTestUtils.readExceptionFromText(sendCallData1st.getBody());
        Assertions.assertThat(sendCallFailInfo.getMessage()).matches("Заказ .*: не удается подтвердить онлайн заказ \\(в заказе бутика .* указано расположение \\[.*\\], продажи запрещены\\)");
        //
        orderFlowTestUtils.adminsApi1SetOrderSourceInfo(boutiqueOrderId, orderSourceInfoService.findOrderSourceInfoByName(OrderSourceInfo.ORDER_SOURCE_INFO_OSKELLY_BOUTIQUE02).get().getId(), true);
        Order order = orderRepository.getOne(orderId);
        OrderPosition op = order.getOrderPositions().get(0);
        ResponseEntity<String> failApprove = orderFlowTestUtils.approvePosition(op.getId());

        Long orderSourceInfoId2Set = orderSourceInfoService.findOrderSourceInfoByName(boutiqueOrderOrderSource2Set).map(OrderSourceInfo::getId).orElse(null);
        orderFlowTestUtils.adminsApi1SetOrderSourceInfo(boutiqueOrderId, orderSourceInfoId2Set, true);
        //
        ResponseEntity<String> sendCallData2nd = orderFlowTestUtils.takeOurselves(orderId, deliveryInfo);
        Assertions.assertThat(sendCallData2nd.getStatusCode().is2xxSuccessful()).isTrue();
    }

    private void _05_OrderFlow_Boutique_BoutiqueOrderReturnsOnOnlineConfirmation(boolean isRefundMode) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        OrderDTO boutiqueOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, isRefundMode,
                10_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        Assertions.assertThat(boutiqueOrder.getOrderSource()).isEqualTo(OrderSourceDTO.BOUTIQUE);
        Assertions.assertThat(boutiqueOrder.getProductLocation()).isEqualTo(ProductLocationDTO.SELLER);
        Assertions.assertThat(boutiqueOrder.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
        //
        OrderService.InitOrderResult platformOrderHold = _XX_OrderFlow_Boutique_CreateOnlineOrder(products);
        rollbackAndStartNewTransaction();
        //
        OrderDTO platformOrder = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(apiV2ClientPlatform, platformOrderHold.getOrderId(), true);
        Assertions.assertThat(platformOrder.getOrderSource()).isEqualTo(OrderSourceDTO.ONLINE);
        orderFlowTestUtils.callOrderHoldCallback(platformOrderHold.getOrderId(), platformOrderHold.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        platformOrder = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(apiV2ClientPlatform, platformOrderHold.getOrderId(), true);
        Assertions.assertThat(platformOrder.getProductLocation()).isEqualTo(ProductLocationDTO.BOUTIQUE);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(platformOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderState(boutiqueOrder.getId(), isRefundMode ? OrderState.MONEY_TRANSFERRED : OrderState.HOLD);
        //
        OrderPositionDTO op = platformOrder.getItems().get(0);
        ResponseEntity<String> failApprove = orderFlowTestUtils.approvePosition(op.getId());
        if (!isRefundMode) {
            Assertions.assertThat(failApprove.getBody()).contains("Нет прав для подтверждения. Пожалуйста, переместите заказ в бутик.");
            return;
        }
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(platformOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderState(boutiqueOrder.getId(), isRefundMode ? OrderState.MONEY_TRANSFERRED : OrderState.HOLD);
        //
        _XX_OrderFlow_Boutique_ConfirmPlatformOrder(platformOrder.getId(), boutiqueOrder.getId(), isRefundMode, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        rollbackAndStartNewTransaction();
        //
        if (isRefundMode) {
            orderFlowTestUtils.processBankOperation(boutiqueOrder.getId(), OperationType.REFUND);
            rollbackAndStartNewTransaction();
        }
        //
        orderFlowTestUtils.validateOrderState(platformOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderState(boutiqueOrder.getId(), OrderState.REFUND);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(boutiqueOrder.getId(), Collections.emptyList());
        List<OperationType> operationTypeList = isRefundMode
                ? Arrays.asList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND)
                : Arrays.asList(OperationType.HOLD, OperationType.HOLD_REVERSE);
        orderFlowTestUtils.validateBankOperationTypeList(boutiqueOrder.getId(), operationTypeList);
        OperationType returnOperationType = isRefundMode ? OperationType.REFUND : OperationType.HOLD_REVERSE;
        orderFlowTestUtils.validateBankOperation(boutiqueOrder.getId(), returnOperationType, 10_000_00);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_OrderFlow_Boutique_BoutiqueOrderReverseOnOnlineConfirmation() {
        _05_OrderFlow_Boutique_BoutiqueOrderReturnsOnOnlineConfirmation(false);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _05_OrderFlow_Boutique_BoutiqueOrderRefundsOnOnlineConfirmation() {
        _05_OrderFlow_Boutique_BoutiqueOrderReturnsOnOnlineConfirmation(true);
    }


    private void _XX_OrderFlow_Boutique_SetProductPrice(Product product, BigDecimal priceValue) {
        product.setCurrentPrice(
                commissionService.calculatePriceWithCommission(
                        priceValue,
                        priceValue,
                        SalesChannel.BOUTIQUE_AND_WEBSITE,
                        false,
                        product.getSeller().getCommissionGrid()
                ).getPriceWithCommission()
        );
        product.setCurrentPriceWithoutCommission(
                commissionService.calculatePriceWithoutCommission(
                        priceValue,
                        priceValue,
                        SalesChannel.BOUTIQUE_AND_WEBSITE,
                        false,
                        product.getSeller().getCommissionGrid()
                )
        );
        boutiqueService.propagatePriceChange(product);

        product.getProductItems().forEach(productItem -> {
            productItem.setCurrentPrice(
                    commissionService.calculatePriceWithCommission(
                            priceValue,
                            priceValue,
                            SalesChannel.BOUTIQUE_AND_WEBSITE,
                            false,
                            product.getSeller().getCommissionGrid()
                    ).getPriceWithCommission()
            );
            productItem.setCurrentPriceWithoutCommission(
                    commissionService.calculatePriceWithoutCommission(
                            priceValue,
                            priceValue,
                            SalesChannel.BOUTIQUE_AND_WEBSITE,
                            false,
                            product.getSeller().getCommissionGrid()
                    )
            );
        });
    }

    private void _06_OrderFlow_Boutique_BoutiqueOrderReturnsOnOnlineConfirmationWithPriceChanged(boolean isRefundMode) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        OrderDTO boutiqueOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, isRefundMode,
                10_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        Assertions.assertThat(boutiqueOrder.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
        commitAndStartNewTransaction();
        //
        Product product = orderFlowTestUtils.loadProduct(products.get(0).getId());
        _XX_OrderFlow_Boutique_SetProductPrice(product, BigDecimal.valueOf(12_350_00, 2));
        commitAndStartNewTransaction();
        OrderDTO orderWithUpdate = orderFlowTestUtils.loadOrderSuccessfull(boutiqueOrder.getId(), true);
        Assertions.assertThat(orderWithUpdate.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(19_000_00, 2));
        //
        OrderService.InitOrderResult platformOrderHold = _XX_OrderFlow_Boutique_CreateOnlineOrder(products);
        rollbackAndStartNewTransaction();
        //
        OrderDTO platformOrder = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(apiV2ClientPlatform, platformOrderHold.getOrderId(), true);
        orderFlowTestUtils.callOrderHoldCallback(platformOrderHold.getOrderId(), platformOrderHold.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(platformOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderState(boutiqueOrder.getId(), isRefundMode ? OrderState.MONEY_TRANSFERRED : OrderState.HOLD);
        //
        OrderPositionDTO op = platformOrder.getItems().get(0);
        ResponseEntity<String> failApprove = orderFlowTestUtils.approvePosition(op.getId());
        if (!isRefundMode) {
            Assertions.assertThat(failApprove.getBody()).contains("Нет прав для подтверждения. Пожалуйста, переместите заказ в бутик.");
            return;
        }
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(platformOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderState(boutiqueOrder.getId(), isRefundMode ? OrderState.MONEY_TRANSFERRED : OrderState.HOLD);
        //
        _XX_OrderFlow_Boutique_ConfirmPlatformOrder(platformOrder.getId(), boutiqueOrder.getId(), isRefundMode, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        rollbackAndStartNewTransaction();
        //
        if (isRefundMode) {
            orderFlowTestUtils.processBankOperation(boutiqueOrder.getId(), OperationType.REFUND);
            rollbackAndStartNewTransaction();
        }
        //
        orderFlowTestUtils.validateOrderState(platformOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderState(boutiqueOrder.getId(), OrderState.REFUND);
        orderFlowTestUtils.validateFiscalReceiptsTypeList(boutiqueOrder.getId(), Collections.emptyList());
        List<OperationType> operationTypeList = isRefundMode
                ? Arrays.asList(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.REFUND)
                : Arrays.asList(OperationType.HOLD, OperationType.HOLD_REVERSE);
        orderFlowTestUtils.validateBankOperationTypeList(boutiqueOrder.getId(), operationTypeList);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _07_01_OrderFlow_Boutique_BoutiqueOrderOnlineConfirmationWithAuthority() {
        orderFlowTestUtils.enableUserAuthority(boutiqueBuyerId, AuthorityName.ORDER_BOUTIQUE_1ST_ACTION, true);
        orderFlowTestUtils.enableUserAuthority(boutiqueBuyerId, AuthorityName.ORDER_BOUTIQUE_2ND_ACTION, true);
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        OrderDTO boutiqueOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true,
                10_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        Assertions.assertThat(boutiqueOrder.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
        commitAndStartNewTransaction();
        //
        Product product = orderFlowTestUtils.loadProduct(products.get(0).getId());
        _XX_OrderFlow_Boutique_SetProductPrice(product, BigDecimal.valueOf(12_350_00, 2));
        commitAndStartNewTransaction();
        OrderDTO orderWithUpdate = orderFlowTestUtils.loadOrderSuccessfull(boutiqueOrder.getId(), true);
        Assertions.assertThat(orderWithUpdate.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(19_000_00, 2));
        //
        OrderService.InitOrderResult platformOrderHold = _XX_OrderFlow_Boutique_CreateOnlineOrder(products);
        rollbackAndStartNewTransaction();
        //
        OrderDTO platformOrder = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(apiV2ClientPlatform, platformOrderHold.getOrderId(), true);
        orderFlowTestUtils.callOrderHoldCallback(platformOrderHold.getOrderId(), platformOrderHold.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(platformOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderState(boutiqueOrder.getId(), OrderState.MONEY_TRANSFERRED);
        //
        platformOrder.getItems().forEach(op -> orderFlowTestUtils.rejectOrApprovePosition(op.getId(), true));
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _07_02_OrderFlow_Boutique_BoutiqueOrderOnlineConfirmationWithoutAuthority() {
        orderFlowTestUtils.enableUserAuthority(boutiqueBuyerId, AuthorityName.ORDER_BOUTIQUE_1ST_ACTION, false);
        orderFlowTestUtils.enableUserAuthority(boutiqueBuyerId, AuthorityName.ORDER_BOUTIQUE_2ND_ACTION, true);
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        OrderDTO boutiqueOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true,
                10_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        Assertions.assertThat(boutiqueOrder.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
        commitAndStartNewTransaction();
        //
        Product product = orderFlowTestUtils.loadProduct(products.get(0).getId());
        _XX_OrderFlow_Boutique_SetProductPrice(product, BigDecimal.valueOf(12_350_00, 2));
        commitAndStartNewTransaction();
        OrderDTO orderWithUpdate = orderFlowTestUtils.loadOrderSuccessfull(boutiqueOrder.getId(), true);
        Assertions.assertThat(orderWithUpdate.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(19_000_00, 2));
        //
        OrderService.InitOrderResult platformOrderHold = _XX_OrderFlow_Boutique_CreateOnlineOrder(products);
        rollbackAndStartNewTransaction();
        //
        OrderDTO platformOrder = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(apiV2ClientPlatform, platformOrderHold.getOrderId(), true);
        orderFlowTestUtils.callOrderHoldCallback(platformOrderHold.getOrderId(), platformOrderHold.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(platformOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderState(boutiqueOrder.getId(), OrderState.MONEY_TRANSFERRED);
        //
        OrderPositionDTO op = platformOrder.getItems().get(0);
        ResponseEntity<String> failApprove = orderFlowTestUtils.approvePosition(op.getId());
        Exception failException = orderFlowTestUtils.readExceptionFromText(failApprove.getBody());
        Assertions.assertThat(failException.getMessage()).matches("Для выполнения действия Вам необходимо одно из следующих прав: .*Бутик 1.*");
    }

    @Test
    @Transactional
    @Rollback(false)
//    @Disabled("Need time")
    public void _07_03_OrderFlow_Boutique_BoutiqueOrderOnlineApprovePositionAndConfirmationWithoutAuthority() {
        orderFlowTestUtils.enableUserAuthority(boutiqueBuyerId, AuthorityName.ORDER_BOUTIQUE_1ST_ACTION, true);
        orderFlowTestUtils.enableUserAuthority(boutiqueBuyerId, AuthorityName.ORDER_BOUTIQUE_2ND_ACTION, false);
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        OrderDTO boutiqueOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true,
                10_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        Assertions.assertThat(boutiqueOrder.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
        commitAndStartNewTransaction();
        //
        Product product = orderFlowTestUtils.loadProduct(products.get(0).getId());
        _XX_OrderFlow_Boutique_SetProductPrice(product, BigDecimal.valueOf(12_350_00, 2));
        commitAndStartNewTransaction();
        OrderDTO orderWithUpdate = orderFlowTestUtils.loadOrderSuccessfull(boutiqueOrder.getId(), true);
        Assertions.assertThat(orderWithUpdate.getFinalAmount()).isEqualByComparingTo(BigDecimal.valueOf(19_000_00, 2));
        //
        OrderService.InitOrderResult platformOrderHold = _XX_OrderFlow_Boutique_CreateOnlineOrder(products);
        rollbackAndStartNewTransaction();
        //
        OrderDTO platformOrder = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(apiV2ClientPlatform, platformOrderHold.getOrderId(), true);
        orderFlowTestUtils.callOrderHoldCallback(platformOrderHold.getOrderId(), platformOrderHold.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(platformOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderState(boutiqueOrder.getId(), OrderState.MONEY_TRANSFERRED);

        OrderPositionDTO op = platformOrder.getItems().get(0);
        ResponseEntity<String> failApprove = orderFlowTestUtils.approvePosition(op.getId());

        orderFlowTestUtils.enableUserAuthority(boutiqueBuyerId, AuthorityName.ORDER_BOUTIQUE_1ST_ACTION, false);
        orderFlowTestUtils.enableUserAuthority(boutiqueBuyerId, AuthorityName.ORDER_BOUTIQUE_2ND_ACTION, true);
        commitAndStartNewTransaction();


//        apiV2ClientBoutique.logout();

//        OrderFlowTestUtils.OskellyDeliveryInfo deliveryInfo = OrderFlowTestUtils.OskellyDeliveryInfo.builder()
//                .courierName("S2O-Courier").courierPhone("**********").courierDate(LocalDate.now()).callWillFail(false)
//                .build();
        ResponseEntity<String> sendCallData1st = orderFlowTestUtils.takeOurselves(platformOrder.getId(), null, boutiqueUser.getEmail(), boutiqueBuyerPassword);
        Exception sendCallFailInfo = orderFlowTestUtils.readExceptionFromText(sendCallData1st.getBody());
        Assertions.assertThat(sendCallFailInfo.getMessage()).matches("Для выполнения действия Вам необходимо одно из следующих прав: Заказы: управление заказами 'Бутик 1'");
        //
        //
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _06_01_OrderFlow_Boutique_BoutiqueOrderReverseOnOnlineConfirmationWithPriceChanged() {
        _06_OrderFlow_Boutique_BoutiqueOrderReturnsOnOnlineConfirmationWithPriceChanged(false);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _06_02_OrderFlow_Boutique_BoutiqueOrderRefundsOnOnlineConfirmationWithPriceChanged() {
        _06_OrderFlow_Boutique_BoutiqueOrderReturnsOnOnlineConfirmationWithPriceChanged(true);
    }

    /**
     * Проверяем сценарий:
     * 1. заказ бутика отправлен в возврат
     * 2. онлайн заказ на товар должен быть отправлен к Продавцу, а не в бутик.
     */
    @Test
    @Transactional
    @Rollback(value = false)
    public void _07_OrderFlow_Boutique_OnlineOrderAfterBoutiqueOrderRefund() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        // Создаем заказ в бутике
        OrderDTO boutiqueOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true,
                10_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        commitAndStartNewTransaction();
        // Исключаем единственную позицию в заказе из отчета агента и отправляем заказ в возврат
        long positionId = boutiqueOrder.getItems().get(0).getId();
        orderFlowTestUtils.excludeFromAgentReport(positionId, true);
        orderFlowTestUtils.refundOnReturn(boutiqueOrder.getId());
        commitAndStartNewTransaction();
        // Создаем онлайн заказ на товар
        OrderService.InitOrderResult platformOrderHold = _XX_OrderFlow_Boutique_CreateOnlineOrder(products);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.callOrderHoldCallback(platformOrderHold.getOrderId(), platformOrderHold.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        // Проверяем, что онлайн заказ не был отнесен к бутику
        // TODO: Switch to using API when "is online boutique order" attribute is exposed in API
        Order order = orderRepository.getOne(platformOrderHold.getOrderId());
        Assertions.assertThat(order.getBoutiqueOrder()).isNull();
        rollbackAndStartNewTransaction();
    }

    @Disabled
    @Test
    @Transactional
    @Rollback(false)
    public void _08_OrderFlow_Boutique_Split_Order() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(2)
                .build()
        );
        commitAndStartNewTransaction();

        OrderDTO testOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true,
                30_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        Assertions.assertThat(testOrder.getOrderSource()).isEqualTo(OrderSourceDTO.BOUTIQUE);
        Assertions.assertThat(testOrder.getOrderSourceInfo().getName()).isEqualTo(OrderSourceInfo.ORDER_SOURCE_INFO_MOVING_TO_BOUTIQUE);
        Assertions.assertThat(testOrder.getOrderSourceInfo().getName())
                .isEqualTo(orderSourceInfoService.findOrderSourceInfoByName(OrderSourceInfo.ORDER_SOURCE_INFO_MOVING_TO_BOUTIQUE).map(OrderSourceInfo::getName).orElse(null));
        Assertions.assertThat(testOrder.getProductLocation()).isEqualTo(ProductLocationDTO.SELLER);

        Order parentOrder = orderRepository.getOne(testOrder.getId());
        List<Order> orders = orderRepository.getOrdersByParentOrder(parentOrder);
        Assertions.assertThat(orders).isNotEmpty();
        orders.forEach(o -> {
            Optional<OrderPosition> optionalOrderPosition = o.getOrderPositions().stream()
                    .filter(op -> testOrder.getItems().stream().anyMatch(i -> op.getId().equals(i.getId())))
                    .findFirst();
            Assertions.assertThat(optionalOrderPosition).isPresent();
            Assertions.assertThat(o.getOrderSource()).isEqualTo(parentOrder.getOrderSource());
            Assertions.assertThat(o.getSellerUser().getId()).isEqualTo(parentOrder.getSellerUser().getId());
            Assertions.assertThat(o.getBuyer().getId()).isEqualTo(parentOrder.getBuyer().getId());
        });
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _09_OrderFlow_Boutique_Order_Sold_And_Returned() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(3)
                .build()
        ).subList(1, 2);
        commitAndStartNewTransaction();
        // Создаем заказ в бутике
        final long boutiqueOrderId = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true,
                20_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME).getId();
        commitAndStartNewTransaction();
        // Вызываем "Продано"
        final long productItemId = products.get(0).getProductItems().get(0).getId();
        final String receiptGuid = UUID.randomUUID().toString();
        final String receiptNumber = "OK00-000001";
        final LocalDateTime receiptDate = LocalDateTime.now().minusHours(1);
        List<OrderDTO> sold1st = orderFlowTestUtils.setBoutiqueItemSold(productItemId, receiptGuid, receiptNumber, receiptDate, 1, 20_000, stock.getOnecUuid());
        rollbackAndStartNewTransaction();
        // Проверяем, что заказ продан
        final Order order = orderRepository.getOne(boutiqueOrderId);
        Assertions.assertThat(order.getSoldTime()).isNotNull();
        Assertions.assertThat(order.getSoldTime()).isEqualTo(receiptDate);
        // Проверяем, что данные чека попали в свойства заказа
        verifyOrderExtraPropValue(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_RECEIPT_GUID, receiptGuid);
        verifyOrderExtraPropValue(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_RECEIPT_NUMBER, receiptNumber);
        verifyOrderExtraPropValue(order, OrderExtraPropInfo.ORDER_EXTRA_PROP_1C_STOCK_ID, stock.getOnecUuid());
        // Проверяем повторный вызов "Продано"
        final LocalDateTime receiptDateV2 = LocalDateTime.now();
        List<OrderDTO> sold2nd = orderFlowTestUtils.setBoutiqueItemSold(productItemId, receiptGuid, receiptNumber, receiptDateV2, 1, 20_000, stock.getOnecUuid());
        rollbackAndStartNewTransaction();
        //
        List<Long> sold1stIds = sold1st.stream().map(OrderDTO::getId).collect(Collectors.toList());
        List<Long> sold2ndIds = sold2nd.stream().map(OrderDTO::getId).collect(Collectors.toList());
        Assertions.assertThat(sold2ndIds).isEqualTo(sold1stIds);
        // Проверяем, что заказ продан
        final Order orderV2 = orderRepository.getOne(boutiqueOrderId);
        Assertions.assertThat(orderV2.getSoldTime()).isNotNull();
        Assertions.assertThat(orderV2.getSoldTime()).isEqualTo(receiptDateV2);
        // Возвращаем заказ
        final String returnReceiptGuid = UUID.randomUUID().toString();
        final String returnReceiptNumber = "OK00-000001";
        List<OrderDTO> return1st = orderFlowTestUtils.returnBoutiqueItem(productItemId, returnReceiptGuid, returnReceiptNumber, 1, receiptGuid, stock.getOnecUuid());
        rollbackAndStartNewTransaction();
        // Проверяем, что заказ возвращен
        final Order orderV3 = orderRepository.getOne(boutiqueOrderId);
        Assertions.assertThat(orderV3.getState()).isEqualTo(OrderState.RETURN);
        // Проверяем, что данные чека возврата попали в свойства заказа
        verifyOrderExtraPropValue(orderV3, OrderExtraPropInfo.ORDER_EXTRA_PROP_RETURN_RECEIPT_GUID, returnReceiptGuid);
        verifyOrderExtraPropValue(orderV3, OrderExtraPropInfo.ORDER_EXTRA_PROP_RETURN_RECEIPT_NUMBER, returnReceiptNumber);
        verifyOrderExtraPropValue(orderV3, OrderExtraPropInfo.ORDER_EXTRA_PROP_1C_STOCK_ID, stock.getOnecUuid());
        // Проверим что заполнилось свойство-ссылка на заказ и что эти заказы одинаковые по содержимому
        final Optional<OrderExtraPropValue> republishOrderIds = OrderExtraPropsService.getOrderExtraPropValue(orderV3, OrderExtraPropInfo.ORDER_EXTRA_PROP_REPUBLISH_ORDER_IDS);
        Assertions.assertThat(republishOrderIds).isPresent();
        final Order orderV4 = orderRepository.getOne(Long.parseLong(republishOrderIds.get().getPropValue()));
        verifyOrderExtraPropValue(orderV4, OrderExtraPropInfo.ORDER_EXTRA_PROP_RETURNED_BOUTIQUE_ORDER_ID,
                String.valueOf(boutiqueOrderId));
        verifyOrderExtraPropValue(orderV4, OrderExtraPropInfo.ORDER_EXTRA_PROP_SKIP_1C_SYNC, "true");
        Set<Long> publishedProductIds = orderV3.getOrderPositions().stream().map(op -> op.getProductItem().getProduct().getId()).collect(Collectors.toSet());
        Set<Long> republishProductIds = orderV4.getOrderPositions().stream().map(op -> op.getProductItem().getProduct().getId()).collect(Collectors.toSet());
        Assertions.assertThat(republishProductIds).containsAll(publishedProductIds);
        Assertions.assertThat(orderV4.getOrderSource()).isEqualTo(OrderSource.BOUTIQUE);
        Assertions.assertThat(orderV4.getState()).isEqualTo(OrderState.MONEY_TRANSFERRED);
        //
        List<OrderDTO> return2nd = orderFlowTestUtils.returnBoutiqueItem(productItemId, returnReceiptGuid, returnReceiptNumber, 1, receiptGuid, stock.getOnecUuid());
        rollbackAndStartNewTransaction();
        //
        List<Long> return1stIds = return1st.stream().map(OrderDTO::getId).collect(Collectors.toList());
        List<Long> return2ndIds = return2nd.stream().map(OrderDTO::getId).collect(Collectors.toList());
        Assertions.assertThat(return1stIds).isEqualTo(return2ndIds);
    }

    @Test
    @Transactional
    @Rollback(value = false)
    public void _09_OrderFlow_Boutique_Order_Sold_And_Returned_To_Seller() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(3)
                .build()
        ).subList(1, 2);
        commitAndStartNewTransaction();
        // Создаем заказ в бутике
        final long boutiqueOrderId = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true,
                20_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME).getId();
        commitAndStartNewTransaction();
        // Возвращаем заказ Продавцу
        final long productItemId = products.get(0).getProductItems().get(0).getId();
        final String returnGuid = UUID.randomUUID().toString();
        List<OrderDTO> return1st = orderFlowTestUtils.returnBoutiqueItemToSeller(productItemId, returnGuid, 1, stock.getOnecUuid(), "a030ce0c-238c-11ed-96e2-ea8acf8eb23d");
        rollbackAndStartNewTransaction();
        // Проверяем, что заказ возвращен
        final Order orderV3 = orderRepository.getOne(boutiqueOrderId);
        Assertions.assertThat(orderV3.getState()).isEqualTo(OrderState.REFUND);
        // Проверяем, что GUID возврата попал в свойства заказа
        verifyOrderExtraPropValue(orderV3, OrderExtraPropInfo.ORDER_EXTRA_PROP_RETURN_TO_SELLER_GUID, returnGuid);
        verifyOrderExtraPropValue(orderV3, OrderExtraPropInfo.ORDER_EXTRA_PROP_1C_STOCK_ID, stock.getOnecUuid());
        //
        List<OrderDTO> return2nd = orderFlowTestUtils.returnBoutiqueItemToSeller(productItemId, returnGuid, 1, stock.getOnecUuid(), "a030ce0c-238c-11ed-96e2-ea8acf8eb23d");
        //
        List<Long> return1stIds = return1st.stream().map(OrderDTO::getId).collect(Collectors.toList());
        List<Long> return2ndIds = return2nd.stream().map(OrderDTO::getId).collect(Collectors.toList());
        Assertions.assertThat(return1stIds).isEqualTo(return2ndIds);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _10_1_OrderFlow_Boutique_Return_FailIfNotSold() {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true,
                10_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        //
        ResponseEntity<String> returnResponse = orderFlowTestUtils.returnCompletedOrSoldOrder(testOrder.getId(), null, false, stock.getOnecUuid());
        Api2Response<String> returnFailInfo = orderFlowTestUtils.getRawApi2Response(returnResponse.getBody());
        Assertions.assertThat(returnFailInfo.getMessage()).matches("Заказ .*: некорректное состояние заказа MONEY_TRANSFERRED");
    }

    private OrderDTO _10_X_OrderFlow_Boutique_Return_CreateOrder(boolean executePayout) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        OrderDTO testOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true,
                10_000_00, OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        //
        orderFlowTestUtils.sellOrderInBoutique(testOrder.getId(), true, stock.getOnecUuid());
        rollbackAndStartNewTransaction();
        //
        if (!executePayout) {
            return testOrder;
        }
        //
        orderFlowTestUtils.sendAgentReport(testOrder.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.confirmAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.prepareSellerPayout(testOrder.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.transferMoneyToSellers(testOrder.getId());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateMoneyToSellers(testOrder.getId());
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.transferAgentPaymentsMoneyToSellers();
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        return testOrder;
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _10_2_OrderFlow_Boutique_Return_NoBalanceChangeWhenSold() {
        OrderDTO testOrder = _10_X_OrderFlow_Boutique_Return_CreateOrder(false);
        //
        BigDecimal balanceZero = orderFlowTestUtils.adjustUserBalanceToValue(testOrder.getSeller().getId(), null);
        //
        orderFlowTestUtils.returnCompletedOrSoldOrder(testOrder.getId(), null, true, stock.getOnecUuid());
        rollbackAndStartNewTransaction();
        //
        BigDecimal balanceAfterReturn = orderFlowTestUtils.adjustUserBalanceToValue(testOrder.getSeller().getId(), null);
        //
        Assertions.assertThat(balanceZero).isEqualByComparingTo(balanceAfterReturn);
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
    }

    private void _10_3_X_OrderFlow_Boutique_Return_WithDifferentParams(Boolean debtOnReturn) {
        OrderDTO testOrder = _10_X_OrderFlow_Boutique_Return_CreateOrder(true);
        //
        BigDecimal balanceZero = orderFlowTestUtils.adjustUserBalanceToValue(testOrder.getSeller().getId(), null);
        Map<Long, Integer> productsStocksAfterSold = orderFlowTestUtils.getProductCountByProductItem(testOrder.getItems().get(0).getProductId());
        long totalAmountAfterSold = productsStocksAfterSold.values().stream().reduce(0, Integer::sum);
        //
        orderFlowTestUtils.returnCompletedOrSoldOrder(testOrder.getId(), debtOnReturn, true, stock.getOnecUuid());
        rollbackAndStartNewTransaction();
        //
        BigDecimal balanceAfterReturn = orderFlowTestUtils.adjustUserBalanceToValue(testOrder.getSeller().getId(), null);
        Map<Long, Integer> productsStocksAfterReturn = orderFlowTestUtils.getProductCountByProductItem(testOrder.getItems().get(0).getProductId());
        long totalAmountAfterReturn = productsStocksAfterReturn.values().stream().reduce(0, Integer::sum);
        //
        Assertions.assertThat(totalAmountAfterReturn).isEqualTo(totalAmountAfterSold + 1);
        //
        if (BooleanUtils.isFalse(debtOnReturn)) {
            Assertions.assertThat(balanceZero).isEqualByComparingTo(balanceAfterReturn);
        } else {
            Assertions.assertThat(balanceZero.subtract(balanceAfterReturn)).isEqualByComparingTo(BigDecimal.valueOf(6_500_00, 2));
        }
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.RETURN);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _10_3_1_OrderFlow_Boutique_Return_BalanceChangedWhenCompletedReturnedWithNullParameter() {
        _10_3_X_OrderFlow_Boutique_Return_WithDifferentParams(null);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _10_3_2_OrderFlow_Boutique_Return_BalanceChangedWhenCompletedReturnedWithFalseParameter() {
        _10_3_X_OrderFlow_Boutique_Return_WithDifferentParams(false);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _10_3_3_OrderFlow_Boutique_Return_BalanceChangedWhenCompletedReturnedWithTrueParameter() {
        _10_3_X_OrderFlow_Boutique_Return_WithDifferentParams(true);
    }

    private long _11_orderFlowBoutiqueOrderTest_boutiqueUserValidate(OrderSourceInfo orderSourceInfo) {
        User boutiqueUser = orderSourceInfo.getMasterUser();
        Assertions.assertThat(boutiqueUser).isNotNull();
        List<AddressEndpoint> activeAeps = boutiqueUser.getAddressEndpoints().stream()
                .filter(it -> Objects.isNull(it.getDeleteTime())).collect(Collectors.toList());
        Assertions.assertThat(activeAeps).hasSize(1);
        return boutiqueUser.getId();
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _11_orderFlowBoutiqueOrderTest_boutiqueUserListValidate() {
        Set<Long> addrList = new HashSet<>();
        //
        Optional<OrderSourceInfo> boutique00 = orderSourceInfoService.findOrderSourceInfoByName(OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME);
        Assertions.assertThat(boutique00).isPresent();
        addrList.add(_11_orderFlowBoutiqueOrderTest_boutiqueUserValidate(boutique00.get()));
        //
        Optional<OrderSourceInfo> boutique01 = orderSourceInfoService.findOrderSourceInfoByName(OrderSourceInfo.ORDER_SOURCE_INFO_OSKELLY_STOCK_NAME);
        Assertions.assertThat(boutique01).isPresent();
        addrList.add(_11_orderFlowBoutiqueOrderTest_boutiqueUserValidate(boutique01.get()));
        //
        Optional<OrderSourceInfo> boutique02 = orderSourceInfoService.findOrderSourceInfoByName(OrderSourceInfo.ORDER_SOURCE_INFO_OSKELLY_BOUTIQUE02);
        Assertions.assertThat(boutique02).isPresent();
        addrList.add(_11_orderFlowBoutiqueOrderTest_boutiqueUserValidate(boutique02.get()));
        //
        Assertions.assertThat(addrList).hasSize(3);
    }

    private static void verifyOrderExtraPropValue(
            @NonNull final Order order,
            @NonNull final OrderExtraPropInfo extraPropInfo,
            @NonNull final String value) {
        final Optional<OrderExtraPropValue> extraPropValue = OrderExtraPropsService.getOrderExtraPropValue(
                order, extraPropInfo);
        Assertions.assertThat(extraPropValue).isPresent();
        Assertions.assertThat(extraPropValue.get().getPropValue()).isEqualTo(value);
    }

    public void _12_orderFlowBoutiqueOrderTest_onlineOrdersOnBoutiqueItemTest(String ordersSourceName, String usersEMail, boolean isFail) {
        List<Product> products = orderFlowTestUtils.getProductsForOrdersWithSeller(OrderFlowTestUtils.FindProduct4Test.builder()
                .sellerId(sellerId)
                .forBoutique(true)
                .maxItems(1)
                .build()
        );
        commitAndStartNewTransaction();
        //
        OrderDTO boutiqueOrder = _00_OrderFlow_Boutique_MoveItemsToBoutique(products, true, 10_000_00, ordersSourceName);
        //
        OrderService.InitOrderResult platformOrderHold = _XX_OrderFlow_Boutique_CreateOnlineOrder(products);
        rollbackAndStartNewTransaction();
        //
        OrderDTO platformOrder = orderFlowTestUtils.loadOrderSuccessfullWithCustomClient(apiV2ClientPlatform, platformOrderHold.getOrderId(), true);
        orderFlowTestUtils.callOrderHoldCallback(platformOrderHold.getOrderId(), platformOrderHold.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        OrderPositionDTO op = platformOrder.getItems().get(0);
        ResponseEntity<String> failApprove = orderFlowTestUtils.approvePosition(op.getId());
        if (isFail) {
            Exception failInfo = orderFlowTestUtils.readExceptionFromText(failApprove.getBody());
            Assertions.assertThat(failInfo.getMessage()).matches("Для выполнения действия Вам необходимо одно из следующих прав: Заказы: управление заказами 'Перемещение' \\+ 'Склад'");
            return;
        }
        //
        orderFlowTestUtils.takeOurselves(platformOrder.getId(), null);
        rollbackAndStartNewTransaction();
        //
        User shipFromAddrUser = userService.getUserByEmail(usersEMail);
        Order platformOrderReadyToShip = orderRepository.getOne(platformOrder.getId());
        List<Long> userAepsList = shipFromAddrUser.getAddressEndpoints().stream()
                .filter(it -> Objects.isNull(it.getDeleteTime()))
                .map(AddressEndpoint::getId).collect(Collectors.toList());
        Long orderPickupAepLong = platformOrderReadyToShip.getPickupAddressEndpoint().getId();
        Assertions.assertThat(userAepsList).hasSize(1);
        Assertions.assertThat(userAepsList).contains(orderPickupAepLong);
        //
        //
        orderFlowTestUtils.takeOurselves(platformOrder.getId(), null);
        rollbackAndStartNewTransaction();
        orderPickupAepLong = platformOrderReadyToShip.getPickupAddressEndpoint().getId();
        Assertions.assertThat(userAepsList).hasSize(1);
        Assertions.assertThat(userAepsList).contains(orderPickupAepLong);
    }

    @Test
    @Transactional
    @Rollback(false)
    public void _12_orderFlowBoutiqueOrderTest_onlineOrdersOnBoutiqueItem() {
        _12_orderFlowBoutiqueOrderTest_onlineOrdersOnBoutiqueItemTest(OrderSourceInfo.ORDER_SOURCE_INFO_OSKELLY_STOCK_NAME, "<EMAIL>", true);
        _12_orderFlowBoutiqueOrderTest_onlineOrdersOnBoutiqueItemTest(OrderSourceInfo.ORDER_SOURCE_INFO_BOUTIQUE_STOLESHNIKOV_NAME, "<EMAIL>", false);
        _12_orderFlowBoutiqueOrderTest_onlineOrdersOnBoutiqueItemTest(OrderSourceInfo.ORDER_SOURCE_INFO_OSKELLY_BOUTIQUE02, "<EMAIL>", false);
    }

}
