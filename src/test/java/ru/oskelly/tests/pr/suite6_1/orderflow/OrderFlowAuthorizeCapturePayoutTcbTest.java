package ru.oskelly.tests.pr.suite6_1.orderflow;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestMethodOrder;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import ru.oskelly.tests.OrderFlowTest;
import ru.oskelly.tests.pr.suite3.presentation.api.v2.ApiV2Client;
import ru.oskelly.tests.testops.TestSuiteName;
import ru.oskelly.tests.testops.annotations.DevSuite;
import ru.oskelly.tests.testops.annotations.Layer;
import su.reddot.component.CartTestSupport;
import su.reddot.domain.model.agentreport.AgentReport;
import su.reddot.domain.model.banktransaction.BankOperation;
import su.reddot.domain.model.banktransaction.OperationType;
import su.reddot.domain.model.banktransaction.TransactionState;
import su.reddot.domain.model.counterparty.CardCounterparty;
import su.reddot.domain.model.counterparty.Counterparty;
import su.reddot.domain.model.counterparty.CounterpartyType;
import su.reddot.domain.model.counterparty.IpCounterparty;
import su.reddot.domain.model.enums.AuthorityName;
import su.reddot.domain.model.fiscalreceiptrequest.FiscalReceiptRequestType;
import su.reddot.domain.model.order.Order;
import su.reddot.domain.model.order.OrderPayment;
import su.reddot.domain.model.order.OrderPaymentState;
import su.reddot.domain.model.order.OrderSource;
import su.reddot.domain.model.order.OrderState;
import su.reddot.domain.model.product.Product;
import su.reddot.domain.model.user.User;
import su.reddot.domain.service.counterparty.CounterpartyService;
import su.reddot.domain.service.dto.BankOperationDTO;
import su.reddot.domain.service.dto.BankPaymentDTO;
import su.reddot.domain.service.dto.order.GroupedCart;
import su.reddot.domain.service.dto.order.OrderDTO;
import su.reddot.domain.service.dto.order.OrderPositionDTO;
import su.reddot.domain.service.order.OrderService;
import su.reddot.domain.service.user.UserService;
import su.reddot.infrastructure.bank.TcbBankService;
import su.reddot.infrastructure.bank.impl.tcb.TcbBankClient;
import su.reddot.infrastructure.bank.impl.tcb.response.GetOrderStateResponse;
import su.reddot.infrastructure.bank.jobs.ScheduledBankRunner;
import su.reddot.infrastructure.cashregister.Checkable;
import su.reddot.infrastructure.logistic.DeliveryState;
import su.reddot.infrastructure.util.CallInTransaction;
import su.reddot.infrastructure.util.Utils;
import su.reddot.oskelly.orderprocessing.internal.web.dto.IntegrationMobileOrderExpertiseDTO;
import su.reddot.presentation.api.v2.Api2Response;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Layer
@DevSuite(value = TestSuiteName.TEST_SUITE_06)
@TestMethodOrder(MethodOrderer.MethodName.class)
public class OrderFlowAuthorizeCapturePayoutTcbTest extends OrderFlowTest {

    @Autowired
    private UserService userService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private CounterpartyService counterpartyService;

    @Autowired
    private ScheduledBankRunner scheduledBankRunner;

    @Autowired
    private CallInTransaction callInTransaction;
    @Autowired
    private OrderFlowTestUtils orderFlowTestUtils;
    @Autowired
    private CartTestSupport cartTestSupport;

    @Value("${test.api.user-email}")
    private String buyerEmail;
    @Value("${test.api.user-password}")
    private String password;
    @Value("${test-prepayments.agent-seller-id}")
    private Long agentSellerId;
    @Value("${test-prepayments.usual-seller-id}")
    private Long usualSellerId;
    @Value("${test-prepayments.agent-seller-counterparty-id}")
    private Long agentSellerCounterpartyId;
    @Value("${test-prepayments.pickup-id}")
    private Long pickupId;
    @Value("${test-prepayments.delivery-id}")
    private Long deliveryId;

    private static OrderFlowTestTcbMock orderFlowTestTcbMock;

    private ApiV2Client apiV2Client;

    @Value("${test-prepayments.usual-seller-counterparty-id}")
    private Long usualSellerCounterpartyId;

    private final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setSerializationInclusion(JsonInclude.Include.NON_NULL)
            .enable(SerializationFeature.INDENT_OUTPUT);

    private List<Product> getProductsForOrdersWithSellerType(boolean isAgentSeller) {
        Long sellerId = isAgentSeller ? agentSellerId : usualSellerId;
        return orderFlowTestUtils.getProductsForOrdersWithSeller(sellerId);
    }

    private OrderService.InitOrderResult holdOrderWithPromoCodePS(User seller, String promoCode, String paymentSystem) {
        OrderService.InitOrderResult holdResult = cartTestSupport.holdWithSetAddressEndpointPCnCC(CartTestSupport.HOLD_V2_ENDPOINT, seller.getId(),
                promoCode, paymentSystem);
        Long orderId = holdResult.getOrderId();
        orderFlowTestUtils.loadOrderSuccessfull(orderId, true);
        return holdResult;
    }

    @Value("${test.receipts.mock-server-host}")
    private String mockServerHost;
    @Value("${test.receipts.mock-server-tcb-bank-port}")
    private Integer mockTcbServerPort;


    private Long prepareAdminUserData() {
        User adminsUser = userService.getUserByEmail(buyerEmail);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_MANUAL_CHANGE_DELIVERY_STATE, true);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.ORDER_PAYOUTS, true);
        orderFlowTestUtils.enableUserAuthority(adminsUser.getId(), AuthorityName.TEST_AUTHORITY_00, true);
        return adminsUser.getId();
    }

    private Long prepareAgentSellerData() {
        prepareAgentSellerIndPData();
        Counterparty counterparty = counterpartyService.findById(agentSellerCounterpartyId);
        counterparty.setContractTime(LocalDateTime.now());
        counterparty.setContractNumber("CONTRACT-ID");
        counterparty.setVatRateIndex(2);
        counterparty.setPaymentAccount("*************.......");
        counterpartyService.save(counterparty);
        return counterparty.getId();
    }

    private Long prepareUsualSellerCardData() {
        User usualSeller = userService.getOne(usualSellerId);
        CardCounterparty cardCounterParty = usualSeller.getCounterparties().stream()
                .filter(cp -> cp.getType() == CounterpartyType.CARD)
                .map(CardCounterparty.class::cast)
                .filter(cp -> cp.getCardRefId().equals("*********"))
                .findFirst().orElse(null);
        if (Objects.nonNull(cardCounterParty)) {
            return cardCounterParty.getId();
        }
        cardCounterParty = new CardCounterparty()
                .setCardRefId("*********")
                .setCardNumber("4274 32** **** 2991")
                .setCardBindTime(ZonedDateTime.parse("2022-08-08T00:00Z", DateTimeFormatter.ISO_ZONED_DATE_TIME))
                .setCardBindBank("TCB")
                .setCardBrand("VISA")
                .setCardExpireTime(ZonedDateTime.parse("2028-08-08T00:00Z", DateTimeFormatter.ISO_ZONED_DATE_TIME));
        cardCounterParty.setUser(usualSeller);
        counterpartyService.save(cardCounterParty);
        //
        return cardCounterParty.getId();
    }

    private Long prepareAgentSellerIndPData() {
        User usualSeller = userService.getOne(agentSellerId);
        Counterparty ipCounterParty = usualSeller.getCounterparties().stream()
                .filter(cp -> cp.getType() == CounterpartyType.INDIVIDUAL_ENTREPRENEUR)
                .map(IpCounterparty.class::cast)
                .filter(cp -> cp.getInn().equals("************"))
                .findFirst().orElse(null);
        if (Objects.nonNull(ipCounterParty)) {
            return ipCounterParty.getId();
        }
        ipCounterParty = new IpCounterparty()
                .setInn("************")
                .setBik("*********")
                .setPaymentAccount("00002810460001020304")
                .setFirstName("Сергей")
                .setLastName("Вымышленный")
                .setPatronymicName("Иванович")
                .setContractTime(LocalDateTime.parse("2022-08-08T00:00", DateTimeFormatter.ISO_LOCAL_DATE_TIME))
                .setContractNumber("CONTRACT-IpCounterparty")
                .setVatRateIndex(4);
        ipCounterParty.setUser(usualSeller);
        counterpartyService.save(ipCounterParty);
        //
        return ipCounterParty.getId();
    }

    private Long prepareUsualSellerData() {
        prepareUsualSellerCardData();
        Counterparty counterparty = counterpartyService.findById(usualSellerCounterpartyId);
        counterparty.setPaymentAccount("*************.......");
        counterpartyService.save(counterparty);
        return counterparty.getId();
    }

    @PostConstruct
    private void init() {
        orderFlowTestUtils.setAllowPaymentSystemChoose(Lists.newArrayList(TcbBankService.SCHEMA));
        User buyer = userService.getUserByEmail(buyerEmail);
        apiV2Client = new ApiV2Client(buyerEmail, password);
        orderFlowTestUtils.init(buyerEmail, password);
        cartTestSupport.setUserId(buyer.getId());
        cartTestSupport.setApiV2Client(apiV2Client);
        cartTestSupport.getDeliveryAddressEndpoint();
        orderFlowTestTcbMock = Objects.isNull(orderFlowTestTcbMock) ? new OrderFlowTestTcbMock(mockServerHost, mockTcbServerPort) : orderFlowTestTcbMock;
        agentSellerCounterpartyId = callInTransaction.runInNewTransaction(this::prepareAgentSellerData);
        usualSellerCounterpartyId = callInTransaction.runInNewTransaction(this::prepareUsualSellerData);
        callInTransaction.runInNewTransaction(this::prepareAdminUserData);
        Mockito.when(orderMobileApi.getMobileOrderExpertise(Mockito.any()))
                .thenReturn(new IntegrationMobileOrderExpertiseDTO().items(Collections.emptyList()));
    }

    @BeforeEach
    public void before() {
        orderFlowTestUtils.adjustUserBalanceToValue(usualSellerId, BigDecimal.ZERO);
        orderFlowTestUtils.adjustUserBalanceToValue(agentSellerId, BigDecimal.ZERO);
    }

    @AfterAll
    public static void done() {
        orderFlowTestTcbMock.stop();
    }

    private static final long ITEM_KIND_ADVANCE = Checkable.Line.LINE_ATTRIBUTE_ADVANCE;
    private static final long ITEM_KIND_COMMODITY = Checkable.Line.LINE_ATTRIBUTE_COMMODITY;
    private static final long ITEM_KIND_SERVICE = Checkable.Line.LINE_ATTRIBUTE_SERVICE;

    private static final long ITEM_PAY_KIND_ADVANCE = Checkable.Line.LINE_PAYMENT_ADVANCE;
    private static final long ITEM_PAY_KIND_FULLPAY = Checkable.Line.LINE_PAYMENT_FULL_PAYMENT;

    private AgentReport validateAgentReportForAgentSellerJur(long orderId) {
        AgentReport agentReport = orderService.getOrder(orderId).getAgentReport();
        Assertions.assertThat(agentReport.getPaymentDetails())
            .matches("Оплата за реализованный товар по заказу № .* по агентскому договору № 10919 от .* на сайте: www.oskelly.ru. НДС не облагается");
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getName, AgentReport::getFirstName, AgentReport::getSecondName, AgentReport::getPatronymic)
                .containsExactly("OOO \"Триумфальная арка\"", null, "...", "...");
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getInn, AgentReport::getBik, AgentReport::getKpp, AgentReport::getPaymentAccount)
                .containsExactly("**********", "*********", "*********", "*************.......");
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getCardRefId, AgentReport::getCardNumber, AgentReport::getCardHolder, AgentReport::getCardBrand)
                .containsExactly(null, null, null, null);
        return agentReport;
    }

    private AgentReport validateAgentReportForAgentSellerIep(long orderId) {
        AgentReport agentReport = orderService.getOrder(orderId).getAgentReport();
        Assertions.assertThat(agentReport.getPaymentDetails())
                .matches("Оплата за реализованный товар по заказу № .* по агентскому договору № 10919 от .* на сайте: www.oskelly.ru. НДС не облагается");
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getName, AgentReport::getFirstName, AgentReport::getSecondName, AgentReport::getPatronymic)
                .containsExactly("Индивидуальный предприниматель Вымышленный Сергей Иванович", "Сергей", "Вымышленный", "Иванович");
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getInn, AgentReport::getBik, AgentReport::getKpp, AgentReport::getPaymentAccount)
                .containsExactly("************", "*********", null, "00002810460001020304");
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getCardRefId, AgentReport::getCardNumber, AgentReport::getCardHolder, AgentReport::getCardBrand)
                .containsExactly(null, null, null, null);
        return agentReport;
    }

    private AgentReport validateAgentReportForUsualSellerCard(long orderId) {
        AgentReport agentReport = orderService.getOrder(orderId).getAgentReport();
        Assertions.assertThat(agentReport.getPaymentDetails())
                .matches("Оплата за реализованный товар по заказу № .* по агентскому договору № 27 от .* на сайте: www.oskelly.ru. НДС не облагается");
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getName, AgentReport::getFirstName, AgentReport::getSecondName, AgentReport::getPatronymic)
                .containsExactly("... Антон ...", null, null, null);
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getInn, AgentReport::getBik, AgentReport::getKpp, AgentReport::getPaymentAccount)
                .containsExactly("************", null, null, null);
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getCardRefId, AgentReport::getCardNumber, AgentReport::getCardHolder, AgentReport::getCardBrand)
                .containsExactly("*********", "4274 32** **** 2991", null, "VISA");
        return agentReport;
    }

    private AgentReport validateAgentReportForUsualSellerPhys(long orderId) {
        AgentReport agentReport = orderService.getOrder(orderId).getAgentReport();
        Assertions.assertThat(agentReport.getPaymentDetails())
                .matches("Оплата за реализованный товар по заказу № .* по агентскому договору № 27 от .* на сайте: www.oskelly.ru. НДС не облагается");
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getName, AgentReport::getFirstName, AgentReport::getSecondName, AgentReport::getPatronymic)
                .containsExactly("... Магомед ...", "Магомед", "...", "...");
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getInn, AgentReport::getBik, AgentReport::getKpp, AgentReport::getPaymentAccount)
                .containsExactly("************", "*********", null, "*************.......");
        Assertions.assertThat(agentReport)
                .extracting(AgentReport::getCardRefId, AgentReport::getCardNumber, AgentReport::getCardHolder, AgentReport::getCardBrand)
                .containsExactly(null, null, null, null);
        return agentReport;
    }

    @SneakyThrows
    public long _01_OrderFlow_HappyPathOkay(boolean isAgentSeller, long sellerCounterPartyId, String contractNo, LocalDateTime contractDateTime) {
        List<Product> products = getProductsForOrdersWithSellerType(isAgentSeller);
        commitAndStartNewTransaction();
        //
        Counterparty sellerCounterparty = counterpartyService.findById(sellerCounterPartyId);
        sellerCounterparty.setContractNumber(contractNo);
        sellerCounterparty.setContractTime(contractDateTime);
        counterpartyService.save(sellerCounterparty);
        commitAndStartNewTransaction();
        //
        GroupedCart cartInfo = orderFlowTestUtils.fillCart(products.subList(0, 5));
        cartInfo.getGroup(products.get(0).getSeller().getId());
        //
        OrderService.InitOrderResult testOrder1 = holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        rollbackAndStartNewTransaction();
        //
        OrderDTO testOrder = orderFlowTestUtils.loadOrderSuccessfull(testOrder1.getOrderId(), true);
        Order orderInfo = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD_PROCESSING);
        Assertions.assertThat(orderInfo.getOrderSource()).isEqualTo(OrderSource.WEB);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 150_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD));
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        //
        orderFlowTestUtils.callOrderHoldCallback(testOrder1.getOrderId(), testOrder1.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        Order orderOnHold = orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
        OrderPayment orderPayment = orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        Assertions.assertThat(orderOnHold.getTransactionId()).isEqualTo(orderPayment.getPaymentSystemTransactionId());
        Assertions.assertThat(orderOnHold.getAcquirerOrderId()).isEqualTo(orderPayment.getPaymentSystemOrderId());
        //
        BankOperation holdOperation = orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 150_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD));
        //
        GetOrderStateResponse holdResponseData = TcbBankService.readRawTcbResponse(holdOperation.getRawResponse());
        Assertions.assertThat(orderPayment.getPaymentSystemTransactionId()).isEqualTo(holdResponseData.getOrderInfo().getOrderId());
        Assertions.assertThat(orderPayment.getPaymentSystemOrderId()).isEqualTo(holdResponseData.getOrderInfo().getExtId());
        //
        testOrder.getItems().forEach(op -> orderFlowTestUtils.rejectOrApprovePosition(op.getId(), true));
        //
        if (isAgentSeller) orderFlowTestUtils.changeSellerCounterparty(testOrder.getId(), sellerCounterPartyId, HttpStatus.Series.SUCCESSFUL);
        //
        orderFlowTestUtils.changeAddressEndpoint(testOrder.getId(), pickupId, deliveryId);
        orderFlowTestUtils.takeOurselves(testOrder.getId(), null);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        for (OrderPositionDTO orderPosition : testOrder.getItems()) {
            orderFlowTestUtils.adminsApi1expertise(orderPosition.getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_PASSED_OK, null);
        }
        //
        orderFlowTestUtils.adminPanel_Charge(testOrder.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD_COMPLETED);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_INPROGRESS);
        if (isAgentSeller) {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_ADVANCE));
            orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.AGENT_ADVANCE, 0L,
                    Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, testOrder.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                    Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        } else {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE));
            orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                    Lists.newArrayList(testOrder.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_ADVANCE),
                    Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
        }
        //
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 150_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_COMPLETE));
        //
        ResponseEntity<String> sendItResponseFail = orderFlowTestUtils.sendOurselves(testOrder.getId(), null);
        Assertions.assertThat(sendItResponseFail.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownSendIt = objectMapper.readValue(sendItResponseFail.getBody(), Exception.class);
        Assertions.assertThat(thrownSendIt.getMessage())
                .matches("Заказ .*: не удается вызвать службу доставки, так как списание средств не завершено");
        rollbackAndStartNewTransaction();
        //
        ResponseEntity<String> changeStateFail = orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, false);
        Assertions.assertThat(changeStateFail.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownChangeState = objectMapper.readValue(changeStateFail.getBody(), Exception.class);
        Assertions.assertThat(thrownChangeState.getMessage())
                .matches("Заказ .*: не удается изменить статус доставки, платеж не завершен");
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.processHoldComplete(testOrder);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_TRANSFERRED);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.CAPTURE_DONE);
        //
        ResponseEntity<String> sendItResponseOkay = orderFlowTestUtils.sendOurselves(testOrder.getId(), null);
        Assertions.assertThat(sendItResponseOkay.getStatusCode().is2xxSuccessful()).isTrue();
        //
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        if (isAgentSeller) {
            orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                    Lists.newArrayList(50_000_00L, 40_000_00L, 30_000_00L, 20_000_00L, 10_000_00L, testOrder.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_COMMODITY, ITEM_KIND_SERVICE),
                    Lists.newArrayList(ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY));
        } else {
            orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                    Lists.newArrayList(testOrder.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_SERVICE),
                    Lists.newArrayList(ITEM_PAY_KIND_FULLPAY));
        }
        //
        orderFlowTestUtils.sendAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.sendAgentReport(testOrder.getId(), true); // It`s ok to send agentReport twice: notification will be resend
        rollbackAndStartNewTransaction();
        //
        if (!isAgentSeller) orderFlowTestUtils.changeSellerCounterparty(testOrder.getId(), sellerCounterPartyId, HttpStatus.Series.SUCCESSFUL);
        //
        orderFlowTestUtils.confirmAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        if (!isAgentSeller) {
            orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(testOrder.getId(), sellerCounterPartyId, OrderFlowTestUtils.FAIL_TEXT_PAYOUT_ACTIVE_OR_DONE);
        }
        //
        ResponseEntity<String> arFailResponse = orderFlowTestUtils.sendAgentReport(testOrder.getId(), false);
        Api2Response<String> arSendFailInfo = orderFlowTestUtils.getRawApi2Response(arFailResponse.getBody());
        Assertions.assertThat(arSendFailInfo.getMessage()).matches("Отчет о продаже для заказа .* уже подтвержден");
        rollbackAndStartNewTransaction();
        //
        ResponseEntity<String> arConfResponse = orderFlowTestUtils.confirmAgentReport(testOrder.getId(), false);
        Api2Response<String> arConfFailInfo = orderFlowTestUtils.getRawApi2Response(arConfResponse.getBody());
        Assertions.assertThat(arConfFailInfo.getMessage()).matches("Отчет о продаже для заказа .* уже подтвержден");
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.prepareSellerPayout(testOrder.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), 112_500_00, null, TcbBankService.SCHEMA, TransactionState.PREPARED);
        AgentReport agentReport = null;
        switch (sellerCounterparty.getType()) {
            case PHYSICAL_PERSON:
                agentReport = validateAgentReportForUsualSellerPhys(testOrder.getId());
                break;
            case CARD:
                agentReport = validateAgentReportForUsualSellerCard(testOrder.getId());
                break;
            case LEGAL_ENTITY:
                agentReport = validateAgentReportForAgentSellerJur(testOrder.getId());
                break;
            case INDIVIDUAL_ENTREPRENEUR:
                agentReport = validateAgentReportForAgentSellerIep(testOrder.getId());
                break;
            default:
                Assertions.fail("Unknown counterparty type");
        }
        if (Objects.isNull(sellerCounterparty.getContractNumber())) {
            Assertions.assertThat(agentReport.getNumberContract()).isEqualTo(testOrder.getSeller().getId().toString());
        } else {
            Assertions.assertThat(agentReport.getNumberContract()).isEqualTo(contractNo);
        }
        if (Objects.isNull(sellerCounterparty.getContractTime())) {
            Assertions.assertThat(agentReport.getDateContract().withZoneSameInstant(ZoneOffset.UTC).toLocalDateTime()).isEqualTo(LocalDateTime.ofEpochSecond(testOrder.getSeller().getRegistrationTime(), 0, ZoneOffset.UTC));
        } else {
            Assertions.assertThat(agentReport.getDateContract().withZoneSameInstant(ZoneOffset.UTC).toLocalDateTime()).isEqualTo(contractDateTime);
        }
        if (isAgentSeller) {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_ADVANCE, FiscalReceiptRequestType.AGENT_PAYMENT));
        } else {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        }
        //
        orderFlowTestUtils.transferMoneyToSellers(testOrder.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_PAYMENT_WAIT);
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(testOrder.getId(), sellerCounterPartyId, OrderFlowTestUtils.FAIL_TEXT_ORDER_STATUS_PAY_WAIT);
        //
        orderFlowTestUtils.validateMoneyToSellers(testOrder.getId());
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.changeSellerCounterpartyFailPayoutActiveOrDone(testOrder.getId(), sellerCounterPartyId, OrderFlowTestUtils.FAIL_TEXT_ORDER_STATUS_COMPLETE);
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), 112_500_00, null, TcbBankService.SCHEMA, TransactionState.DONE);
        //
        _01_orderFlow_happyPathOkay_validateOrderPositions(testOrder);
        //
        return orderInfo.getId();
    }

    private void _01_orderFlow_happyPathOkay_validateOrderPositions(OrderDTO orderDto) {
        orderFlowTestUtils.validateOrderPositions(orderDto.getId(), orderDto.getItems().get(0).getProductId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(50_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(12_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(37_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(12_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(37_500_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        orderFlowTestUtils.validateOrderPositions(orderDto.getId(), orderDto.getItems().get(1).getProductId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(40_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(30_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(30_000_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        orderFlowTestUtils.validateOrderPositions(orderDto.getId(), orderDto.getItems().get(2).getProductId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(30_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(22_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(22_500_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        orderFlowTestUtils.validateOrderPositions(orderDto.getId(), orderDto.getItems().get(3).getProductId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(5_000_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(15_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(5_000_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(15_000_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        orderFlowTestUtils.validateOrderPositions(orderDto.getId(), orderDto.getItems().get(4).getProductId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
    }

    @Test
    @Transactional
    public void _UsualSeller_01_HappyPath_Phys_Okay() {
        long ordersId = _01_OrderFlow_HappyPathOkay(false, usualSellerCounterpartyId, null, null);
        Api2Response<List<BankOperationDTO>> bankOperations = orderFlowTestUtils.getApiV2AdminBankOperations(ordersId);
        BankOperationDTO payoutOp = OrderFlowTestUtils.findBankOperation(bankOperations.getData(), OperationType.SELLER_PAYOUT);
        Assertions.assertThat(payoutOp.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(112_500_00, 2));
        Assertions.assertThat(payoutOp.getPaymentAccount()).isEqualTo("*************.......");
        Assertions.assertThat(payoutOp.getCardNumber()).isNull();
        Assertions.assertThat(payoutOp.getCreateTime()).isBeforeOrEqualTo(ZonedDateTime.now());
        Assertions.assertThat(payoutOp.getPaymentSystemTime()).isEqualTo(ZonedDateTime.of(2022, 5, 30, 12, 56, 22, 0, ZoneOffset.UTC));
        //
        Map<String, String> tcbPayoutsInfo = orderFlowTestTcbMock.getOperationInfo(payoutOp.getUuid());
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_TCB_LOGIN)
                .isEqualTo(OrderFlowTestTcbMock.TCB_LOGIN_OSK_GROUP);
        //
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ROUTE)
                .isEqualTo(TcbBankClient.API_V1_ROUTE_ACCOUNT_EXTERNAL_CREDIT);
        BigDecimal payoutAmount = new BigDecimal(tcbPayoutsInfo.get(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_AMOUNT));
        Assertions.assertThat(payoutAmount).isEqualByComparingTo(payoutOp.getAmount().movePointRight(2));
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_COMMENTS)
                .isEqualTo("Оплата за реализованный товар по заказу № " + ordersId + " по агентскому договору № 27 от 22.08.17 на сайте: www.oskelly.ru. НДС не облагается");
        //
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_ID)
                .isEqualTo("*************.......");
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_INN)
                .isEqualTo("************");
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_BIC)
                .isEqualTo("*********");
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_NAME)
                .isEqualTo("... Магомед ...");
    }

    @Test
    @Transactional
    public void _UsualSeller_01_HappyPath_Card_Okay() {
        Long counterPartyId = prepareUsualSellerCardData();
        orderFlowTestUtils.allowCardPayoutForUser(usualSellerId);
        //
        long ordersId = _01_OrderFlow_HappyPathOkay(false, counterPartyId, null, null);
        Api2Response<List<BankOperationDTO>> bankOperations = orderFlowTestUtils.getApiV2AdminBankOperations(ordersId);
        BankOperationDTO payoutOp = OrderFlowTestUtils.findBankOperation(bankOperations.getData(), OperationType.SELLER_PAYOUT);
        Assertions.assertThat(payoutOp.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(112_500_00, 2));
        Assertions.assertThat(payoutOp.getPaymentAccount()).isNull();
        Assertions.assertThat(payoutOp.getCardNumber()).isEqualTo("4274 32** **** 2991");
        Assertions.assertThat(payoutOp.getCreateTime()).isBeforeOrEqualTo(ZonedDateTime.now());
        Assertions.assertThat(payoutOp.getPaymentSystemTime()).isEqualTo(ZonedDateTime.of(2022, 5, 30, 12, 56, 22, 0, ZoneOffset.UTC));
        //
        Map<String, String> tcbPayoutsInfo = orderFlowTestTcbMock.getOperationInfo(payoutOp.getUuid());
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_TCB_LOGIN)
                .isEqualTo(OrderFlowTestTcbMock.TCB_LOGIN_OSK_GROUP);
        //
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ROUTE)
                .isEqualTo(TcbBankClient.API_V1_ROUTE_CARD_REGISTERED_CREDIT);
        BigDecimal payoutAmount = new BigDecimal(tcbPayoutsInfo.get(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_AMOUNT));
        Assertions.assertThat(payoutAmount).isEqualByComparingTo(payoutOp.getAmount().movePointRight(2));
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_COMMENTS)
                .isEqualTo("Оплата за реализованный товар по заказу № " + ordersId + " по агентскому договору № 27 от 22.08.17 на сайте: www.oskelly.ru. НДС не облагается");
        //
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_CARD_ID)
                .isEqualTo("*********");
    }

    @Test
    @Transactional
    public void _AgentSeller_01_HappyPath_Jur_Okay() {
        long ordersId = _01_OrderFlow_HappyPathOkay(true, agentSellerCounterpartyId, "04/23", LocalDateTime.now());
        Api2Response<List<BankOperationDTO>> bankOperations = orderFlowTestUtils.getApiV2AdminBankOperations(ordersId);
        BankOperationDTO payoutOp = OrderFlowTestUtils.findBankOperation(bankOperations.getData(), OperationType.SELLER_PAYOUT);
        Assertions.assertThat(payoutOp.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(112_500_00, 2));
        Assertions.assertThat(payoutOp.getPaymentAccount()).isEqualTo("*************.......");
        Assertions.assertThat(payoutOp.getCardNumber()).isNull();
        Assertions.assertThat(payoutOp.getCreateTime()).isBeforeOrEqualTo(ZonedDateTime.now());
        Assertions.assertThat(payoutOp.getPaymentSystemTime()).isEqualTo(ZonedDateTime.of(2022, 5, 30, 12, 56, 22, 0, ZoneOffset.UTC));
        //
        Map<String, String> tcbPayoutsInfo = orderFlowTestTcbMock.getOperationInfo(payoutOp.getUuid());
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_TCB_LOGIN)
                .isEqualTo(OrderFlowTestTcbMock.TCB_LOGIN_OSK_GROUP);
        //
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ROUTE)
                .isEqualTo(TcbBankClient.API_V1_ROUTE_ACCOUNT_EXTERNAL_CREDIT);
        BigDecimal payoutAmount = new BigDecimal(tcbPayoutsInfo.get(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_AMOUNT));
        Assertions.assertThat(payoutAmount).isEqualByComparingTo(payoutOp.getAmount().movePointRight(2));
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_COMMENTS)
                .isEqualTo("Оплата за реализованный товар по заказу № " + ordersId + " по агентскому договору № 10919 от 28.03.19 на сайте: www.oskelly.ru. НДС не облагается");
        //
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_ID)
                .isEqualTo("*************.......");
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_INN)
                .isEqualTo("**********");
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_BIC)
                .isEqualTo("*********");
        Assertions.assertThat(tcbPayoutsInfo).extracting(OrderFlowTestTcbMock.MockTcbServer.PROP_NAME_ACCOUNT_NAME)
                .isEqualTo("OOO \"Триумфальная арка\"");
    }

    @Test
    @Transactional
    public void _AgentSeller_01_HappyPath_Iep_Okay() {
        Long counterPartyId = prepareAgentSellerIndPData();
        //
        long ordersId = _01_OrderFlow_HappyPathOkay(true, counterPartyId, "15/05", LocalDateTime.now());
        Api2Response<List<BankOperationDTO>> bankOperations = orderFlowTestUtils.getApiV2AdminBankOperations(ordersId);
        BankOperationDTO payoutOp = OrderFlowTestUtils.findBankOperation(bankOperations.getData(), OperationType.SELLER_PAYOUT);
        Assertions.assertThat(payoutOp.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(112_500_00, 2));
        Assertions.assertThat(payoutOp.getPaymentAccount()).isEqualTo("00002810460001020304");
        Assertions.assertThat(payoutOp.getCardNumber()).isNull();
        Assertions.assertThat(payoutOp.getCreateTime()).isBeforeOrEqualTo(ZonedDateTime.now());
        Assertions.assertThat(payoutOp.getPaymentSystemTime()).isEqualTo(ZonedDateTime.of(2022, 5, 30, 12, 56, 22, 0, ZoneOffset.UTC));
    }

    @Test
    @Transactional
    public void _AgentSeller_02_PickupDeclinedFullOkay() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)
                .agentSellerId(agentSellerId)
                .sellerCounterpartyId(agentSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(5)
                .confirmPositions(Collections.emptyList())
                .refusePositions(ImmutableList.of(1, 2, 3, 4, 5))

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())

                .build());
        //
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_REVERSE));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Collections.emptyList());
    }

    @Test
    @Transactional
    public void _UsualSeller_02_PickupDeclinedFullOkay() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(5)
                .confirmPositions(Collections.emptyList())
                .refusePositions(ImmutableList.of(1, 2, 3, 4, 5))

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())

                .build());
        //
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_REVERSE));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Collections.emptyList());
    }

    @Test
    @Transactional
    public void _AgentSeller_03_RejectedFullOkay() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)
                .agentSellerId(agentSellerId)
                .sellerCounterpartyId(agentSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(5)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())

                .build());
        //
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_REVERSE));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Collections.emptyList());
        //
        OrderDTO orderDTO = orderFlowTestUtils.loadOrderSuccessfull(orderInfo.getId(), true);
        Assertions.assertThat(orderDTO.getNumbers().getTotalItemSaleAmount()).isEqualByComparingTo(BigDecimal.valueOf(150_000_00L, 2));
        Assertions.assertThat(orderDTO.getNumbers().getDeliveryWithCustomsAmount()).isNull();
    }

    @Test
    @Transactional
    public void _UsualSeller_03_RejectedFullOkay() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(5)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(ImmutableList.of(1, 2, 3, 4, 5))
                .defectsByPositions(Collections.emptyList())
                .cleaningsByPositions(Collections.emptyList())

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())

                .build());
        //
        orderFlowTestUtils.validateOrderState(orderInfo.getId(), OrderState.REFUND);
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(), Lists.newArrayList(OperationType.HOLD, OperationType.HOLD_REVERSE));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 150_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(), Collections.emptyList());
        //
        OrderDTO orderDTO = orderFlowTestUtils.loadOrderSuccessfull(orderInfo.getId(), true);
        Assertions.assertThat(orderDTO.getNumbers().getTotalItemSaleAmount()).isEqualByComparingTo(BigDecimal.valueOf(150_000_00L, 2));
        Assertions.assertThat(orderDTO.getNumbers().getDeliveryWithCustomsAmount()).isNull();
    }

    @Test
    @Transactional
    public void _AgentSeller_04_ComboPathOkay() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)
                .agentSellerId(agentSellerId)
                .sellerCounterpartyId(agentSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(5)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(ImmutableList.of(5))

                .expertisePassPositions(ImmutableList.of(4))
                .expertiseFailPositions(ImmutableList.of(3))
                .defectsByPositions(Lists.newArrayList(null, 1234L, null, null, null))
                .cleaningsByPositions(Lists.newArrayList(4321L, null, null, null, null))

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())

                .orderPayAuthAmount(BigDecimal.valueOf(150_500_00, 2))
                .orderPayCaptAmount(BigDecimal.valueOf(69_266_00, 2))
                .sellerPayoutAmount(46_945_00L)
                .noAgentReportCompare(true)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 81_234_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 40_000_00 + 18_766_00 + 10_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.AGENT_ADVANCE, FiscalReceiptRequestType.AGENT_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_ADVANCE, 0L,
                Lists.newArrayList(40_000_00L, 18_766_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE, OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE, OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                Lists.newArrayList(40_000_00L, 18_766_00L, 10_000_00L, orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_COMMODITY, OrderFlowTestUtils.ITEM_KIND_COMMODITY, OrderFlowTestUtils.ITEM_KIND_COMMODITY, OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY, OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY));
        //
        OrderDTO orderDTO = orderFlowTestUtils.loadOrderSuccessfull(orderInfo.getId(), true);
        Assertions.assertThat(orderDTO.getNumbers().getTotalItemSaleAmount()).isEqualByComparingTo(BigDecimal.valueOf(150_000_00L, 2));
        Assertions.assertThat(orderDTO.getNumbers().getDeliveryWithCustomsAmount()).isNull();
        //
        _04_orderFlow_comboPathOkay_validateOrderPositions(orderInfo);
    }

    @Test
    @Transactional
    public void _UsualSeller_04_ComboPathOkay() {
        OrderDTO orderInfo = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(5)
                .confirmPositions(ImmutableList.of(1, 2, 3, 4))
                .refusePositions(ImmutableList.of(5))

                .expertisePassPositions(ImmutableList.of(4))
                .expertiseFailPositions(ImmutableList.of(3))
                .defectsByPositions(Lists.newArrayList(null, 1234L, null, null, null))
                .cleaningsByPositions(Lists.newArrayList(4321L, null, null, null, null))

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())

                .orderPayAuthAmount(BigDecimal.valueOf(150_500_00, 2))
                .orderPayCaptAmount(BigDecimal.valueOf(69_266_00, 2))
                .sellerPayoutAmount(46_945_00L)
                .noAgentReportCompare(true)

                .build());
        //
        orderFlowTestUtils.validateBankOperationTypeList(orderInfo.getId(),
                ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_REVERSE, 81_234_00);
        orderFlowTestUtils.validateBankOperation(orderInfo.getId(), OperationType.HOLD_COMPLETE, 40_000_00 + 18_766_00 + 10_000_00 + orderInfo.getDeliveryCost().movePointRight(2).longValue());
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(orderInfo.getId(),
                ImmutableList.of(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_ADVANCE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(orderInfo.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                Lists.newArrayList(orderInfo.getDeliveryCost().movePointRight(2).longValue()),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_KIND_SERVICE),
                Lists.newArrayList(OrderFlowTestUtils.ITEM_PAY_KIND_FULLPAY));
        //
        OrderDTO orderDTO = orderFlowTestUtils.loadOrderSuccessfull(orderInfo.getId(), true);
        Assertions.assertThat(orderDTO.getNumbers().getTotalItemSaleAmount()).isEqualByComparingTo(BigDecimal.valueOf(150_000_00L, 2));
        Assertions.assertThat(orderDTO.getNumbers().getDeliveryWithCustomsAmount()).isNull();
        //
        _04_orderFlow_comboPathOkay_validateOrderPositions(orderInfo);
    }

    private void _04_validateExpertiseIllegalStates(ResponseEntity<String> expertiseResponse) {
        Assertions.assertThat(expertiseResponse.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownException = orderFlowTestUtils.readExceptionFromText(expertiseResponse.getBody());
        Assertions.assertThat(thrownException.getMessage()).matches("Заказ .*: не удается изменить состояние экспертизы, некорректный статус \\(HOLD_COMPLETED\\)");
    }

    private void _04_validateExpertiseOpUnconfirmed(ResponseEntity<String> expertiseResponse) {
        Assertions.assertThat(expertiseResponse.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownException = orderFlowTestUtils.readExceptionFromText(expertiseResponse.getBody());
        Assertions.assertThat(thrownException.getMessage()).matches("Заказ .*: не удается изменить состояние экспертизы, позиция заказа .* не подтверждена");
    }

    private void _04_orderFlow_comboPathOkay_validateOrderPositions(OrderDTO orderDto) {
        orderFlowTestUtils.validateOrderPositions(orderDto.getId(), orderDto.getItems().get(0).getProductId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(50_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(12_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(37_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getSellerChargeAmount()).isNull();
        });
        orderFlowTestUtils.validateOrderPositions(orderDto.getId(), orderDto.getItems().get(1).getProductId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(40_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(30_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(30_000_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.ZERO);
        });
        orderFlowTestUtils.validateOrderPositions(orderDto.getId(), orderDto.getItems().get(2).getProductId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(30_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(22_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.ZERO);
            Assertions.assertThat(it.getSellerChargeAmount()).isNull();
        });
        orderFlowTestUtils.validateOrderPositions(orderDto.getId(), orderDto.getItems().get(3).getProductId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(20_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(5_000_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(15_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(5_000_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(13_766_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.valueOf(1234_00, 2));
        });
        orderFlowTestUtils.validateOrderPositions(orderDto.getId(), orderDto.getItems().get(4).getProductId(), it -> {
            Assertions.assertThat(it.getAmount()).isEqualByComparingTo(BigDecimal.valueOf(10_000_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(3_179_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.valueOf(4_321_00, 2));
        });
    }

    @Test
    @Transactional
    public void _AgentSeller_05_SingleItemDefect() {
        _AgentSeller_05_SingleItemDefectTestCall(
                ImmutableList.of(1647L),
                Collections.emptyList(),
                null);
    }

    public void _AgentSeller_05_SingleItemDefectTestCall(List<Long> defectsByPositions, List<Long> serviceByPositions, OrderFlowTestUtils.ComboExpertiseMode comboExpertiseMode) {
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)
                .agentSellerId(agentSellerId)
                .sellerCounterpartyId(agentSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(defectsByPositions)
                .cleaningsByPositions(serviceByPositions)
                .comboExpertiseMode(comboExpertiseMode)

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())
                .orderPayAuthAmount(BigDecimal.valueOf(10_500_00, 2))
                .orderPayCaptAmount(BigDecimal.valueOf(8_853_00, 2))
                .sellerPayoutAmount(5_853_00L)

                .build());
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_REVERSE, 1_647_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 8_353_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, 5_853_00L);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_ADVANCE, FiscalReceiptRequestType.AGENT_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.AGENT_ADVANCE, 0L,
                ImmutableList.of( 8_353_00L, testOrder.getDeliveryCost().movePointRight(2).longValue()),
                ImmutableList.of(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                ImmutableList.of(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                ImmutableList.of(8_353_00L, testOrder.getDeliveryCost().movePointRight(2).longValue()),
                ImmutableList.of(ITEM_KIND_COMMODITY, ITEM_KIND_SERVICE),
                ImmutableList.of(ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY));
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), 5_853_00L, null, TcbBankService.SCHEMA, TransactionState.DONE);
    }

    @Test
    @Transactional
    public void _UsualSeller_05_SingleItemDefect() {
        _UsualSeller_05_SingleItemDefectTestCall(
                ImmutableList.of(1647L),
                Collections.emptyList(),
                null);
    }

    public void _UsualSeller_05_SingleItemDefectTestCall(List<Long> defectsByPositions, List<Long> serviceByPositions, OrderFlowTestUtils.ComboExpertiseMode comboExpertiseMode) {
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(defectsByPositions)
                .cleaningsByPositions(serviceByPositions)
                .comboExpertiseMode(comboExpertiseMode)

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())
                .orderPayAuthAmount(BigDecimal.valueOf(10_500_00, 2))
                .orderPayCaptAmount(BigDecimal.valueOf(8_853_00, 2))
                .sellerPayoutAmount(5_853_00L)
                .refundAfterCompleteAmount(1_234_56L)

                .build());
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_REVERSE, 1_647_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 8_353_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, 5_853_00L);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.REFUND, 1_234_56);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                ImmutableList.of(testOrder.getDeliveryCost().movePointRight(2).longValue()),
                ImmutableList.of(ITEM_KIND_ADVANCE),
                ImmutableList.of(ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                ImmutableList.of(testOrder.getDeliveryCost().movePointRight(2).longValue()),
                ImmutableList.of(ITEM_KIND_SERVICE),
                ImmutableList.of(ITEM_PAY_KIND_FULLPAY));
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), 5_853_00L, null, TcbBankService.SCHEMA, TransactionState.DONE);
        //
        orderFlowTestUtils.validateOrderPositions(testOrder.getId(), testOrder.getItems().get(0).getProductId(), it -> {
            Assertions.assertThat(it.getMarketplaceCommissionAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountRaw()).isEqualByComparingTo(BigDecimal.valueOf(7_500_00, 2));
            Assertions.assertThat(it.getMarketplaceCommissionAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(2_500_00, 2));
            Assertions.assertThat(it.getSellerPayoutAmountNet()).isEqualByComparingTo(BigDecimal.valueOf(5_853_00, 2));
            Assertions.assertThat(it.getSellerChargeAmount()).isEqualByComparingTo(BigDecimal.valueOf(1647_00, 2));
        });
        //
        OrderDTO orderDtoOnDone = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(orderDtoOnDone.getItems()).hasSize(1).allSatisfy(it -> {
           Assertions.assertThat(it.getAmountsDetails().getDisplayAmount()).isEqualByComparingTo(it.getAmount());
           Assertions.assertThat(it.getAmountsDetails().getPayableAmount()).isEqualByComparingTo(it.getAmount());
           Assertions.assertThat(it.getAmountsDetails().getRawSellAmount()).isEqualByComparingTo(it.getAmount());
        });
    }

    @Test
    @Transactional
    public void _AgentSeller_06_SingleItemCleans() {
        _AgentSeller_06_SingleItemCleansTestCall(
                Collections.emptyList(),
                ImmutableList.of(1647L),
                null);
    }

    public void _AgentSeller_06_SingleItemCleansTestCall(List<Long> defectsByPositions, List<Long> serviceByPositions, OrderFlowTestUtils.ComboExpertiseMode comboExpertiseMode) {
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isAgentSeller(true)
                .agentSellerId(agentSellerId)
                .sellerCounterpartyId(agentSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(defectsByPositions)
                .cleaningsByPositions(serviceByPositions)
                .comboExpertiseMode(comboExpertiseMode)

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())
                .orderPayAuthAmount(BigDecimal.valueOf(10_500_00, 2))
                .orderPayCaptAmount(BigDecimal.valueOf(10_500_00, 2))
                .sellerPayoutAmount(5_853_00L)

                .build());
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, 5_853_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_ADVANCE, FiscalReceiptRequestType.AGENT_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.AGENT_ADVANCE, 0L,
                ImmutableList.of( 10_000_00L, testOrder.getDeliveryCost().movePointRight(2).longValue()),
                ImmutableList.of(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                ImmutableList.of(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                ImmutableList.of(10_000_00L, testOrder.getDeliveryCost().movePointRight(2).longValue()),
                ImmutableList.of(ITEM_KIND_COMMODITY, ITEM_KIND_SERVICE),
                ImmutableList.of(ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY));
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), 5_853_00, null, TcbBankService.SCHEMA, TransactionState.DONE);
    }

    @Test
    @Transactional
    public void _UsualSeller_06_SingleItemCleans() {
        _UsualSeller_06_SingleItemCleansTestCall(
                Collections.emptyList(),
                ImmutableList.of(1647L),
                null);
    }

    public void _UsualSeller_06_SingleItemCleansTestCall(List<Long> defectsByPositions, List<Long> serviceByPositions, OrderFlowTestUtils.ComboExpertiseMode comboExpertiseMode) {
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(defectsByPositions)
                .cleaningsByPositions(serviceByPositions)
                .comboExpertiseMode(comboExpertiseMode)

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())
                .orderPayAuthAmount(BigDecimal.valueOf(10_500_00, 2))
                .orderPayCaptAmount(BigDecimal.valueOf(10_500_00, 2))
                .sellerPayoutAmount(5_853_00L)

                .build());
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT));
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, 5_853_00);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                ImmutableList.of(testOrder.getDeliveryCost().movePointRight(2).longValue()),
                ImmutableList.of(ITEM_KIND_ADVANCE),
                ImmutableList.of(ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                ImmutableList.of(testOrder.getDeliveryCost().movePointRight(2).longValue()),
                ImmutableList.of(ITEM_KIND_SERVICE),
                ImmutableList.of(ITEM_PAY_KIND_FULLPAY));
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), 5_853_00, null, TcbBankService.SCHEMA, TransactionState.DONE);
        //
        OrderDTO orderDtoOnDone = orderFlowTestUtils.loadOrderSuccessfull(testOrder.getId(), true);
        Assertions.assertThat(orderDtoOnDone.getItems()).hasSize(1).allSatisfy(it -> {
            Assertions.assertThat(it.getAmountsDetails().getDisplayAmount()).isEqualByComparingTo(it.getAmount());
            Assertions.assertThat(it.getAmountsDetails().getPayableAmount()).isEqualByComparingTo(it.getAmount());
            Assertions.assertThat(it.getAmountsDetails().getRawSellAmount()).isEqualByComparingTo(it.getAmount());
        });
    }

    @Test
    @Transactional
    public void _AgentSeller_07_serviceThenDefects_singleItemDefect() {
        _AgentSeller_05_SingleItemDefectTestCall(
                ImmutableList.of(1647L),
                ImmutableList.of(1000L),
                OrderFlowTestUtils.ComboExpertiseMode.SERVICE_THEN_DEFECTS);
    }

    @Test
    @Transactional
    public void _AgentSeller_07_defectsThenService_singleItemCleans() {
        _AgentSeller_06_SingleItemCleansTestCall(
                ImmutableList.of(1000L),
                ImmutableList.of(1647L),
                OrderFlowTestUtils.ComboExpertiseMode.DEFECTS_THEN_SERVICE);
    }

    @Test
    @Transactional
    public void _UsualSeller_07_serviceThenDefects_singleItemDefect() {
        _UsualSeller_05_SingleItemDefectTestCall(
                ImmutableList.of(1647L),
                ImmutableList.of(1000L),
                OrderFlowTestUtils.ComboExpertiseMode.SERVICE_THEN_DEFECTS);
    }

    @Test
    @Transactional
    public void _UsualSeller_07_defectsThenService_singleItemCleans() {
        _UsualSeller_06_SingleItemCleansTestCall(
                ImmutableList.of(1000L),
                ImmutableList.of(1647L),
                OrderFlowTestUtils.ComboExpertiseMode.DEFECTS_THEN_SERVICE);
    }

    @Test
    @Transactional
    public void _UsualSeller_07_defectsWithService_defectsWithService() {
        OrderDTO testOrder = orderFlowTestUtils.processTestOrderAuthorizeReverseCapture(this, OrderFlowTestUtils.TestConfig.builder()
                .isUsualSeller(true)
                .usualSellerId(usualSellerId)
                .sellerCounterpartyId(usualSellerCounterpartyId)
                .pickupDeliveryAepId(pickupId)
                .targetDeliveryAepId(deliveryId)

                .itemsCount(1)
                .confirmPositions(ImmutableList.of(1))
                .refusePositions(Collections.emptyList())

                .expertisePassPositions(Collections.emptyList())
                .expertiseFailPositions(Collections.emptyList())
                .defectsByPositions(ImmutableList.of(1000L))
                .cleaningsByPositions(ImmutableList.of(2000L))
                .comboExpertiseMode(OrderFlowTestUtils.ComboExpertiseMode.DEFECTS_WITH_SERVICE)

                .legalEntityOnecId(OrderFlowTestFixtures.OnecEntityType.ONEC_ENTITY_GROUP.getOnecUuid())
                .orderPayAuthAmount(BigDecimal.valueOf(10_500_00, 2))
                .orderPayCaptAmount(BigDecimal.valueOf(9_500_00, 2))
                .sellerPayoutAmount(4_500_00L)
                .refundAfterCompleteAmount(1_234_56L)

                .build());
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
        //
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), ImmutableList.of(OperationType.HOLD, OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE, OperationType.SELLER_PAYOUT, OperationType.REFUND));

        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_REVERSE, 1_000_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD_COMPLETE, 9_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.SELLER_PAYOUT, 4_500_00);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.REFUND, 1_234_56);
        //
        orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                ImmutableList.of(testOrder.getDeliveryCost().movePointRight(2).longValue()),
                ImmutableList.of(ITEM_KIND_ADVANCE),
                ImmutableList.of(ITEM_PAY_KIND_ADVANCE));
        orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                ImmutableList.of(testOrder.getDeliveryCost().movePointRight(2).longValue()),
                ImmutableList.of(ITEM_KIND_SERVICE),
                ImmutableList.of(ITEM_PAY_KIND_FULLPAY));
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), 4_500_00L, null, TcbBankService.SCHEMA, TransactionState.DONE);
    }

    private void _08_OrderFlow_ValidateHoldCompleted(OrderDTO orderDto, OrderState orderState, OrderPaymentState orderPaymentState, boolean isAgentSeller) {
        orderFlowTestUtils.validateOrderState(orderDto.getId(), orderState);
        orderFlowTestUtils.validateOrderPayment(orderDto.getId(), TcbBankService.SCHEMA, orderPaymentState);
        if (isAgentSeller) {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(orderDto.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_ADVANCE));
            orderFlowTestUtils.validateOrderFiscalReceipt(orderDto.getId(), FiscalReceiptRequestType.AGENT_ADVANCE, 0L,
                    Lists.newArrayList( 8_353_00L, orderDto.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_ADVANCE, ITEM_KIND_ADVANCE),
                    Lists.newArrayList(ITEM_PAY_KIND_ADVANCE, ITEM_PAY_KIND_ADVANCE));

        } else {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(orderDto.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE));
            orderFlowTestUtils.validateOrderFiscalReceipt(orderDto.getId(), FiscalReceiptRequestType.DELIVERY_ADVANCE, 0L,
                    Lists.newArrayList(orderDto.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_ADVANCE),
                    Lists.newArrayList(ITEM_PAY_KIND_ADVANCE));
        }
        //
        orderFlowTestUtils.validateBankOperation(orderDto.getId(), OperationType.HOLD_REVERSE, 1_647_00);
        orderFlowTestUtils.validateBankOperation(orderDto.getId(), OperationType.HOLD_COMPLETE, 8_353_00 + orderDto.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(orderDto.getId(), Lists.newArrayList(OperationType.HOLD,  OperationType.HOLD_REVERSE, OperationType.HOLD_COMPLETE));
    }

    @SneakyThrows
    private void _08_OrderFlow_ChargeMoneyOkay(boolean isAgentSeller) {
        List<Product> products = getProductsForOrdersWithSellerType(isAgentSeller);
        commitAndStartNewTransaction();
        //
        GroupedCart cartInfo = orderFlowTestUtils.fillCart(products.subList(0, 1));
        OrderDTO cartOrder = cartInfo.getGroup(products.get(0).getSeller().getId());
        //
        OrderService.InitOrderResult testOrder1 = holdOrderWithPromoCodePS(products.get(0).getSeller(), null, TcbBankService.SCHEMA);
        rollbackAndStartNewTransaction();
        //
        OrderDTO testOrder = orderFlowTestUtils.loadOrderSuccessfull(testOrder1.getOrderId(), true);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD_PROCESSING);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_INPROGRESS);
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD));
        //
        orderFlowTestUtils.callOrderHoldCallback(testOrder1.getOrderId(), testOrder1.getBank_url().replace("https://", "http://"));
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.HOLD);
        orderFlowTestUtils.validateOrderPayment(testOrder.getId(), TcbBankService.SCHEMA, OrderPaymentState.AUTHORIZE_DONE);
        //
        orderFlowTestUtils.validateBankOperation(testOrder.getId(), OperationType.HOLD, 10_000_00 + testOrder.getDeliveryCost().movePointRight(2).longValue());
        orderFlowTestUtils.validateBankOperationTypeList(testOrder.getId(), Lists.newArrayList(OperationType.HOLD));
        //
        orderFlowTestUtils.rejectOrApprovePosition(testOrder.getItems().get(0).getId(), true);
        //
        Long sellerCounterPartyId = isAgentSeller ? agentSellerCounterpartyId : usualSellerCounterpartyId;
        orderFlowTestUtils.changeSellerCounterparty(testOrder.getId(), sellerCounterPartyId, HttpStatus.Series.SUCCESSFUL);
        orderFlowTestUtils.changeAddressEndpoint(testOrder.getId(), pickupId, deliveryId);
        orderFlowTestUtils.takeOurselves(testOrder.getId(), null);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_SELLER_TO_OFFICE, true);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_FROM_SELLER_TO_OFFICE, true);
        //
        ResponseEntity<String> chargeResponseFail = orderFlowTestUtils.adminPanel_Charge(testOrder.getId());
        Assertions.assertThat(chargeResponseFail.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownCharge = objectMapper.readValue(chargeResponseFail.getBody(), Exception.class);
        Assertions.assertThat(thrownCharge.getMessage())
                .matches("Заказ .*, подтверждение списания ДС невозможно: некорректное состояние заказа");
        rollbackAndStartNewTransaction();
        //
        ResponseEntity<String> sendItResponseFail = orderFlowTestUtils.sendOurselves(testOrder.getId(), null);
        Assertions.assertThat(sendItResponseFail.getStatusCode().is4xxClientError()).isTrue();
        Exception thrownSendIt = objectMapper.readValue(sendItResponseFail.getBody(), Exception.class);
        Assertions.assertThat(thrownSendIt.getMessage())
                .matches("Заказ .*, подтверждение списания ДС невозможно: некорректное состояние заказа");
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.adminsApi1expertise(testOrder.getItems().get(0).getId(), true, OrderFlowTestUtils.ExpertiseAction.EXPERTISE_DEFECT_IT, 1647L);
        //
        ResponseEntity<String> chargeRsp1st = orderFlowTestUtils.adminPanel_Charge(testOrder.getId());
        Assertions.assertThat(chargeRsp1st.getStatusCode().is2xxSuccessful()).isTrue();
        rollbackAndStartNewTransaction();
        //
        _08_OrderFlow_ValidateHoldCompleted(testOrder, OrderState.HOLD_COMPLETED, OrderPaymentState.CAPTURE_INPROGRESS, isAgentSeller);
        //
        ResponseEntity<String> chargeRsp2nd = orderFlowTestUtils.adminPanel_Charge(testOrder.getId());
        Assertions.assertThat(chargeRsp2nd.getStatusCode().is4xxClientError()).isTrue();
        Exception thrown = objectMapper.readValue(chargeRsp2nd.getBody(), Exception.class);
        Assertions.assertThat(thrown.getMessage())
                .matches("Не валидное состояние заказа Оплачен успешно для .* при списании денег");
        rollbackAndStartNewTransaction();
        //
        _08_OrderFlow_ValidateHoldCompleted(testOrder, OrderState.HOLD_COMPLETED, OrderPaymentState.CAPTURE_INPROGRESS, isAgentSeller); // Nothing changed when we called charge again
        //
        orderFlowTestUtils.adminPanel_Charge(testOrder.getId());
        rollbackAndStartNewTransaction();
        _08_OrderFlow_ValidateHoldCompleted(testOrder, OrderState.HOLD_COMPLETED, OrderPaymentState.CAPTURE_INPROGRESS, isAgentSeller); // Nothing changed when we called delivery company (and moneycharging inside)
        //
        orderFlowTestUtils.processHoldComplete(testOrder);
        rollbackAndStartNewTransaction();
        _08_OrderFlow_ValidateHoldCompleted(testOrder, OrderState.MONEY_TRANSFERRED, OrderPaymentState.CAPTURE_DONE, isAgentSeller); // Nothing changed when we called delivery company (and moneycharging inside)
        //
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.OURSELVES_FROM_OFFICE_TO_BUYER, true);
        orderFlowTestUtils.changeDeliveryState(testOrder.getId(), DeliveryState.DELIVERED_TO_BUYER, true);
        if (isAgentSeller) {
            orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.AGENT_PAYMENT, 0L,
                    Lists.newArrayList(8_353_00L, testOrder.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_COMMODITY, ITEM_KIND_SERVICE),
                    Lists.newArrayList(ITEM_PAY_KIND_FULLPAY, ITEM_PAY_KIND_FULLPAY));
        } else {
            orderFlowTestUtils.validateOrderFiscalReceipt(testOrder.getId(), FiscalReceiptRequestType.DELIVERY_PAYMENT, 0L,
                    Lists.newArrayList(testOrder.getDeliveryCost().movePointRight(2).longValue()),
                    Lists.newArrayList(ITEM_KIND_SERVICE),
                    Lists.newArrayList(ITEM_PAY_KIND_FULLPAY));
        }
        //
        orderFlowTestUtils.sendAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.confirmAgentReport(testOrder.getId(), true);
        rollbackAndStartNewTransaction();
        //
        orderFlowTestUtils.prepareSellerPayout(testOrder.getId(), true);
        commitAndStartNewTransaction();
        //
        orderFlowTestUtils.validateSellerPayout(testOrder.getId(), 5_853_00, null, TcbBankService.SCHEMA, TransactionState.PREPARED);
        if (isAgentSeller) {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.AGENT_ADVANCE, FiscalReceiptRequestType.AGENT_PAYMENT));
        } else {
            orderFlowTestUtils.validateFiscalReceiptsTypeList(testOrder.getId(), Lists.newArrayList(FiscalReceiptRequestType.DELIVERY_ADVANCE, FiscalReceiptRequestType.DELIVERY_PAYMENT));
        }
        //
        List<BankPaymentDTO> bankPaymentTransferDtos = scheduledBankRunner.transferMoneyToSellers();
        Utils.sleepMillis(1000);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.MONEY_PAYMENT_WAIT);
        //
        List<BankPaymentDTO> bankPaymentVerifyDtos = scheduledBankRunner.checkTransferMoneyToSeller();
        Utils.sleepMillis(1000);
        rollbackAndStartNewTransaction();
        orderFlowTestUtils.validateOrderState(testOrder.getId(), OrderState.COMPLETED);
    }

    @Test
    @Transactional
    public void _AgentSeller_08_ChargeMoneyOkay() {
        _08_OrderFlow_ChargeMoneyOkay(true);
    }

    @Test
    @Transactional
    public void _UsualSeller_08_ChargeMoneyOkay() {
        _08_OrderFlow_ChargeMoneyOkay(false);
    }

}
