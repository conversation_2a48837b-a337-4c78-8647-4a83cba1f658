package ru.oskelly.tests.pr.suite6_1.orderflow;

import org.junit.platform.suite.api.SelectClasses;
import org.junit.platform.suite.api.Suite;

@Suite
@SelectClasses({
        // These tests shares context (fine)
        OrderFlowAuthorizeCapturePayoutTcbTest.class,
        OrderFlowAuthorizeExpireTest.class,
        OrderFlowBoutiqueOrderTest.class,
        OrderFlowHoldWithCustomPaymentSystemTest.class,
        OrderFlowOrderBalancePayoutsTest.class,
        OrderFlowRuDutiesTest.class,
        OrderFlowRuRoundTest.class,
        OrderFlowTcbHoldBoundCardsTest.class,
        OrderFlowVariousPaymentsTest.class,
        // These tests requires more contexts (refactor)
        OrderFlowHoldWithPayOptionTest.class,
        // These tests are disabled (enable when you need them)
        OrderFlowDutiesTest.class
})
public class OrderFlow06_1TestSuite {

}
