logging:
  level:
    # httpclient.wire: DEBUG
    # org.apache.http: DEBUG
    # org.hibernate.SQL: DEBUG
    org.springframework.test.context.transaction: WARN # Skip "Began transaction" / "Committed transaction"
    org.mockserver.log: WARN # Skip "received request: " / "returning response:"
    su.reddot.domain.service.device: WARN # Lots of logs in test output, enable when debugging
    su.reddot.domain.service.feed: WARN # Lots of logs in test output, enable when debugging
    su.reddot.domain.service.activity: WARN # Lots of logs in test output, enable when debugging
    su.reddot.domain.service.task: WARN # Lots of logs in test output, enable when debugging (e.g. ScheduledAutoPickupRunner)

boutique:
  boutique-buyers-ids: 12, 27 # using <EMAIL> as boutique buyer in tests (better switch to new backup with real ID)
  boutique-buyer-id-for-automation: 27 # Used as a buyer in automated Boutique order creation
  office-address-endpoint-id: 1584 # address_endpoint.id used as seller address in Boutique orders

payments:
  cb-fallback-mode: plutus/two-step-pay
  payments-service:
    api-url: http://payments:8036
    useAmqp: true
  checkout-com:
    enabled: true
  noon-payments:
    enabled: true
    api-url: http://localhost:8184/payment/v1
    return-url: http://localhost:42
    currencies:
      - "AED"
      - "USD"
      - "EUR"
      - "RUB"
  boutique:
    enabled: true
    return-url: http://localhost:42

payouts:
  cb-payout-2tcb: true
  boutique:
    api-endpoint: http://localhost:8181/api/v1
    api-business-endpoint: http://localhost:8181/api/interfaces/business
    login: T3657102618ID
    signing-key: hdS2TYO4LYvvAYPdb7djJhXPCbS2Ts1HZaFg24h04rcxwp4cn4h8akLapczRfVkt6XEAtJjf0mpseztogPQXj1GPyF0ZFzVjfTAJTETOVciY4t4ZTmLzewjeqZbXRuKBAfmv7UEL5ccgTSTfKbfuYveSKws2C55ACRxZgK08Q8DctcvHg5nQoqNeaDUMBMi8fLOcu27ajgNY1byHaUVGSpnndtYNYoHe0D5Sxtnn7nFgMXn8enMRZLq0xB7bdvk5
    account-number: PAT100002179ID # login on https://paytest.online.tkbbank.ru/ with password 'TzCpLsPv'

bank-account:
  payment-version: stub+tcb
  enable-advance-receipts: true

  jobs:
    create-seller-payouts:
      cron: "-"

  best2pay:
    api-endpoint: http://localhost:8183/webapi
    gateweb-endpoint: http://localhost:8183/gateweb

  tcb: &tcb-stub
    api-endpoint: http://localhost:8181/api/v1
    api-business-endpoint: http://localhost:8181/api/interfaces/business
    login: T2721101360ID
    signing-key: V0fmkT6zjLzsgR2aaHMB3iZWxLC0EExvGV3q41ozjO52bHuFcXJY7uLMasYKqg3OoeVvXNeitnspm6UYq5XQ5HxmjrpMcNicUsP6W8SzTzEUbvzdGattsnSPRJkFxBgQdpE3sXcqoy6FdMkX0mfbtYUGJ8qnYmbrtYRW0EUT7df2pmMeywOkwewoE6vsGYBbxQmo3x6wAvdqT0rwxkXBGZ3oCCs2jCmiLdFJ8xkbysUbbfENP74wb8DGV7VBfem7
    account-number: PAT100000763ID
    return-url: http://localhost:42

  tcb2: *tcb-stub

cash-register:
  starrys:
    taxMode: 1
    group: 0a0bdc20-bff4-4d69-8b4c-6c20b8c5b60d
    endpoint: http://localhost:8182/fr/api/v2/Complex # real & test endpoint: https://kkt4.chekonline.ru/fr/api/v2/Complex
    automationBuyerCheckFromDate: 2020-01-01T00:00:00-00:00
    allow-non-localhost-endpoint: false # true
    password: ********** # 30
    #client-certificate-path: file:///home/<USER>/starrys/test-key.p12
    #client-certificate-password: nopassword
  receipts-sender:
    requestid-prefix: "${random.uuid}-"

spring:
  feed-flyway:
    enabled: true



app:
  default-country:
    iso-code-alpha2: RU
    default-country-id: 191
  bargain:
    close-task:
      enabled: false
    open-task:
      enabled: false
  cart:
    forbidden-crossborder-category-ids: # Empty list or we`ll have some kind of mess in existing tests (will fail on order create)
  master:
    url: http://localhost:42
    ftp-host: mars.internal-dev.oskelly.tech
  publication:
    images-ftp-host: mars.internal-dev.oskelly.tech
    images-ftp-login: boutiques
    images-ftp-passw: a!s@d#
  feed-db:
    enabled: true
    #Синхронизация базы фидов
    sync:
      enabled: true
      product:
        on-change-enabled: true
      user:
        on-change-enabled: true
      order-position:
        on-change-enabled: true
    export:
      enabled: true
      v2:
        yandex-feed-yml:
          enabled: true
        google:
          enabled: true
  queue:
    #Выключатели для приема событий из AMQP
    processor:
      #По умолчанию, события из Rabbit игнорятся, если они были отправлены тем же инстансом
      #Данная опция выключается для упрощения тестирования прогона событий через очередь (отправка и обработка на едином инстансе)
      skip-events-from-the-same-instance: false
      change:
        product:
          enabled: true
        user:
          enabled: true
        environment:
          enabled: true
      new:
        image:
          #Чтение очереди с новыми изображениями для ресайза
          enabled: false
        activity:
          #Чтение очереди с новыми активностями
          enabled: true
        #Переходим на user-auth
        #user-access:
          #Чтение очереди об обращении пользователя к системе
          #enabled: true
        common-fanout:
          #Чтение универсальной fanout рассылки (лайки, баны)
          enabled: true
        user-auth:
          #Получение событий об успешной авторизации пользователя
          enabled: true
        user-register:
          #Получение событий об успешной регистрации пользователя
          enabled: true
        user-subscription:
          #Получение событий о подписке/отписке пользователя на уведомления по email/push/sms/...
          enabled: true

  user-service:
    update-last-access:
      enabled: true

  feedExport:
    oneass:
      products-part-size: 25

  integration:
    opencv:
      baseUrl: http://127.0.0.1
      timeOutInSec: 30
      productsInGroupLimit: 2000
      catalog:
        syncEnabled: false

    currencyrate:
      updateCron: "-"
      rateSource:
        APICHINA:
          apiHost: "http://localhost"
          apiPort: 9091
          ratesUrl: "/fx/usdrate/v1"
          authUrl: "/auth/oauth/v2/token"
          clientId: ${BANK_OF_CHINA_API_CLIENT_ID:qqqqqq}
          clientSecret: ${BANK_OF_CHINA_API_CLIENT_SECRET:wwwwwww}

    mindbox:
      web-secret-key: SECRET
      web-endpoint-id: _FAKE_oskelly.ru
      ios-secret-key: IOS_SECRET
      ios-endpoint-id: _FAKE_oskelly-iOs-sandbox
      android-secret-key: ANDROID_SECRET
      android-endpoint-id: _FAKE_oskelly-Android-sandbox
      triggers:
        enabled: false
        user-auth:
          enabled: false
        user-register:
          enabled: false
        user-subscription:
          enabled: false
        user-change:
          enabled: false

    dalli:
      client-number-s2o-postfix: -OSKELLY-S2O-TEST-2
      client-number-o2b-postfix: -OSKELLY-O2B-TEST-2
    cse:
      add-timestamp-to-client-number: true

  security:
    enableCredsEndpoint: true

  maxmind:
    db-file-path: src/main/resources/maxmind/GeoLite2-Country.mmdb

  paymentOptions:
    yandexPay:
      enabled: true
      ignoreExp: true
      points:
        enabled: true
    yandexSplit:
      enabled: true
      ignoreExp: true
    sbp:
      enabled: true
      ignoreExp: true
    noonApplePay:
      enabled: true
      ignoreExp: true
    noonCard:
      enabled: true
      ignoreExp: true
    tabbySplit:
      enabled: true
      ignoreExp: true
      order: 40
      splitCount: 4
      delay: 1
      delayChronoUnit: MONTHS
      supportedCurrencies: AED,SAR,QAR,KWD,BHD

  commissionGridChange:
    history:
      productsBatchSize: 3
      enabled: true
      schedule: "-" #"0 * * * * ?"
    mainGridsEditEnabled: false

resources:
  images:
    storageType: S3
    urlPath: /
    prefix: http://localhost:4566/oskelly
    cardBrands:
      MIR: "-"
      12STOREEZ: http://localhost:4566/oskelly/cards/12STOREEZ
      MASTER: "-"
      VISA: "-"



s3:
  serviceEndpoint: http://localhost:4566
  signingRegion: us-east-1
  accessKey: testkey
  secretKey: testsecret
  bucket:
    images: oskelly


notifications:
  setLowerPrice:
    productPublishingTimeBefore: P3M

instorepickup:
  name: самовывоз
  phone: +7 (499) 501-11-62

order-events:
  cron: "-"

kafka:
  enabled: false
keycloak:
  enabled: false

logistics:
  job:
    auto-pickup-runner:
      processing-limit: 10
      retry-max-count: 2
      retry-initial-step-sec: 1
      retry-multiplier: 1
      cron: "*/1 * * * * *"

user-sync-service:
  test-mode:
    enabled: false
    uuids: ${USER_SYNC_ENABLED_TEST_UUIDS:}
  enabled:
    send: false
    consume: false
  jobs:
    retry:
      cron: "-"
      expiration-hours: 24
      batchSizeLimit: 10
  kafka:
    topic: kafka.core.userSync
    user-ban-topic: kafka.core.userBanSync
    consumer:
      group-id: coreRU
      bootstrap-servers: localhost:9092
      topic: kafka.core.userSync
      user-ban-topic: kafka.core.userBanSync
      properties:
        security.protocol: PLAINTEXT   # см. org.apache.kafka.common.security.auth.SecurityProtocol  updaters:
    user-updated-event-sender:
      enabled: false

bonuses-service:
  enabled: true
  programs:
    birthday:
      enabled: true
    welcome:
      enabled: true
  scheduler:
    error-income-transactions-processing:
      cron: "-"
    old-hold-transactions-processing:
      oldness: 3600
      cron: "-"
    birthday-bonuses-transfer:
      cron: "-"

loyalty-cards-service:
  enabled: true

loyalty-service:
  enabled: false
  #убрать при включении сервиса лояльности и возможно добавить тестов в Order и Cart с учетом процента списания меньшего 100
  noop:
    withdraw-percent: 100

